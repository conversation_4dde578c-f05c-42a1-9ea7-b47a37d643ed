package com.example.gymbro.core.network.detector

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

/**
 * 🚀 FeatureMatcher 单元测试
 *
 * 测试目标：
 * - 验证KMP算法模式匹配准确性
 * - 测试多模式匹配功能
 * - 验证协议特征检测
 * - 测试性能和缓存机制
 */
class FeatureMatcherTest {

    private lateinit var featureMatcher: FeatureMatcher

    @BeforeEach
    fun setup() {
        featureMatcher = FeatureMatcher()
    }

    @Test
    fun `应该正确匹配JSON SSE模式`() {
        // Given
        val text = "data: {\"choices\":[{\"delta\":{\"content\":\"Hello\"}}]}".toCharArray()
        val pattern = FeatureMatcher.JSON_SSE_PATTERN

        // When
        val position = featureMatcher.findPattern(text, text.size, pattern)

        // Then
        assertEquals(0, position, "应该在位置0找到JSON SSE模式")
    }

    @Test
    fun `应该正确匹配XML thinking模式`() {
        // Given
        val text = "Some text <thinking>content</thinking> more text".toCharArray()
        val pattern = FeatureMatcher.XML_THINKING_PATTERN

        // When
        val position = featureMatcher.findPattern(text, text.size, pattern)

        // Then
        assertEquals(10, position, "应该在正确位置找到XML thinking模式")
    }

    @Test
    fun `应该正确匹配JSON流模式`() {
        // Given
        val text = "{\"text\": \"Hello World\"}".toCharArray()
        val pattern = FeatureMatcher.JSON_STREAM_PATTERN

        // When
        val position = featureMatcher.findPattern(text, text.size, pattern)

        // Then
        assertEquals(0, position, "应该在位置0找到JSON流模式")
    }

    @Test
    fun `未找到模式时应该返回-1`() {
        // Given
        val text = "This is plain text without any patterns".toCharArray()
        val pattern = FeatureMatcher.JSON_SSE_PATTERN

        // When
        val position = featureMatcher.findPattern(text, text.size, pattern)

        // Then
        assertEquals(-1, position, "未找到模式时应该返回-1")
    }

    @Test
    fun `应该处理空文本和空模式`() {
        // Given
        val emptyText = "".toCharArray()
        val emptyPattern = "".toByteArray()
        val normalPattern = FeatureMatcher.JSON_SSE_PATTERN

        // When & Then
        assertEquals(-1, featureMatcher.findPattern(emptyText, 0, normalPattern))
        assertEquals(-1, featureMatcher.findPattern("test".toCharArray(), 4, emptyPattern))
    }

    @Test
    fun `多模式匹配应该找到所有匹配项`() {
        // Given
        val text = "data: {\"content\": \"test\"} <thinking>process</thinking>".toCharArray()
        val patterns = listOf(
            FeatureMatcher.JSON_SSE_PATTERN,
            FeatureMatcher.XML_THINKING_PATTERN,
            FeatureMatcher.CONTENT_PATTERN,
        )

        // When
        val matches = featureMatcher.findMultiplePatterns(text, text.size, patterns)

        // Then
        assertTrue(matches.size >= 2, "应该找到至少2个匹配项")

        // 验证匹配项按位置排序
        for (i in 1 until matches.size) {
            assertTrue(
                matches[i - 1].position <= matches[i].position,
                "匹配项应该按位置排序",
            )
        }
    }

    @Test
    fun `协议特征检测应该正确识别JSON SSE特征`() {
        // Given
        val text = "data: {\"choices\":[{\"delta\":{\"content\":\"test\"}}]}".toCharArray()

        // When
        val features = featureMatcher.detectProtocolFeatures(text, text.size)

        // Then
        assertTrue(features.hasJsonSse, "应该检测到JSON SSE特征")
        assertTrue(features.hasContentField, "应该检测到content字段")
        assertEquals(ContentType.JSON_SSE, features.getMostLikelyProtocol())
        assertTrue(features.getConfidence() > 0.5f, "置信度应该大于0.5")
    }

    @Test
    fun `协议特征检测应该正确识别XML ThinkingBox特征`() {
        // Given
        val text = "<thinking><segment>test content</segment></thinking>".toCharArray()

        // When
        val features = featureMatcher.detectProtocolFeatures(text, text.size)

        // Then
        assertTrue(features.hasXmlThinking, "应该检测到XML thinking特征")
        assertTrue(features.hasSegmentTag, "应该检测到segment标签")
        assertTrue(features.hasThinkingEndTag, "应该检测到thinking结束标签")
        assertEquals(ContentType.XML_THINKING, features.getMostLikelyProtocol())
        assertTrue(features.getConfidence() > 0.8f, "置信度应该大于0.8")
    }

    @Test
    fun `协议特征检测应该正确识别JSON流特征`() {
        // Given
        val text = "{\"text\": \"Hello\", \"message\": \"World\"}".toCharArray()

        // When
        val features = featureMatcher.detectProtocolFeatures(text, text.size)

        // Then
        assertTrue(features.hasJsonStream, "应该检测到JSON流特征")
        assertTrue(features.hasTextField, "应该检测到text字段")
        assertTrue(features.hasMessageField, "应该检测到message字段")
        assertEquals(ContentType.JSON_STREAM, features.getMostLikelyProtocol())
    }

    @Test
    fun `协议特征检测应该正确识别WebSocket特征`() {
        // Given
        val text = "WS: frame data here".toCharArray()

        // When
        val features = featureMatcher.detectProtocolFeatures(text, text.size)

        // Then
        assertTrue(features.hasWebSocket, "应该检测到WebSocket特征")
        assertEquals(ContentType.WEBSOCKET_FRAME, features.getMostLikelyProtocol())
    }

    @Test
    fun `无特征时应该返回纯文本类型`() {
        // Given
        val text = "This is just plain text without any protocol features".toCharArray()

        // When
        val features = featureMatcher.detectProtocolFeatures(text, text.size)

        // Then
        assertFalse(features.hasJsonSse)
        assertFalse(features.hasXmlThinking)
        assertFalse(features.hasJsonStream)
        assertFalse(features.hasWebSocket)
        assertEquals(ContentType.PLAIN_TEXT, features.getMostLikelyProtocol())
        assertEquals(0f, features.getConfidence())
    }

    @Test
    fun `应该正确处理部分长度的文本`() {
        // Given
        val fullText = "data: {\"content\": \"test\"} extra content".toCharArray()
        val partialLength = 20 // 只检查前20个字符
        val pattern = FeatureMatcher.JSON_SSE_PATTERN

        // When
        val position = featureMatcher.findPattern(fullText, partialLength, pattern)

        // Then
        assertEquals(0, position, "应该在部分文本中找到模式")
    }

    @Test
    fun `KMP算法应该正确处理重复字符模式`() {
        // Given
        val text = "aaaaaab".toCharArray()
        val pattern = "aaab".toByteArray()

        // When
        val position = featureMatcher.findPattern(text, text.size, pattern)

        // Then
        assertEquals(3, position, "KMP算法应该正确处理重复字符")
    }

    @Test
    fun `应该正确处理模式在文本末尾的情况`() {
        // Given
        val text = "some text data: {".toCharArray()
        val pattern = FeatureMatcher.JSON_SSE_PATTERN // "data: {"

        // When
        val position = featureMatcher.findPattern(text, text.size, pattern)

        // Then
        assertEquals(10, position, "应该在文本末尾找到模式")
    }

    @Test
    fun `获取统计信息应该返回正确数据`() {
        // When
        val stats = featureMatcher.getStatistics()

        // Then
        assertTrue(stats.cachedPatternsCount > 0, "应该有缓存的模式")
        assertTrue(stats.totalPatternLength > 0, "总模式长度应该大于0")
    }

    @Test
    fun `清理缓存应该重新初始化`() {
        // Given
        val initialStats = featureMatcher.getStatistics()

        // When
        featureMatcher.clearCache()
        val afterClearStats = featureMatcher.getStatistics()

        // Then
        assertEquals(
            initialStats.cachedPatternsCount,
            afterClearStats.cachedPatternsCount,
            "清理后应该重新初始化相同数量的模式",
        )
    }

    @Test
    fun `PatternMatch应该正确实现equals和hashCode`() {
        // Given
        val pattern1 = "test".toByteArray()
        val pattern2 = "test".toByteArray()
        val pattern3 = "different".toByteArray()

        val match1 = PatternMatch(pattern1, 0)
        val match2 = PatternMatch(pattern2, 0)
        val match3 = PatternMatch(pattern3, 0)
        val match4 = PatternMatch(pattern1, 1)

        // When & Then
        assertEquals(match1, match2, "相同模式和位置应该相等")
        assertNotEquals(match1, match3, "不同模式应该不相等")
        assertNotEquals(match1, match4, "不同位置应该不相等")

        assertEquals(match1.hashCode(), match2.hashCode(), "相等对象应该有相同hashCode")
        assertEquals("test", match1.patternString, "patternString应该正确转换")
    }

    @Test
    fun `ProtocolFeatures置信度计算应该合理`() {
        // Given
        val features = ProtocolFeatures().apply {
            hasJsonSse = true
            hasContentField = true
            hasDeltaField = true
        }

        // When
        val confidence = features.getConfidence()

        // Then
        assertEquals(1.0f, confidence, 0.01f, "完整JSON SSE特征应该有100%置信度")

        // Given - 部分特征
        val partialFeatures = ProtocolFeatures().apply {
            hasJsonSse = true
        }

        // When
        val partialConfidence = partialFeatures.getConfidence()

        // Then
        assertTrue(partialConfidence < confidence, "部分特征应该有较低置信度")
    }
}
