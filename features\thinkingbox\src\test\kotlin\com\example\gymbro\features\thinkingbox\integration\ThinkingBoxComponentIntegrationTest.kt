package com.example.gymbro.features.thinkingbox.integration

import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.domain.parser.XmlStreamScanner
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.reducer.SegmentQueueReducer
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingBoxReducer
import io.mockk.*
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * ThinkingBox完整集成测试套件
 *
 * 🎯 测试目标：验证多组件协作的完整功能
 * 📊 覆盖率目标：≥85%
 * 🔧 测试框架：JUnit 5 + MockK + kotlin.test
 * 🔥 重点：组件间协作、完整数据流、错误传播、状态一致性
 *
 * 测试覆盖：
 * - Parser → Mapper → Reducer 完整数据流
 * - 错误在组件间的正确传播
 * - 状态在组件间的一致性维护
 * - 复杂场景下的组件协作
 * - 四条铁律在集成环境下的遵守
 */
@DisplayName("ThinkingBox完整集成测试")
class ThinkingBoxComponentIntegrationTest {

    private lateinit var xmlScanner: XmlStreamScanner
    private lateinit var parser: StreamingThinkingMLParser
    private lateinit var mapper: DomainMapper
    private lateinit var segmentQueueReducer: SegmentQueueReducer
    private lateinit var thinkingBoxReducer: ThinkingBoxReducer

    @BeforeEach
    fun setUp() {
        xmlScanner = mockk(relaxed = true)
        parser = StreamingThinkingMLParser(xmlScanner)
        mapper = DomainMapper()
        segmentQueueReducer = SegmentQueueReducer()
        thinkingBoxReducer = ThinkingBoxReducer(segmentQueueReducer)
    }

    @Nested
    @DisplayName("完整数据流集成测试")
    inner class CompleteDataFlowIntegrationTests {

        @Test
        @DisplayName("Parser到Reducer完整数据流应该正确工作")
        fun `Parser to Reducer complete data flow should work correctly`() = runTest {
            // Given - 模拟完整的XML数据流
            val messageId = "integration-test-123"
            every { xmlScanner.feed(any()) } returnsMany listOf(
                // 第一步：<think>标签
                listOf(XmlStreamScanner.TagOpen("think", emptyMap())),
                // 第二步：预思考内容
                listOf(XmlStreamScanner.Text("预思考内容")),
                // 第三步：</think>标签
                listOf(XmlStreamScanner.TagClose("think")),
                // 第四步：<thinking>标签
                listOf(XmlStreamScanner.TagOpen("thinking", emptyMap())),
                // 第五步：<phase>标签
                listOf(XmlStreamScanner.TagOpen("phase", mapOf("id" to "analysis"))),
                // 第六步：phase内容
                listOf(XmlStreamScanner.Text("分析阶段内容")),
                // 第七步：</phase>标签
                listOf(XmlStreamScanner.TagClose("phase")),
                // 第八步：</thinking>标签
                listOf(XmlStreamScanner.TagClose("thinking")),
                // 第九步：<final>标签
                listOf(XmlStreamScanner.TagOpen("final", emptyMap())),
                // 第十步：final内容
                listOf(XmlStreamScanner.Text("最终答案内容")),
                // 第十一步：</final>标签
                listOf(XmlStreamScanner.TagClose("final")),
            )

            // 初始化状态
            var tbState = SegmentQueueReducer.TBState(messageId = messageId)
            var mappingContext = DomainMapper.MappingContext()
            val allThinkingEvents = mutableListOf<ThinkingEvent>()
            val allEffects = mutableListOf<ThinkingBoxContract.Effect>()

            // When - 模拟完整的token处理序列
            val tokens = listOf(
                "<think>", "预思考内容", "</think>",
                "<thinking>", "<phase id=\"analysis\">", "分析阶段内容", "</phase>", "</thinking>",
                "<final>", "最终答案内容", "</final>",
            )

            tokens.forEach { token ->
                parser.parseTokenChunk(token, messageId) { semanticEvent ->
                    // Parser → Mapper
                    val mappingResult = mapper.mapSemanticToThinking(semanticEvent, mappingContext)
                    mappingContext = mappingResult.context

                    // Mapper → Reducer
                    mappingResult.events.forEach { thinkingEvent ->
                        allThinkingEvents.add(thinkingEvent)
                        val reduceResult = segmentQueueReducer.reduce(tbState, thinkingEvent)
                        tbState = reduceResult.state
                        allEffects.addAll(reduceResult.effects)
                    }
                }
            }

            // Then - 验证完整数据流的正确性

            // 1. 验证事件序列完整性
            assertTrue(allThinkingEvents.any { it is ThinkingEvent.SegmentStarted && it.id == "perthink" })
            assertTrue(allThinkingEvents.any { it is ThinkingEvent.SegmentText && it.text == "预思考内容" })
            assertTrue(allThinkingEvents.any { it is ThinkingEvent.SegmentClosed && it.id == "perthink" })
            assertTrue(allThinkingEvents.any { it is ThinkingEvent.SegmentStarted && it.id == "analysis" })
            assertTrue(allThinkingEvents.any { it is ThinkingEvent.SegmentText && it.text == "分析阶段内容" })
            assertTrue(allThinkingEvents.any { it is ThinkingEvent.ThinkingClosed })
            assertTrue(allThinkingEvents.any { it is ThinkingEvent.FinalStart })
            assertTrue(allThinkingEvents.any { it is ThinkingEvent.FinalContent && it.text == "最终答案内容" })
            assertTrue(allThinkingEvents.any { it is ThinkingEvent.FinalComplete })

            // 2. 验证最终状态正确性
            assertTrue(tbState.thinkingClosed)
            assertTrue(tbState.finalClosed)
            assertEquals("最终答案内容", tbState.getFinalContent())
            assertEquals(messageId, tbState.messageId)

            // 3. 验证Effect生成
            assertTrue(allEffects.any { it is ThinkingBoxContract.Effect.NotifyHistoryThinking })
            assertTrue(allEffects.any { it is ThinkingBoxContract.Effect.NotifyHistoryFinal })
        }

        @Test
        @DisplayName("ThinkingBoxReducer集成应该正确处理Contract层转换")
        fun `ThinkingBoxReducer integration should handle Contract layer conversion correctly`() = runTest {
            // Given
            val messageId = "contract-integration-test"
            val initialState = ThinkingBoxContract.State()

            // When - 通过ThinkingBoxReducer处理初始化
            val initIntent = ThinkingBoxContract.Intent.Initialize(messageId)
            val initResult = thinkingBoxReducer.reduceInternal(initIntent, initialState)

            // Then - 验证Contract层状态转换
            assertEquals(messageId, initResult.state.messageId)
            assertFalse(initResult.state.isLoading)
            assertTrue(initResult.state.segmentsQueue.isEmpty())

            // 验证Effect生成
            assertEquals(1, initResult.effects.size)
            assertTrue(initResult.effects.first() is ThinkingBoxContract.Effect.StartTokenStreamListening)
        }

        @Test
        @DisplayName("复杂XML结构应该被正确解析和处理")
        fun `complex XML structure should be parsed and processed correctly`() = runTest {
            // Given - 复杂的XML结构
            every { xmlScanner.feed(any()) } returnsMany listOf(
                listOf(XmlStreamScanner.TagOpen("think", emptyMap())),
                listOf(XmlStreamScanner.Text("初始思考")),
                listOf(XmlStreamScanner.TagOpen("thinking", emptyMap())),
                listOf(XmlStreamScanner.TagOpen("phase", mapOf("id" to "p1"))),
                listOf(XmlStreamScanner.Text("第一阶段")),
                listOf(XmlStreamScanner.TagClose("phase")),
                listOf(XmlStreamScanner.TagOpen("phase", mapOf("id" to "p2"))),
                listOf(XmlStreamScanner.Text("第二阶段")),
                listOf(XmlStreamScanner.TagClose("phase")),
                listOf(XmlStreamScanner.TagOpen("phase", emptyMap())), // 无ID的phase
                listOf(XmlStreamScanner.Text("第三阶段")),
                listOf(XmlStreamScanner.TagClose("phase")),
                listOf(XmlStreamScanner.TagClose("thinking")),
                listOf(XmlStreamScanner.TagOpen("final", emptyMap())),
                listOf(XmlStreamScanner.Text("综合答案")),
                listOf(XmlStreamScanner.TagClose("final")),
            )

            // 初始化状态
            var tbState = SegmentQueueReducer.TBState(messageId = "complex-xml-test")
            var mappingContext = DomainMapper.MappingContext()
            val allSegmentEvents = mutableListOf<ThinkingEvent.SegmentStarted>()

            // When - 处理复杂XML序列
            val complexTokens = listOf(
                "<think>", "初始思考", "<thinking>",
                "<phase id=\"p1\">", "第一阶段", "</phase>",
                "<phase id=\"p2\">", "第二阶段", "</phase>",
                "<phase>", "第三阶段", "</phase>",
                "</thinking>", "<final>", "综合答案", "</final>",
            )

            complexTokens.forEach { token ->
                parser.parseTokenChunk(token, "complex-xml-test") { semanticEvent ->
                    val mappingResult = mapper.mapSemanticToThinking(semanticEvent, mappingContext)
                    mappingContext = mappingResult.context

                    mappingResult.events.forEach { thinkingEvent ->
                        if (thinkingEvent is ThinkingEvent.SegmentStarted) {
                            allSegmentEvents.add(thinkingEvent)
                        }
                        val reduceResult = segmentQueueReducer.reduce(tbState, thinkingEvent)
                        tbState = reduceResult.state
                    }
                }
            }

            // Then - 验证复杂结构处理结果

            // 验证所有段都被正确创建
            assertTrue(allSegmentEvents.any { it.id == "perthink" && it.kind == SegmentKind.PERTHINK })
            assertTrue(allSegmentEvents.any { it.id == "p1" && it.kind == SegmentKind.PHASE })
            assertTrue(allSegmentEvents.any { it.id == "p2" && it.kind == SegmentKind.PHASE })
            assertTrue(allSegmentEvents.any { it.id == "phase-1" && it.kind == SegmentKind.PHASE }) // 自动生成ID
            assertTrue(allSegmentEvents.any { it.id == "final-phase" && it.kind == SegmentKind.FINAL_PHASE })

            // 验证phaseIdCounter正确递增
            assertEquals(2, mappingContext.phaseIdCounter.get()) // 应该递增到2

            // 验证最终状态
            assertTrue(tbState.thinkingClosed)
            assertTrue(tbState.finalClosed)
            assertEquals("综合答案", tbState.getFinalContent())
        }
    }

    @Nested
    @DisplayName("错误传播和处理集成测试")
    inner class ErrorPropagationIntegrationTests {

        @Test
        @DisplayName("Parser错误应该正确传播到上层")
        fun `Parser errors should propagate correctly to upper layers`() = runTest {
            // Given - Mock抛出解析异常
            every { xmlScanner.feed("error-token") } throws RuntimeException("XML parsing failed")

            var tbState = SegmentQueueReducer.TBState(messageId = "error-test")
            var mappingContext = DomainMapper.MappingContext()
            val allSemanticEvents = mutableListOf<SemanticEvent>()

            // When - 处理错误token
            parser.parseTokenChunk("error-token", "error-test") { semanticEvent ->
                allSemanticEvents.add(semanticEvent)

                val mappingResult = mapper.mapSemanticToThinking(semanticEvent, mappingContext)
                mappingContext = mappingResult.context

                mappingResult.events.forEach { thinkingEvent ->
                    val reduceResult = segmentQueueReducer.reduce(tbState, thinkingEvent)
                    tbState = reduceResult.state
                }
            }

            // Then - 验证错误事件被正确传播
            assertTrue(allSemanticEvents.any { it is SemanticEvent.ParseErrorEvent })

            val errorEvent = allSemanticEvents.filterIsInstance<SemanticEvent.ParseErrorEvent>().first()
            assertTrue(errorEvent.error.message.contains("XML parsing failed"))
        }

        @Test
        @DisplayName("Mapper验证错误应该被正确处理")
        fun `Mapper validation errors should be handled correctly`() = runTest {
            // Given - 构造会导致Mapper产生ParseError的场景
            every { xmlScanner.feed(any()) } returns listOf(
                XmlStreamScanner.TagOpen("phase", emptyMap()), // 在PRE_THINK状态下使用phase标签
            )

            var tbState = SegmentQueueReducer.TBState(messageId = "mapper-error-test")
            var mappingContext = DomainMapper.MappingContext(
                parseState = DomainMapper.ParsePhase.PRE_THINK, // 故意设置为PRE_THINK状态
            )
            val allThinkingEvents = mutableListOf<ThinkingEvent>()

            // When - 处理会导致验证错误的token
            parser.parseTokenChunk("<phase>", "mapper-error-test") { semanticEvent ->
                val mappingResult = mapper.mapSemanticToThinking(semanticEvent, mappingContext)
                mappingContext = mappingResult.context

                mappingResult.events.forEach { thinkingEvent ->
                    allThinkingEvents.add(thinkingEvent)
                    val reduceResult = segmentQueueReducer.reduce(tbState, thinkingEvent)
                    tbState = reduceResult.state
                }
            }

            // Then - 验证ParseError事件被正确生成和处理
            assertTrue(allThinkingEvents.any { it is ThinkingEvent.ParseError })

            val parseError = allThinkingEvents.filterIsInstance<ThinkingEvent.ParseError>().first()
            assertTrue(parseError.message.contains("Invalid <phase> tag in PRE_THINK state"))
        }

        @Test
        @DisplayName("部分组件失败不应该影响其他组件继续工作")
        fun `partial component failure should not affect other components`() = runTest {
            // Given - 设置部分失败场景
            every { xmlScanner.feed("good-token") } returns listOf(
                XmlStreamScanner.Text("正常内容"),
            )
            every { xmlScanner.feed("bad-token") } throws RuntimeException("解析失败")

            var tbState = SegmentQueueReducer.TBState(messageId = "partial-failure-test")
            var mappingContext = DomainMapper.MappingContext()
            val successfulEvents = mutableListOf<ThinkingEvent>()
            val errorEvents = mutableListOf<SemanticEvent.ParseErrorEvent>()

            // When - 混合处理正常和错误token
            val tokens = listOf("good-token", "bad-token", "good-token")

            tokens.forEach { token ->
                try {
                    parser.parseTokenChunk(token, "partial-failure-test") { semanticEvent ->
                        when (semanticEvent) {
                            is SemanticEvent.ParseErrorEvent -> {
                                errorEvents.add(semanticEvent)
                            }
                            else -> {
                                val mappingResult = mapper.mapSemanticToThinking(
                                    semanticEvent,
                                    mappingContext,
                                )
                                mappingContext = mappingResult.context

                                mappingResult.events.forEach { thinkingEvent ->
                                    successfulEvents.add(thinkingEvent)
                                    val reduceResult = segmentQueueReducer.reduce(tbState, thinkingEvent)
                                    tbState = reduceResult.state
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    // 继续处理下一个token
                }
            }

            // Then - 验证部分成功，部分失败
            assertTrue(successfulEvents.isNotEmpty(), "应该有成功处理的事件")
            assertTrue(errorEvents.isNotEmpty(), "应该有错误事件")

            // 验证成功的部分仍然正常工作
            assertTrue(successfulEvents.any { it is ThinkingEvent.SegmentStarted })
            assertTrue(successfulEvents.any { it is ThinkingEvent.SegmentText })
        }
    }

    @Nested
    @DisplayName("状态一致性集成测试")
    inner class StateConsistencyIntegrationTests {

        @Test
        @DisplayName("多组件状态应该保持一致")
        fun `multi-component state should remain consistent`() = runTest {
            // Given - 设置复杂的状态变化序列
            every { xmlScanner.feed(any()) } returnsMany listOf(
                listOf(XmlStreamScanner.TagOpen("thinking", emptyMap())),
                listOf(XmlStreamScanner.TagOpen("phase", mapOf("id" to "state-test"))),
                listOf(XmlStreamScanner.Text("状态测试内容")),
                listOf(XmlStreamScanner.TagClose("phase")),
                listOf(XmlStreamScanner.TagClose("thinking")),
            )

            // 初始化多个状态跟踪
            var tbState = SegmentQueueReducer.TBState(messageId = "consistency-test")
            var mappingContext = DomainMapper.MappingContext()
            val stateSnapshots = mutableListOf<Pair<DomainMapper.ParsePhase, SegmentQueueReducer.TBState>>()

            // When - 处理状态变化序列
            val tokens =
                listOf("<thinking>", "<phase id=\"state-test\">", "状态测试内容", "</phase>", "</thinking>")

            tokens.forEach { token ->
                parser.parseTokenChunk(token, "consistency-test") { semanticEvent ->
                    val mappingResult = mapper.mapSemanticToThinking(semanticEvent, mappingContext)
                    mappingContext = mappingResult.context

                    mappingResult.events.forEach { thinkingEvent ->
                        val reduceResult = segmentQueueReducer.reduce(tbState, thinkingEvent)
                        tbState = reduceResult.state

                        // 记录状态快照
                        stateSnapshots.add(mappingContext.parseState to tbState.copy())
                    }
                }
            }

            // Then - 验证状态一致性

            // 验证最终状态一致性
            assertEquals(DomainMapper.ParsePhase.FINAL_PHASE, mappingContext.parseState)
            assertTrue(tbState.thinkingClosed)

            // 验证状态变化序列的逻辑一致性
            val parsePhases = stateSnapshots.map { it.first }
            assertTrue(parsePhases.contains(DomainMapper.ParsePhase.PRE_THINK))
            assertTrue(parsePhases.contains(DomainMapper.ParsePhase.FORMAL_PHASE))
            assertTrue(parsePhases.contains(DomainMapper.ParsePhase.FINAL_PHASE))

            // 验证不应该有无效的状态转换
            assertTrue(stateSnapshots.isNotEmpty())
        }

        @Test
        @DisplayName("Context和TBState应该同步更新")
        fun `Context and TBState should update synchronously`() = runTest {
            // Given
            every { xmlScanner.feed(any()) } returnsMany listOf(
                listOf(XmlStreamScanner.TagOpen("think", emptyMap())),
                listOf(XmlStreamScanner.Text("同步测试")),
                listOf(XmlStreamScanner.TagOpen("thinking", emptyMap())),
                listOf(XmlStreamScanner.TagOpen("phase", emptyMap())),
                listOf(XmlStreamScanner.Text("phase内容")),
                listOf(XmlStreamScanner.TagClose("thinking")),
            )

            var tbState = SegmentQueueReducer.TBState(messageId = "sync-test")
            var mappingContext = DomainMapper.MappingContext()
            val syncCheckpoints = mutableListOf<Triple<String?, String?, Boolean>>()

            // When - 记录同步检查点
            val tokens = listOf("<think>", "同步测试", "<thinking>", "<phase>", "phase内容", "</thinking>")

            tokens.forEach { token ->
                parser.parseTokenChunk(token, "sync-test") { semanticEvent ->
                    val mappingResult = mapper.mapSemanticToThinking(semanticEvent, mappingContext)
                    mappingContext = mappingResult.context

                    mappingResult.events.forEach { thinkingEvent ->
                        val reduceResult = segmentQueueReducer.reduce(tbState, thinkingEvent)
                        tbState = reduceResult.state

                        // 记录同步检查点 (MappingContext.currentSegmentId, TBState.current?.id, TBState.thinkingClosed)
                        syncCheckpoints.add(
                            Triple(
                                mappingContext.currentSegmentId,
                                tbState.current?.id,
                                tbState.thinkingClosed,
                            ),
                        )
                    }
                }
            }

            // Then - 验证同步性
            assertTrue(syncCheckpoints.isNotEmpty())

            // 验证最终状态同步
            val finalCheckpoint = syncCheckpoints.last()
            assertNotNull(finalCheckpoint)
            assertTrue(finalCheckpoint.third) // thinkingClosed应该为true
        }
    }

    @Nested
    @DisplayName("性能和资源管理集成测试")
    inner class PerformanceAndResourceManagementIntegrationTests {

        @Test
        @DisplayName("大量数据处理不应该导致性能问题")
        fun `large data processing should not cause performance issues`() = runTest {
            // Given - 大量数据
            val largeDataSize = 100
            every { xmlScanner.feed(any()) } returns (1..largeDataSize).map {
                XmlStreamScanner.Text("content-$it")
            }

            var tbState = SegmentQueueReducer.TBState(messageId = "performance-test")
            var mappingContext = DomainMapper.MappingContext()
            val processedEvents = mutableListOf<ThinkingEvent>()

            // When - 处理大量数据
            val startTime = System.currentTimeMillis()

            repeat(largeDataSize) { index ->
                parser.parseTokenChunk("large-data-$index", "performance-test") { semanticEvent ->
                    val mappingResult = mapper.mapSemanticToThinking(semanticEvent, mappingContext)
                    mappingContext = mappingResult.context

                    mappingResult.events.forEach { thinkingEvent ->
                        processedEvents.add(thinkingEvent)
                        val reduceResult = segmentQueueReducer.reduce(tbState, thinkingEvent)
                        tbState = reduceResult.state
                    }
                }
            }

            val endTime = System.currentTimeMillis()
            val processingTime = endTime - startTime

            // Then - 验证性能要求
            assertTrue(processingTime < 5000, "处理时间${processingTime}ms应该小于5秒")
            assertTrue(processedEvents.size >= largeDataSize, "应该处理所有数据")

            // 验证内存没有异常增长（通过状态大小间接验证）
            assertTrue(tbState.getSummary().length < 1000, "状态摘要不应该过大")
        }

        @Test
        @DisplayName("频繁的状态更新不应该导致内存泄漏")
        fun `frequent state updates should not cause memory leaks`() = runTest {
            // Given - 频繁状态更新场景
            every { xmlScanner.feed(any()) } returnsMany (1..50).map {
                listOf(
                    XmlStreamScanner.TagOpen("phase", mapOf("id" to "freq-$it")),
                    XmlStreamScanner.Text("content-$it"),
                    XmlStreamScanner.TagClose("phase"),
                )
            }.flatten()

            var tbState = SegmentQueueReducer.TBState(messageId = "memory-test")
            var mappingContext = DomainMapper.MappingContext(
                parseState = DomainMapper.ParsePhase.FORMAL_PHASE,
            )
            val stateHistory = mutableListOf<SegmentQueueReducer.TBState>()

            // When - 频繁更新状态
            repeat(50) { index ->
                parser.parseTokenChunk("freq-update-$index", "memory-test") { semanticEvent ->
                    val mappingResult = mapper.mapSemanticToThinking(semanticEvent, mappingContext)
                    mappingContext = mappingResult.context

                    mappingResult.events.forEach { thinkingEvent ->
                        val reduceResult = segmentQueueReducer.reduce(tbState, thinkingEvent)
                        tbState = reduceResult.state
                        stateHistory.add(tbState.copy()) // 保存状态副本用于内存测试
                    }
                }
            }

            // Then - 验证内存使用合理
            assertTrue(stateHistory.size > 0, "应该有状态历史记录")

            // 验证最终状态不包含过多累积数据
            assertTrue(tbState.queue.size < 100, "队列大小不应该无限增长")
            assertTrue(tbState.getTotalSegmentCount() < 100, "总段数不应该无限增长")
        }
    }
}