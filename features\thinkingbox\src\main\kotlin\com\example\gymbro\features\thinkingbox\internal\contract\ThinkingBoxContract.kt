package com.example.gymbro.features.thinkingbox.internal.contract

import androidx.compose.runtime.Immutable
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind

/**
 * ThinkingBox MVI Contract - Segment队列架构版本（729方案3.md）
 * 定义基于Segment队列模型的Intent、State、Effect契约
 */
object ThinkingBoxContract {

    /**
     * UI状态 - P0核心架构重构版本（四条铁律）
     *
     * 🔥 【单一画布】以segmentsQueue为核心的LazyColumn增量绘制
     * 🎯 【四条铁律】UI绝对不重组刷新，打字机效果，1/3屏高，8行截断
     */
    @Immutable
    data class State(
        // 🔥 【核心状态】基础字段
        val messageId: String = "",
        val conversationId: String = "", // 🔥 【多轮对话】会话ID支持
        val segmentsQueue: List<SegmentUi> = emptyList(), // LazyColumn渲染的段队列

        // 🔥 【Final相关】最终内容状态
        val finalReady: Boolean = false, // finalBuffer不为空 && 思考框已关闭
        val finalContent: String = "", // 最终富文本内容

        // 🔥 【流式状态】简化的流式控制
        val thinkingClosed: Boolean = false, // 思考阶段是否结束
        // 注意：streaming状态改为Flow-derived，不再在State中维护

        // 🔥 【基础状态】错误和加载状态
        val error: UiText? = null,
        val isLoading: Boolean = false,
    ) : UiState {

        /**
         * 🔥 【四条铁律】AIThinkingCard可见性 - 直接判断队列非空
         */
        val shouldShowAIThinkingCard: Boolean
            get() = segmentsQueue.isNotEmpty() || (!thinkingClosed && isStreaming())

        /**
         * 计算是否应该显示最终内容
         */
        val shouldShowFinalContent: Boolean
            get() = finalReady && finalContent.isNotEmpty()

        /**
         * 🔥 【四条铁律】Flow-derived streaming状态计算
         */
        fun isStreaming(): Boolean {
            return !thinkingClosed
        }
    }

    /**
     * Segment UI表示 - P0核心架构重构版本（四条铁律）
     * 🔥 【独立渲染】每个SegmentUi管理自己的渲染生命周期
     */
    @Immutable
    data class SegmentUi(
        val id: String,
        val kind: SegmentKind, // 🔥 【类型安全】使用SegmentKind枚举，支持FINAL_PHASE
        val title: String?,
        val content: String,
        val isComplete: Boolean, // 段是否已完成（闭合）
        val isRendered: Boolean = false, // 🔥 【新增】UI渲染状态标志
    )

    /**
     * 用户意图 - Segment队列架构版本（729方案3.md）
     */
    sealed interface Intent : AppIntent {
        // 🔥 【核心生命周期】启动和初始化
        data class Initialize(
            val messageId: String,
            val conversationId: String = "" // 🔥 【多轮对话】会话ID参数，默认为空保持向后兼容
        ) : Intent
        data object Reset : Intent

        // 🔥 【Segment队列】UI渲染完成回调
        data class UiSegmentRendered(val segmentId: String) : Intent

        // 🔥 【错误处理】基础错误处理
        data object ClearError : Intent
    }

    /**
     * 副作用 - Segment队列架构版本（729方案3.md）
     */
    sealed interface Effect : UiEffect {
        // 🔥 【UI控制】基础UI效果
        data object ScrollToBottom : Effect
        data object CloseThinkingBox : Effect

        // 🔥 【History写入】方案B的DomainEvent - 支持多轮对话
        data class NotifyHistoryThinking(
            val messageId: String,
            val conversationId: String, // 🔥 【多轮对话】会话ID
            val thinkingMarkdown: String,
            val debounceMs: Long = 100L, // 🔥 【729方案9优化】100ms debounce，保证UI先行
        ) : Effect

        data class NotifyHistoryFinal(
            val messageId: String,
            val conversationId: String, // 🔥 【多轮对话】会话ID
            val finalMarkdown: String,
        ) : Effect

        // 🔥 【Token流】启动Token流监听
        data class StartTokenStreamListening(val messageId: String) : Effect

        // 🔥 【错误处理】基础错误显示
        data class ShowError(val error: UiText) : Effect

        // 🔥 【调试日志】开发调试
        data class LogDebug(val message: String) : Effect
    }
}
