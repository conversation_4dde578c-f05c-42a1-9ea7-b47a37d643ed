package com.example.gymbro.features.thinkingbox.api

import com.example.gymbro.core.error.types.ModernResult

/**
 * ThinkingBoxLauncher - ThinkingBox启动器接口
 *
 * 🎯 职责分离架构设计：
 * - 负责启动和管理ThinkingBox的完整生命周期
 * - Coach通过此接口委托AI处理给ThinkingBox模块
 * - 遵循职责分离原则：Coach专注对话管理，ThinkingBox专注思考显示
 * - 协调Display和core-network模块的交互
 *
 * 🔥 核心功能：
 * - 启动AI思考过程处理
 * - 管理思考会话的生命周期
 * - 提供处理状态查询能力
 * - 支持优雅的取消和清理机制
 *
 * 架构原则：
 * - 异步处理：所有操作都是非阻塞的
 * - 错误恢复：完整的错误处理和恢复机制
 * - 资源管理：proper资源清理和生命周期管理
 * - 状态透明：提供清晰的处理状态信息
 *
 * @since Coach-ThinkingBox重构v2.0
 */
interface ThinkingBoxLauncher {

    /**
     * 启动AI思考过程
     *
     * 这是Coach模块启动AI处理的主要入口点。
     *
     * @param messageId 消息唯一标识符，用于关联整个处理会话
     * @param userPrompt 用户输入的原始提示内容
     * @param completionListener 完成回调监听器，用于接收处理结果
     * @return 启动操作结果，成功时表示启动完成，失败时包含错误信息
     *
     * 行为说明：
     * - 初始化新的思考会话
     * - 启动token流监听和解析
     * - 注册完成回调监听器
     * - 开始实时AI思考过程显示
     *
     * 实现细节：
     * - 如果messageId已存在活跃会话，先清理旧会话再启动新会话
     * - 启动过程是异步的，此方法返回表示启动请求已接受
     * - 实际的AI处理结果通过completionListener异步回调
     * - 支持优雅的UI渲染：追加渲染而非无限重组刷新
     *
     * 错误处理：
     * - 网络连接失败将通过completionListener回调
     * - 参数验证失败将同步返回错误结果
     * - 系统资源不足将通过completionListener回调
     */
    suspend fun startThinking(
        messageId: String,
        userPrompt: String,
        completionListener: ThinkingBoxCompletionListener,
    ): ModernResult<Unit>

    /**
     * 启动AI处理（使用完整请求对象）
     *
     * 使用ThinkingBoxRequest对象启动AI处理，支持完整的上下文信息传递。
     * 这是推荐的启动方式，提供更丰富的上下文支持。
     *
     * @param request 完整的思考处理请求，包含所有必要的上下文信息
     * @param completionListener 完成回调监听器，用于接收处理结果
     * @return 启动操作结果，成功时表示启动完成，失败时包含错误信息
     *
     * 行为说明：
     * - 解析请求中的所有上下文信息
     * - 初始化个性化的思考会话
     * - 启动token流监听和解析
     * - 注册完成回调监听器
     *
     * 实现细节：
     * - 验证请求对象的完整性和有效性
     * - 根据上下文信息配置AI处理参数
     * - 支持用户资料、对话历史、模板等上下文
     * - 提供更精准的个性化AI响应
     *
     * 错误处理：
     * - 请求验证失败将同步返回错误结果
     * - 网络和处理错误通过completionListener回调
     */
    suspend fun startAiProcessing(
        request: ThinkingBoxRequest,
        completionListener: ThinkingBoxCompletionListener,
    ): ModernResult<Unit>

    /**
     * 取消AI思考过程
     *
     * 优雅地停止正在进行的思考处理。
     *
     * @param messageId 要取消的消息标识符
     * @return 取消操作结果
     *
     * 行为说明：
     * - 停止token流监听和解析
     * - 清理相关UI状态和资源
     * - 取消未完成的网络请求
     * - 不会触发completionListener回调（主动取消）
     *
     * 实现细节：
     * - 如果messageId不存在或已完成，返回成功结果
     * - 取消操作是幂等的，多次调用不会产生副作用
     * - 确保所有相关协程和资源得到正确清理
     *
     * 应用场景：
     * - 用户主动取消思考过程
     * - 页面导航时清理资源
     * - 系统内存压力下的资源释放
     */
    suspend fun cancelThinking(messageId: String): ModernResult<Unit>

    /**
     * 获取思考处理状态
     *
     * 查询指定消息的当前处理状态。
     *
     * @param messageId 消息标识符
     * @return 处理状态信息
     *
     * 状态类型：
     * - Idle: 未开始或已完成
     * - Processing: 正在处理中
     * - Completed: 处理完成
     * - Failed: 处理失败
     *
     * 用途：
     * - UI状态同步和显示
     * - 调试和诊断
     * - 重复提交防护
     */
    suspend fun getThinkingStatus(messageId: String): ModernResult<ThinkingBoxStatus>

    /**
     * 获取所有活跃的思考会话
     *
     * 返回当前正在处理的所有消息ID列表。
     *
     * @return 活跃会话的消息ID列表
     *
     * 用途：
     * - 资源监控和管理
     * - 系统诊断和调试
     * - 批量清理操作
     */
    suspend fun getActiveThinkingSessions(): ModernResult<List<String>>

    /**
     * 清理所有活跃的思考会话
     *
     * 停止所有正在进行的思考处理并清理资源。
     *
     * @return 清理操作结果
     *
     * 行为说明：
     * - 取消所有正在进行的思考会话
     * - 清理所有相关资源和状态
     * - 不触发任何completionListener回调
     *
     * 应用场景：
     * - 应用退出时的全局清理
     * - 内存压力下的批量资源释放
     * - 系统重置操作
     */
    suspend fun clearAllThinkingSessions(): ModernResult<Unit>
}

/**
 * ThinkingBoxStatus - 思考处理状态
 *
 * 描述AI思考过程的当前状态。
 */
sealed class ThinkingBoxStatus {
    /**
     * 空闲状态：未开始处理或已完成处理
     */
    object Idle : ThinkingBoxStatus()

    /**
     * 处理中状态：正在进行AI思考和显示
     *
     * @param progress 可选的进度信息
     */
    data class Processing(
        val progress: ThinkingProgress? = null,
    ) : ThinkingBoxStatus()

    /**
     * 完成状态：思考过程已成功完成
     *
     * @param completedAt 完成时间戳（毫秒）
     * @param duration 处理总时长（毫秒）
     */
    data class Completed(
        val completedAt: Long,
        val duration: Long,
    ) : ThinkingBoxStatus()

    /**
     * 失败状态：思考过程失败或中断
     *
     * @param error 错误信息
     * @param failedAt 失败时间戳（毫秒）
     * @param hasPartialResult 是否有部分结果可用
     */
    data class Failed(
        val error: ThinkingBoxError,
        val failedAt: Long,
        val hasPartialResult: Boolean = false,
    ) : ThinkingBoxStatus()
}

/**
 * ThinkingBoxError - 思考处理错误类型
 *
 * 定义各种可能的错误情况和原因。
 */
sealed class ThinkingBoxError(val message: String, val code: String) {

    /**
     * AI请求失败
     */
    data class AiRequestFailed(
        val reason: String,
        val httpCode: Int? = null,
    ) : ThinkingBoxError("AI请求失败: $reason", "AI_REQUEST_FAILED")

    /**
     * 网络连接错误
     */
    data class NetworkError(
        val reason: String,
        val isRetryable: Boolean = true,
    ) : ThinkingBoxError("网络错误: $reason", "NETWORK_ERROR")

    /**
     * 处理超时错误
     */
    data class ProcessingTimeout(
        val timeoutMs: Long,
        val stage: String = "unknown",
    ) : ThinkingBoxError("处理超时: ${timeoutMs}ms (阶段: $stage)", "PROCESSING_TIMEOUT")

    /**
     * 无效请求错误
     */
    data class InvalidRequest(
        val reason: String,
        val field: String? = null,
    ) : ThinkingBoxError("无效请求: $reason", "INVALID_REQUEST")

    /**
     * 解析错误
     */
    data class ParseError(
        val reason: String,
        val position: Int? = null,
    ) : ThinkingBoxError("解析错误: $reason", "PARSE_ERROR")

    /**
     * 系统资源不足
     */
    data class ResourceExhausted(
        val resource: String,
        val currentUsage: String? = null,
    ) : ThinkingBoxError("资源不足: $resource", "RESOURCE_EXHAUSTED")

    /**
     * 未知系统错误
     */
    data class UnknownError(
        val reason: String,
        val originalException: String? = null,
    ) : ThinkingBoxError("未知错误: $reason", "UNKNOWN_ERROR")
}
