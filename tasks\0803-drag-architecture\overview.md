# 拖拽架构统一任务概述

## 任务背景

用户发现MVI架构的internal关键部分被忽略，存在多个拖拽实现冲突：

1. **架构冲突问题**: template edit的drag和workoutexercisecomponent内部实现的drag存在冲突
2. **设想目标**: workoutexercisecomponent组件实现自己的拖动功能，template edit不需要再实现drag
3. **理想状态**: 单纯引入workoutexercisecomponent组件就能实现拖动排序功能

## 核心问题分析

### 发现的拖拽实现冲突

1. **shared目录统一组件** (正确架构)：
   ```
   features/workout/shared/components/drag/
   ├── DragAnimations.kt
   ├── DragModifiers.kt  
   ├── DragState.kt
   └── UnifiedDragHandler.kt
   ```

2. **template edit重复实现** (架构冗余)：
   ```
   template/edit/internal/components/drag/
   ├── DragPerformanceTest.kt
   ├── UnifiedDragHandler.kt (重复!)
   ├── UnifiedDragHandlerImpl.kt
   └── UnifiedDragHandlerImplTest.kt
   ```

3. **旧版拖拽处理** (待清理)：
   ```
   template/edit/utils/DragDropHandler.kt
   template/utils/TemplateScreenDragHandler.kt
   ```

## 需要分析的关键文件列表

### Tier 1: shared目录统一组件
```
features/workout/shared/components/drag/DragAnimations.kt
features/workout/shared/components/drag/DragModifiers.kt
features/workout/shared/components/drag/DragState.kt
features/workout/shared/components/drag/UnifiedDragHandler.kt
```

### Tier 2: template目录拖拽相关文件
```
features/workout/template/edit/internal/components/drag/DragPerformanceTest.kt
features/workout/template/edit/internal/components/drag/UnifiedDragHandler.kt
features/workout/template/edit/internal/components/drag/UnifiedDragHandlerImpl.kt
features/workout/template/edit/internal/components/drag/UnifiedDragHandlerImplTest.kt
features/workout/template/edit/internal/components/DragAnimationIntegration.kt
features/workout/template/edit/internal/components/DraggableExerciseCard.kt
features/workout/template/edit/utils/DragDropHandler.kt
features/workout/template/utils/TemplateScreenDragHandler.kt
```

### Tier 3: workoutexercisecomponent相关
```
features/workout/template/edit/internal/components/WorkoutExerciseComponent.kt
features/workout/template/edit/internal/components/TemplateEditComponents.kt
```

### Tier 4: plan edit拖拽系统 (参考)
```
features/workout/plan/edit/internal/components/canvas/animation/M3DragAnimationSystem.kt
features/workout/plan/edit/internal/components/canvas/components/UnifiedDraggableItem.kt
features/workout/plan/edit/internal/components/canvas/coordinator/UnifiedDragCoordinator.kt
features/workout/plan/edit/internal/components/DraggableTemplateCard.kt
```

## 任务目标

1. **架构统一**: 消除重复拖拽实现，建立单一责任的拖拽组件架构
2. **组件自治**: workoutexercisecomponent实现完整的内部拖拽功能
3. **简化集成**: template edit只需引入组件即可获得拖拽能力
4. **性能优化**: 避免多层拖拽逻辑造成的性能问题

## 预期成果

- 删除template edit内部的冗余拖拽实现
- 强化shared组件的通用性和复用性
- workoutexercisecomponent成为拖拽功能的完整载体
- 建立清晰的MVI internal架构边界

## 风险评估

- **低风险**: 删除重复实现
- **中风险**: 组件职责重新划分
- **高风险**: workoutexercisecomponent内部拖拽逻辑改造

## 下一步

等待Secondary Agent分析所有相关文件的接口和函数结构，形成完整的架构现状报告。