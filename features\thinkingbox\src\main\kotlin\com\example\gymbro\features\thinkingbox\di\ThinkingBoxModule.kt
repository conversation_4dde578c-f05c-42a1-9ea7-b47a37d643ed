package com.example.gymbro.features.thinkingbox.di

import com.example.gymbro.data.thinkingbox.repository.HistoryRepositoryImpl
import com.example.gymbro.domain.thinkingbox.repository.HistoryRepository
// 🚀 【730task集成】新架构组件导入
// 🔥 【架构简化】移除ThinkingBoxAdapter依赖，直接使用DirectOutputChannel
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.internal.adapter.ThinkingBoxStreamAdapter
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import timber.log.Timber
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * ThinkingBoxModule - Hilt 依赖注入模块
 *
 * 提供ThinkingBox模块所需的基础依赖
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class ThinkingBoxModule {

    /**
     * 绑定HistoryRepository接口到实现类
     */
    @Binds
    @Singleton
    abstract fun bindHistoryRepository(impl: HistoryRepositoryImpl): HistoryRepository

    /**
     * 🔥 Coach-ThinkingBox重构：绑定ThinkingBoxDisplay接口到实现类
     */
    @Binds
    @Singleton
    abstract fun bindThinkingBoxDisplay(
        impl: com.example.gymbro.features.thinkingbox.internal.display.ThinkingBoxDisplayImpl,
    ): com.example.gymbro.features.thinkingbox.api.ThinkingBoxDisplay

    /**
     * 🔥 【V2系统提升】DomainMapperFactory已移除，V2系统不需要工厂模式
     */

    companion object {
        // 注意：StreamingThinkingMLParser, ThinkingPhaseExtractor, EventConverter,
        // OptimizedEventConverter, ThinkingMLGuardrail, ProgressiveRenderer 都已经有 @Inject 构造函数，
        // 不需要 @Provides 方法，Hilt 会自动处理依赖注入

        /**
         * 提供 ThinkingBox 专用的 CoroutineScope
         */
        @Provides
        @Singleton
        @ThinkingBoxScope
        fun provideThinkingBoxCoroutineScope(): CoroutineScope = CoroutineScope(
            SupervisorJob() + Dispatchers.Default,
        )

        // 🔥 【日志统一】移除重复的RawThinkingLogger，使用ThinkingBoxLogTree统一管理

        // TokenizerService 现在由 CoreMlBindsModule 提供 OpenAiTokenizer 实现
        // 移除重复绑定以避免 Hilt DuplicateBindings 错误

        // 🔥 【调试工具】TokenFlowFixValidation 使用 @Inject 构造函数，无需手动提供

        /**
         * 🔥 【架构优化】提供ThinkingBoxStreamAdapter - 单一订阅点架构
         *
         * 优化后依赖注入说明：
         * - DirectOutputChannel: 来自CoreNetworkModule，唯一数据源
         * - StreamingThinkingMLParser: 通过@Inject构造函数自动注入，负责语义解析
         * - DomainMapper: 通过@Inject构造函数自动注入，负责事件映射
         * - ThinkingBoxViewModelProvider: 用于直接调用ViewModel方法，避免回调复杂性
         */
        @Provides
        @Singleton
        fun provideThinkingBoxStreamAdapter(
            directOutputChannel: com.example.gymbro.core.network.output.DirectOutputChannel,
            streamingParser: StreamingThinkingMLParser,
            domainMapper: DomainMapper,
            viewModelProvider: ThinkingBoxViewModelProvider,
        ): ThinkingBoxStreamAdapter {
            Timber.d("TB-DI: 🔧 [架构优化] 创建ThinkingBoxStreamAdapter - 单一订阅点架构")
            return ThinkingBoxStreamAdapter(
                directOutputChannel,
                streamingParser,
                domainMapper,
                viewModelProvider,
            )
        }
    }
}

/**
 * ThinkingBox 模块专用的 CoroutineScope 限定符
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ThinkingBoxScope
