package com.example.gymbro.core.ai.prompt.builder

import com.example.gymbro.core.ai.prompt.memory.MemoryContext
import com.example.gymbro.core.ai.prompt.memory.MemoryContextBuilder
import com.example.gymbro.core.ai.prompt.memory.MemoryContextType
import com.example.gymbro.core.ai.prompt.memory.MemoryIntegrator
import com.example.gymbro.core.ai.prompt.model.AiContextData
import com.example.gymbro.core.ai.prompt.registry.PromptRegistry
import com.example.gymbro.core.ai.prompt.structure.SystemLayer
import com.example.gymbro.core.ai.tokenizer.ModelTypes
import com.example.gymbro.core.ai.tokenizer.TokenizerService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.serialization.Serializable
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Core层的ChatMessage定义
 * 仅存于Core层，避免与UI/网络层耦合
 */
@Serializable
data class CoreChatMessage(
    val role: String,
    val content: String,
)

/**
 * 分层Prompt构建器 - 统一智能构建器
 *
 * 负责将各种数据源整合成标准的ChatMessage列表：
 * - System: 系统提示词（支持4种模式切换）
 * - Tool: Memory记忆、用户档案、训练数据等
 * - Conversation: 对话历史
 * - User: 用户当前输入
 *
 * 🔥 修复：遵循Clean Architecture，通过参数传入userId而不是内部获取
 * 🆕 支持 DeepSeek 模型专用提示词
 *
 * @since 618重构 - 完全重写，集成Memory系统
 */
@Singleton
class LayeredPromptBuilder
@Inject
constructor(
    private val tokenizer: TokenizerService,
    private val promptRegistry: PromptRegistry,
    private val memoryIntegrator: MemoryIntegrator,
    private val memoryContextBuilder: MemoryContextBuilder,
) : PromptBuilder {
    companion object {
        private const val DEFAULT_TOKEN_BUDGET = 3000
        private const val MEMORY_TOKEN_BUDGET = 1000
        private const val FALLBACK_USER_ID = "anonymous_user" // 🔥 fallback用户ID
        private const val PROMPT_CHECK_INTERVAL_MS = 30_000L // 🔥 新增：30秒检查一次
        private const val MIN_VALID_PROMPT_LENGTH = 500 // 🔥 新增：最小有效提示词长度

        // 调试日志控制（可通过环境变量或其他方式控制）
        private const val ENABLE_DEBUG_LOGGING = true
    }

    // 🔥 新增：提示词变更检测机制
    private var lastPromptCheckTime = 0L
    private var lastPromptHash = 0
    private var consecutiveFailures = 0

    /**
     * 实现PromptBuilder接口 - 标准方法
     */
    override suspend fun buildChatMessages(
        systemLayer: SystemLayer?,
        userInput: String,
        history: List<ConversationTurn>,
        model: String?, // 🆕 新增模型参数
    ): List<CoreChatMessage> {
        // 接口方法调用日志
        if (ENABLE_DEBUG_LOGGING) {
            Timber.tag("PROMPT-BUILDER").d("LayeredPromptBuilder.buildChatMessages called with model: $model")
        }

        return buildChatMessages(
            systemLayer = systemLayer,
            userInput = userInput,
            history = history,
            userId = null,
            forceOmitSystemPrompt = false,
            model = model, // 🆕 传递模型参数
        )
    }

    /**
     * 构建ChatMessage列表 - 增强版本，支持更多参数
     *
     * @param systemLayer 系统层配置（可选，null时使用当前模式）
     * @param userInput 用户输入
     * @param history 对话历史
     * @param userId 用户ID（由调用者提供）
     * @param forceOmitSystemPrompt 强制忽略系统提示词
     * @param model 模型名称（用于统一提示词系统）🆕
     * @return ChatMessage列表
     *
     * @since 618重构
     */
    suspend fun buildChatMessages(
        systemLayer: SystemLayer?,
        userInput: String,
        history: List<ConversationTurn>,
        userId: String? = null,
        forceOmitSystemPrompt: Boolean = false,
        model: String? = null, // 🆕 新增模型参数
    ): List<CoreChatMessage> {
        // 重载方法调用日志
        if (ENABLE_DEBUG_LOGGING) {
            Timber.tag(
                "PROMPT-BUILDER",
            ).d("buildChatMessages: userId=$userId, forceOmit=$forceOmitSystemPrompt, model=$model")
        }

        // 使用传入的userId或fallback
        val effectiveUserId = userId.takeIf { !it.isNullOrBlank() } ?: FALLBACK_USER_ID

        // 构建Memory上下文
        val memoryContext =
            memoryContextBuilder.buildContext(
                userId = effectiveUserId,
                userInput = userInput,
                history = history,
            )

        // 委托给增强版方法
        return buildChatMessagesWithMemory(
            systemLayer = systemLayer,
            userInput = userInput,
            history = history,
            memoryContext = memoryContext,
            forceOmitSystemPrompt = forceOmitSystemPrompt,
            model = model, // 🆕 传递模型参数
        )
    }

    /**
     * 构建带Memory集成的ChatMessage列表
     *
     * @param systemLayer 系统层配置
     * @param userInput 用户输入
     * @param history 对话历史
     * @param memoryContext Memory上下文
     * @param tokenBudget 总token预算
     * @param forceOmitSystemPrompt 强制忽略系统提示词
     * @param model 模型名称（用于统一提示词系统）🆕
     * @return ChatMessage列表
     *
     * @since 618重构
     */
    suspend fun buildChatMessagesWithMemory(
        systemLayer: SystemLayer? = null,
        userInput: String,
        history: List<ConversationTurn>,
        memoryContext: MemoryContext? = null,
        tokenBudget: Int = DEFAULT_TOKEN_BUDGET,
        forceOmitSystemPrompt: Boolean = false,
        model: String? = null, // 🆕 新增模型参数
    ): List<CoreChatMessage> =
        buildList {
            if (ENABLE_DEBUG_LOGGING) {
                Timber.tag(
                    "PROMPT-BUILDER",
                ).d("Building messages: Memory=${memoryContext != null}, Model=${model ?: "default"}")
            }

            // PromptRegistry状态检查
            if (ENABLE_DEBUG_LOGGING) {
                val currentMode = promptRegistry.getCurrentMode()
                val registryStatus = promptRegistry.getLoadedConfigsStatus()
                Timber.tag("PROMPT-BUILDER").d("Registry - Mode: $currentMode, Status: $registryStatus")
            }

            // 1. System消息 - 根据forceOmitSystemPrompt条件性添加
            if (!forceOmitSystemPrompt) {
                // 🔥 新增：主动检测提示词变更
                checkAndRefreshPromptIfNeeded()

                // 获取系统提示词
                if (ENABLE_DEBUG_LOGGING) {
                    Timber.tag("PROMPT-BUILDER").d("Getting system prompt...")
                    promptRegistry.clearAllCaches()
                }

                val systemPrompt = promptRegistry.getSystemPrompt()

                if (ENABLE_DEBUG_LOGGING) {
                    Timber.tag("PROMPT-BUILDER").d("System prompt length: ${systemPrompt.length}")
                }

                // 仅在LayeredPromptBuilder作为最终展示时输出摘要
                logSystemPromptSummary(systemPrompt)

                // 🔥 新增：如果检测到可能的问题，执行全面诊断和修复
                if (systemPrompt.length < 500) { // 正常的standard.json应该有几千字符
                    Timber
                        .tag("PROMPT-BUILDER")
                        .w("⚠️ 系统提示词异常短(${systemPrompt.length}字符)，可能使用了fallback配置")

                    // 第一步：检查版本一致性并更新
                    Timber.i("🔄 第一步：检查并更新配置文件版本...")
                    try {
                        val updateResult = promptRegistry.checkAndUpdateAllConfigs()
                        Timber.i("🔄 版本检查结果:\n$updateResult")

                        // 检查是否有更新
                        if (updateResult.contains("更新文件数: 0")) {
                            Timber.d("📄 配置文件版本已是最新")
                        } else {
                            Timber.i("📄 有配置文件被更新，重新获取系统提示词")
                            val updatedSystemPrompt = promptRegistry.getSystemPrompt()
                            Timber.i("📄 更新后系统提示词长度: ${updatedSystemPrompt.length}字符")

                            if (updatedSystemPrompt.length > systemPrompt.length && updatedSystemPrompt.length > 500) {
                                Timber.i("✅ 版本更新成功，使用最新的系统提示词")
                                add(CoreChatMessage("system", updatedSystemPrompt))
                                return@buildList
                            }
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "❌ 版本检查失败: ${e.message}")
                    }

                    // 第二步：尝试强制重新加载
                    Timber.i("🔄 第二步：尝试强制重新加载standard配置...")
                    try {
                        val reloadedConfig = promptRegistry.reloadConfig("standard")
                        val newSystemPrompt = reloadedConfig.systemPrompt
                        Timber.i("📄 重新加载后长度: ${newSystemPrompt.length}字符")
                        if (newSystemPrompt.length > systemPrompt.length && newSystemPrompt.length > 500) {
                            Timber.i("✅ 重新加载成功，使用新的系统提示词")
                            add(CoreChatMessage("system", newSystemPrompt))
                            return@buildList
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "❌ 重新加载失败: ${e.message}")
                    }

                    // 第三步：强制同步assets文件
                    Timber.w("🔄 第三步：强制同步assets文件...")
                    try {
                        val syncResult = promptRegistry.forceSyncAllAssets()
                        Timber.i("🔄 同步结果:\n$syncResult")

                        // 重新获取系统提示词
                        val finalSystemPrompt = promptRegistry.getSystemPrompt()
                        Timber.i("📄 同步后系统提示词长度: ${finalSystemPrompt.length}字符")

                        if (finalSystemPrompt.length > 500) {
                            Timber.i("✅ 同步成功，使用修复后的系统提示词")
                            add(CoreChatMessage("system", finalSystemPrompt))
                            return@buildList
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "❌ 强制同步失败: ${e.message}")
                    }

                    Timber.w("⚠️ 所有自动修复尝试均未成功，继续使用当前提示词")
                }

                // 验证系统提示词
                validateSystemPrompt(systemPrompt)

                add(CoreChatMessage("system", systemPrompt))
            }

            // 2. Memory消息 - 集成4层记忆系统
            if (memoryContext != null) {
                val memoryMessages =
                    memoryIntegrator.buildMemoryMessages(
                        context = memoryContext,
                        tokenBudget = MEMORY_TOKEN_BUDGET,
                    )
                addAll(memoryMessages)
                if (ENABLE_DEBUG_LOGGING) {
                    Timber.tag("LayeredPromptBuilder").d("Added ${memoryMessages.size} memory messages")
                }
            }

            // 3. 对话历史
            history.forEach { turn ->
                add(CoreChatMessage("user", turn.user))
                add(CoreChatMessage("assistant", turn.assistant))
            }

            // 4. 用户输入
            add(CoreChatMessage("user", userInput))

            // 5. 验证消息结构
            if (!forceOmitSystemPrompt) {
                validateMessages(this)
            }

            if (ENABLE_DEBUG_LOGGING) {
                Timber.tag("LayeredPromptBuilder").d("Messages built successfully: ${this.size} total")

                // 仅输出消息摘要，作为LayeredPromptBuilder的最终展示
                this.forEachIndexed { index, message ->
                    val preview = message.content.take(50).replace("\n", " ")
                    Timber.tag("LayeredPromptBuilder").d("[$index] ${message.role}: $preview...")
                }
            }

            // LayeredPromptBuilder作为最终展示的汇总日志
            logFinalPromptSummary(this)
        }

    /**
     * 从AiContextData构建消息（兼容旧接口）
     *
     * @param userId 用户ID（由调用者提供）
     * @since 618重构
     */
    suspend fun buildMessagesFromContext(
        ctx: AiContextData,
        userInput: String,
        userId: String? = null,
        tokenBudget: Int = DEFAULT_TOKEN_BUDGET,
    ): List<CoreChatMessage> {
        // 使用传入的userId
        val effectiveUserId = userId.takeIf { !it.isNullOrBlank() } ?: FALLBACK_USER_ID

        // 从AiContextData提取Memory上下文
        val memoryContext = extractMemoryContext(ctx, userInput, effectiveUserId)

        // 转换对话历史格式
        val history =
            ctx.recentHistory
                .map { message ->
                    // 简化处理：将连续的消息配对为对话轮次
                    ConversationTurn(
                        user = message.content,
                        assistant = "已处理", // 临时占位符，实际应该从历史中提取
                    )
                }.take(5) // 限制历史数量

        return buildChatMessagesWithMemory(
            userInput = userInput,
            history = history,
            memoryContext = memoryContext,
            tokenBudget = tokenBudget,
        )
    }

    /**
     * 估算Token数量
     */
    override fun estimateTokens(prompt: String): Int = tokenizer.countTokens(prompt, ModelTypes.GPT_4)

    /**
     * 获取当前提示词模式
     *
     * @since 618重构
     */
    fun getCurrentMode(): String = promptRegistry.currentId.value

    /**
     * 切换提示词模式
     *
     * @since 618重构
     */
    fun switchMode(mode: String) {
        promptRegistry.switch(mode)
        if (ENABLE_DEBUG_LOGGING) {
            Timber.tag("LayeredPromptBuilder").d("Switched to mode: $mode")
        }
    }

    // =============== 私有辅助方法 ===============

    /**
     * 仅在DEBUG模式下输出LayeredPromptBuilder的最终prompt信息
     */
    private fun logFinalPromptSummary(messages: List<CoreChatMessage>) {
        if (ENABLE_DEBUG_LOGGING) {
            val totalLength = messages.sumOf { it.content.length }
            val estimatedTokens = estimateTokens(messages.joinToString("\n") { "${it.role}: ${it.content}" })

            Timber.tag("LayeredPromptBuilder").d(
                "Final prompt: ${messages.size} messages, $totalLength chars, ~$estimatedTokens tokens",
            )
        }
    }

    /**
     * 仅在DEBUG模式下输出系统提示词摘要
     */
    private fun logSystemPromptSummary(systemPrompt: String) {
        if (ENABLE_DEBUG_LOGGING) {
            Timber.tag(
                "LayeredPromptBuilder",
            ).d("System prompt: ${systemPrompt.length} chars, hash: ${systemPrompt.hashCode()}")
        }
    }

    /**
     * 转义JSON字符串
     */
    private fun escapeJsonString(str: String): String =
        "\"" +
            str
                .replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t") + "\""

    /**
     * 🔥 新增：主动检测提示词变更并刷新
     *
     * 检测机制：
     * 1. 定时检查（30秒间隔）
     * 2. 内容哈希比较
     * 3. 文件长度验证
     * 4. 强制刷新机制
     */
    private suspend fun checkAndRefreshPromptIfNeeded() {
        val currentTime = System.currentTimeMillis()

        // 检查是否需要定时检查
        val shouldCheck = (currentTime - lastPromptCheckTime) > PROMPT_CHECK_INTERVAL_MS

        if (shouldCheck) {
            if (ENABLE_DEBUG_LOGGING) {
                Timber.tag("LayeredPromptBuilder").d("Checking prompt changes...")
            }

            try {
                val currentPrompt = promptRegistry.getSystemPrompt()
                val currentHash = currentPrompt.hashCode()
                val isLengthValid = currentPrompt.length >= MIN_VALID_PROMPT_LENGTH
                val isHashChanged = lastPromptHash != 0 && lastPromptHash != currentHash

                if (ENABLE_DEBUG_LOGGING) {
                    Timber.tag(
                        "LayeredPromptBuilder",
                    ).d("Prompt check - Length: ${currentPrompt.length}, Hash: $currentHash")
                }

                if (!isLengthValid || lastPromptHash == 0) {
                    if (ENABLE_DEBUG_LOGGING) {
                        Timber.tag("LayeredPromptBuilder").w("Prompt appears invalid, attempting refresh")
                    }

                    val refreshResult = forceRefreshPrompt()
                    consecutiveFailures = if (refreshResult) 0 else consecutiveFailures + 1
                } else if (isHashChanged) {
                    if (ENABLE_DEBUG_LOGGING) {
                        Timber.tag("LayeredPromptBuilder").d("Prompt content changed")
                    }
                    consecutiveFailures = 0
                }

                lastPromptCheckTime = currentTime
                lastPromptHash = currentHash
            } catch (e: Exception) {
                consecutiveFailures++
                if (ENABLE_DEBUG_LOGGING) {
                    Timber.tag("LayeredPromptBuilder").e(e, "Prompt check failed")
                }
            }
        }
    }

    /**
     * 🔥 【并发控制修复】强制刷新提示词
     *
     * @return 是否刷新成功
     */
    private suspend fun forceRefreshPrompt(): Boolean =
        try {
            if (ENABLE_DEBUG_LOGGING) {
                Timber.tag("LayeredPromptBuilder").d("Force refreshing prompt...")
            }

            val updateResult = promptRegistry.checkAndUpdateAllConfigs()

            if (updateResult.contains("更新文件数: 0")) {
                val syncResult = promptRegistry.forceSyncAllAssets()
                if (ENABLE_DEBUG_LOGGING) {
                    Timber.tag("LayeredPromptBuilder").d("Force sync result: $syncResult")
                }
            }

            val currentMode = promptRegistry.getCurrentMode()
            val refreshedConfig = promptRegistry.reloadConfig(currentMode)
            val newPrompt = refreshedConfig.systemPrompt
            val isValid = newPrompt.length >= MIN_VALID_PROMPT_LENGTH

            if (ENABLE_DEBUG_LOGGING && isValid) {
                Timber.tag("LayeredPromptBuilder").d("Refresh success: ${newPrompt.length} chars")
            }

            isValid
        } catch (e: Exception) {
            if (ENABLE_DEBUG_LOGGING) {
                Timber.tag("LayeredPromptBuilder").e(e, "Force refresh failed")
            }
            false
        }

    /**
     * 从AiContextData提取Memory上下文
     */
    private fun extractMemoryContext(
        ctx: AiContextData,
        userInput: String,
        userId: String, // 使用传入的userId
    ): MemoryContext? {
        // 判断上下文类型
        val contextType =
            when {
                ctx.relevantTemplates.isNotEmpty() -> MemoryContextType.TRAINING
                ctx.userProfile != null -> MemoryContextType.PROFILE
                ctx.recentHistory.isNotEmpty() -> MemoryContextType.CONVERSATION
                else -> MemoryContextType.GENERAL
            }

        // 如果是简单问候，不需要Memory
        if (isSimpleGreeting(userInput)) {
            return null
        }

        return MemoryContext(
            userId = userId, // 使用传入的userId
            query = userInput,
            contextType = contextType,
            metadata =
            buildMap {
                if (ctx.relevantTemplates.isNotEmpty()) {
                    put("has_relevant_templates", true)
                }
                if (ctx.recentHistory.isNotEmpty()) {
                    put("has_recent_history", true)
                }
            },
        )
    }

    /**
     * 判断是否为简单问候
     */
    private fun isSimpleGreeting(input: String): Boolean {
        val greetings = listOf("hi", "hello", "你好", "嗨", "hey")
        val normalized = input.lowercase().trim()
        return greetings.any { normalized == it || normalized.startsWith("$it ") }
    }

    /**
     * 系统提示词验证器
     * 根据ThinkingBox简化重构完成总结.md的建议实现
     */
    private fun validateSystemPrompt(systemPrompt: String) {
        val hash = systemPrompt.hashCode()
        Timber.i("🔍 systemPromptHash=$hash")

        // 🔥 强制输出系统提示词内容用于调试（分块显示）
        val chunks = systemPrompt.chunked(500)
        chunks.forEachIndexed { index, chunk ->
            Timber.tag("PROMPT-BUILDER").e("📄 系统提示词-第${index + 1}块/${chunks.size}: $chunk")
        }

        // 检查是否包含<think>示例
        if (systemPrompt.contains("<think>")) {
            Timber.w("⚠️ 系统提示词包含<think>示例，可能导致AI输出原始标签")
            // 打印具体的<think>内容位置
            val thinkIndex = systemPrompt.indexOf("<think>")
            val thinkEndIndex = systemPrompt.indexOf("</think>", thinkIndex)
            if (thinkEndIndex != -1) {
                val thinkContent = systemPrompt.substring(thinkIndex, thinkEndIndex + 8)
                Timber.w("🚨 发现<think>内容: $thinkContent")
            }
        }

        // 检查是否包含思考相关指令
        if (systemPrompt.contains("思考标签") || systemPrompt.contains("思考过程")) {
            Timber.w("⚠️ 系统提示词包含思考相关指令，可能导致AI输出标签")
        }

        // 检查是否包含<phase:XXX>指令
        if (systemPrompt.contains("<phase:")) {
            Timber.d("✅ 系统提示词包含<phase:XXX>指令")
        }

        // 检查长度
        Timber.d("📏 系统提示词长度: ${systemPrompt.length}字符")
    }

    /**
     * 验证消息结构
     */
    private fun validateMessages(messages: List<CoreChatMessage>) {
        // 必须有且只有一条system消息
        val systemCount = messages.count { it.role == "system" }
        require(systemCount == 1) {
            "必须有且只有一条system消息，实际: $systemCount"
        }

        // system消息必须在第一条
        require(messages.first().role == "system") {
            "第一条消息必须是system消息"
        }

        // 最后一条必须是user消息
        require(messages.last().role == "user") {
            "最后一条消息必须是user消息"
        }

        if (ENABLE_DEBUG_LOGGING) {
            Timber.tag("LayeredPromptBuilder").d("Message validation passed")
        }
    }

    // =============== 已废弃的方法 ===============

    @Deprecated("改用 buildChatMessages", level = DeprecationLevel.ERROR)
    override suspend fun buildPrompt(
        context: AiContextData,
        userMessage: String,
        tokenBudget: Int,
    ): String = throw UnsupportedOperationException("请使用 buildChatMessages")

    @Deprecated("增量模式已下线", level = DeprecationLevel.ERROR)
    override suspend fun buildIncrementalPrompt(
        context: AiContextData,
        previousContext: AiContextData?,
        userMessage: String,
    ): String = throw UnsupportedOperationException("增量模式已下线")

    @Deprecated("Pipeline已移除", level = DeprecationLevel.ERROR)
    override fun executeSteps(
        userPrompt: String,
        systemLayer: SystemLayer,
        enableFunctions: Boolean,
    ): Flow<PipelineEvent> = emptyFlow()

    @Deprecated("Pipeline已移除", level = DeprecationLevel.ERROR)
    override fun executeStepsIntelligently(
        userPrompt: String,
        systemLayer: SystemLayer,
        enableFunctions: Boolean,
        forceSimpleMode: Boolean,
    ): Flow<PipelineEvent> = emptyFlow()

    /**
     * 获取当前激活的 SystemLayer
     * 用于 SendChatMessageAndGetResponseUseCase 中的 Prompt 切换功能
     */
    fun getCurrentSystemLayer(): SystemLayer {
        val currentPromptSuite = promptRegistry.getSuite()
        return currentPromptSuite.systemLayer
    }
}
