# Template目录拖拽代码完全清理任务

## 任务概述

**方案**: A - 渐进式彻底清理  
**时间**: 2天  
**风险**: 低  
**目标**: 建立零冗余、高质量的统一拖拽架构

## 四大清理目标

### 1. 完全清理旧拖拽实现
- 移除所有`detectDragGestures`直接使用
- 统一使用`DragModifiers`和shared组件
- 删除分散的拖拽状态字段

### 2. 代码结构整理
- 统一导入语句
- 移除未使用的依赖
- 优化文件结构和职责分离

### 3. TODO/FIXME代码质量问题识别和实施
- 识别重要TODO点并分类处理
- 移除占位符注释
- 完善未完成的函数实现

### 4. 硬编码修复
- 所有dp值使用designSystem tokens
- 动画参数使用MotionDurations
- 颜色值使用MaterialTheme

## 需要清理的关键文件列表

### 高优先级文件 (立即处理)
1. `TemplateScreen.kt` - 混合拖拽实现
2. `TemplateEditComponents.kt` - 重复拖拽逻辑  
3. `TemplateContract.kt` - 状态管理混乱
4. `TemplateEditContract.kt` - 旧状态字段
5. `DragAnimationIntegration.kt` - 状态管理不一致

### 代码质量问题文件
6. `TemplateEffectHandler.kt` - TODO注释
7. `TemplateEditScreen.kt` - TODO/硬编码
8. `TemplateDataMapper.kt` - 多处TODO
9. `TemplatePreview.kt` - 硬编码dp值
10. `TemplatePreviewCard.kt` - TODO和硬编码

## 执行计划

### 第一天: 核心清理
- [ ] 移除旧拖拽实现
- [ ] 统一状态管理
- [ ] 清理关键TODO点

### 第二天: 质量提升  
- [ ] 硬编码标准化
- [ ] 导入优化
- [ ] 文档完善

## 下一步
使用Sub Agent对关键文件进行详细问题扫描和汇报