package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import com.example.gymbro.core.ai.tokenizer.TokenizerService
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.ColorTokens
import com.example.gymbro.designSystem.theme.tokens.MapleMono
import com.example.gymbro.designSystem.theme.tokens.Tokens
import kotlinx.coroutines.delay

/**
 * FinalActionsRow - 最终操作行组件
 *
 * 完全使用设计系统Tokens，零硬编码
 *
 * @param finalContent 最终内容文本
 * @param tokenizerService Token 计算服务（可选）
 * @param modifier 修饰符
 */
@Composable
fun FinalActionsRow(
    finalContent: String,
    tokenizerService: TokenizerService? = null,
    modifier: Modifier = Modifier,
) {
    val clipboardManager = LocalClipboardManager.current
    var tokenCount by remember(finalContent) { mutableIntStateOf(0) }
    var showCopied by remember { mutableStateOf(false) }

    // 计算Token数量
    LaunchedEffect(finalContent, tokenizerService) {
        tokenizerService?.let { service ->
            tokenCount = service.countTokens(finalContent)
        }
    }

    // 复制状态重置
    LaunchedEffect(showCopied) {
        if (showCopied) {
            delay(2000)
            showCopied = false
        }
    }

    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Token 计数显示
        if (tokenizerService != null) {
            Column {
                Icon(
                    imageVector = Icons.Default.ContentCopy,
                    contentDescription = null,
                    tint = ColorTokens.Dark.OnSurfaceVariant, // 🔥 【主题适配】使用主题感知颜色
                )
                Text(
                    text = "$tokenCount tokens",
                    style = androidx.compose.ui.text.TextStyle(
                        fontFamily = MapleMono, // 🔥 【字体系统修复】使用项目标准字体族
                        fontSize = Tokens.Typography.Small,
                        color = ColorTokens.Dark.OnSurfaceVariant, // 🔥 【主题适配】使用主题感知颜色
                    ),
                )
            }
        }

        // 复制按钮
        Button(
            onClick = {
                clipboardManager.setText(AnnotatedString(finalContent))
                showCopied = true
            },
            shape = RoundedCornerShape(Tokens.Radius.Button),
            colors = ButtonDefaults.buttonColors(
                containerColor = ColorTokens.Component.ButtonPrimary,
                contentColor = ColorTokens.Dark.OnPrimary, // 🔥 【主题适配】使用主题感知颜色
            ),
            modifier = Modifier
                .height(Tokens.Button.HeightSecondary)
                .padding(horizontal = Tokens.Spacing.Small),
        ) {
            Text(
                text = if (showCopied) "已复制" else "复制内容",
                style = androidx.compose.ui.text.TextStyle(
                    fontFamily = MapleMono, // 🔥 【字体系统修复】使用项目标准字体族
                    fontSize = Tokens.Typography.ButtonText,
                    fontWeight = FontWeight.Medium,
                    color = ColorTokens.Dark.OnPrimary, // 🔥 【主题适配】使用主题感知颜色
                ),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun FinalActionsRowPreview() {
    GymBroTheme {
        FinalActionsRow(
            finalContent = "这是一段测试内容，用于预览复制功能。",
        )
    }
}