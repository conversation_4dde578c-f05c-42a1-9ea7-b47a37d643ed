package com.example.gymbro.features.thinkingbox.integration

import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.reducer.SegmentQueueReducer
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * 真实代码验证测试
 * 验证ThinkingBox的真实代码组件能否正确处理AI流
 */
@DisplayName("真实代码验证测试")
class RealCodeValidationTest {

    private lateinit var domainMapper: DomainMapper
    private lateinit var segmentQueueReducer: SegmentQueueReducer

    @BeforeEach
    fun setup() {
        domainMapper = DomainMapper()
        segmentQueueReducer = SegmentQueueReducer()
    }

    @Test
    @DisplayName("验证DomainMapper的XML标签映射")
    fun `verify DomainMapper XML tag mapping`() = runTest {
        var context = DomainMapper.MappingContext()

        // 1. 测试 <thinking> 标签开启
        val thinkingOpenEvent = SemanticEvent.TagOpened("thinking", emptyMap())
        val thinkingResult = domainMapper.mapSemanticToThinking(thinkingOpenEvent, context)
        context = thinkingResult.context

        // 验证thinking开启会闭合perthink段
        assertTrue(thinkingResult.events.isNotEmpty())
        assertTrue(thinkingResult.events.any { it is ThinkingEvent.SegmentClosed })

        // 2. 测试 <phase id="1"> 标签
        val phaseOpenEvent = SemanticEvent.TagOpened("phase", mapOf("id" to "1"))
        val phaseResult = domainMapper.mapSemanticToThinking(phaseOpenEvent, context)
        context = phaseResult.context

        // 验证phase开启创建新段
        assertTrue(phaseResult.events.any { it is ThinkingEvent.SegmentStarted })
        val segmentStarted = phaseResult.events.find {
            it is ThinkingEvent.SegmentStarted
        } as ThinkingEvent.SegmentStarted
        assertEquals("1", segmentStarted.id)
        assertEquals(SegmentKind.PHASE, segmentStarted.kind)

        // 3. 测试文本内容
        val textEvent = SemanticEvent.TextChunk("这是phase1的内容")
        val textResult = domainMapper.mapSemanticToThinking(textEvent, context)
        context = textResult.context

        // 验证文本映射
        assertTrue(textResult.events.any { it is ThinkingEvent.SegmentText })
        val segmentText = textResult.events.find { it is ThinkingEvent.SegmentText } as ThinkingEvent.SegmentText
        assertEquals("这是phase1的内容", segmentText.text)

        // 4. 测试 </phase> 标签关闭
        val phaseCloseEvent = SemanticEvent.TagClosed("phase")
        val phaseCloseResult = domainMapper.mapSemanticToThinking(phaseCloseEvent, context)
        context = phaseCloseResult.context

        // 验证phase关闭
        assertTrue(phaseCloseResult.events.any { it is ThinkingEvent.SegmentClosed })

        // 5. 测试 </thinking> 标签关闭
        val thinkingCloseEvent = SemanticEvent.TagClosed("thinking")
        val thinkingCloseResult = domainMapper.mapSemanticToThinking(thinkingCloseEvent, context)
        context = thinkingCloseResult.context

        // 验证thinking关闭
        assertTrue(thinkingCloseResult.events.any { it is ThinkingEvent.ThinkingClosed })

        // 6. 测试 <final> 标签
        val finalStartEvent = SemanticEvent.FinalStart
        val finalStartResult = domainMapper.mapSemanticToThinking(finalStartEvent, context)
        context = finalStartResult.context

        // 验证final开始
        assertTrue(finalStartResult.events.any { it is ThinkingEvent.FinalStart })
        assertTrue(context.inFinal)

        // 7. 测试final内容
        val finalChunkEvent = SemanticEvent.FinalChunk("# 最终答案\n\n这是AI的最终回答")
        val finalChunkResult = domainMapper.mapSemanticToThinking(finalChunkEvent, context)
        context = finalChunkResult.context

        // 验证final内容
        assertTrue(finalChunkResult.events.any { it is ThinkingEvent.FinalContent })
        val finalContent = finalChunkResult.events.find {
            it is ThinkingEvent.FinalContent
        } as ThinkingEvent.FinalContent
        assertEquals("# 最终答案\n\n这是AI的最终回答", finalContent.text)

        // 8. 测试 final结束
        val finalEndEvent = SemanticEvent.FinalEnd
        val finalEndResult = domainMapper.mapSemanticToThinking(finalEndEvent, context)

        // 验证final结束
        assertTrue(finalEndResult.events.any { it is ThinkingEvent.FinalComplete })
        assertFalse(finalEndResult.context.inFinal)
    }

    @Test
    @DisplayName("验证SegmentQueueReducer的状态管理")
    fun `verify SegmentQueueReducer state management`() = runTest {
        var state = SegmentQueueReducer.TBState(messageId = "test-123")

        // 1. 创建第一个segment
        val segmentStarted = ThinkingEvent.SegmentStarted("1", SegmentKind.PHASE, "分析目标")
        var result = segmentQueueReducer.reduce(state, segmentStarted)
        state = result.state

        // 验证segment创建
        assertEquals("1", state.current?.id)
        assertEquals(SegmentKind.PHASE, state.current?.kind)
        assertEquals("分析目标", state.current?.title)
        assertTrue(state.queue.isEmpty())

        // 2. 添加文本内容
        val segmentText = ThinkingEvent.SegmentText("我需要分析用户的健身目标")
        result = segmentQueueReducer.reduce(state, segmentText)
        state = result.state

        // 验证文本添加
        assertTrue(state.current?.getTextContent()?.contains("我需要分析用户的健身目标") == true)

        // 3. 关闭segment
        val segmentClosed = ThinkingEvent.SegmentClosed("1")
        result = segmentQueueReducer.reduce(state, segmentClosed)
        state = result.state

        // 验证segment关闭并加入队列
        assertEquals(null, state.current)
        assertEquals(1, state.queue.size)
        assertTrue(state.queue.first().closed)
        assertEquals("我需要分析用户的健身目标", state.queue.first().getTextContent())

        // 4. 创建第二个segment
        val segmentStarted2 = ThinkingEvent.SegmentStarted("2", SegmentKind.PHASE, "制定计划")
        result = segmentQueueReducer.reduce(state, segmentStarted2)
        state = result.state

        // 验证第二个segment
        assertEquals("2", state.current?.id)
        assertEquals("制定计划", state.current?.title)
        assertEquals(1, state.queue.size) // 第一个segment仍在队列中

        // 5. 结束thinking阶段
        val thinkingClosed = ThinkingEvent.ThinkingClosed
        result = segmentQueueReducer.reduce(state, thinkingClosed)
        state = result.state

        // 验证thinking结束
        assertTrue(state.thinkingClosed)

        // 6. 处理final内容
        val finalStart = ThinkingEvent.FinalStart
        result = segmentQueueReducer.reduce(state, finalStart)
        state = result.state

        val finalContent = ThinkingEvent.FinalContent("# 健身计划\n\n根据分析，为您制定以下计划...")
        result = segmentQueueReducer.reduce(state, finalContent)
        state = result.state

        // 验证final内容
        assertTrue(state.finalBuffer.toString().contains("健身计划"))

        val finalComplete = ThinkingEvent.FinalComplete
        result = segmentQueueReducer.reduce(state, finalComplete)
        state = result.state

        // 验证final完成
        assertTrue(state.finalClosed)
        // 注意：streaming属性已被删除，现在是Flow-derived

        // 7. 验证最终状态
        // 注意：isFinalReadyToRender需要 thinkingClosed && queue.isEmpty() && finalBuffer.isNotEmpty()
        // 但我们的测试中queue不为空，所以这里只验证基本状态
        assertTrue(state.thinkingClosed)
        assertTrue(state.finalClosed)
        assertTrue(state.getFinalContent().contains("健身计划"))
        assertTrue(state.finalBuffer.isNotEmpty())
    }

    @Test
    @DisplayName("验证完整的AI流处理链路")
    fun `verify complete AI stream processing chain`() = runTest {
        // 模拟完整的AI流：SemanticEvent → ThinkingEvent → State
        var mapperContext = DomainMapper.MappingContext()
        var reducerState = SegmentQueueReducer.TBState(messageId = "integration-test")

        // 定义完整的AI流事件序列
        val aiStreamEvents = listOf(
            SemanticEvent.TagOpened("thinking", emptyMap()),
            SemanticEvent.TagOpened("phase", mapOf("id" to "1")),
            SemanticEvent.TextChunk("分析用户需求"),
            SemanticEvent.TagClosed("phase"),
            SemanticEvent.TagOpened("phase", mapOf("id" to "2")),
            SemanticEvent.TextChunk("制定解决方案"),
            SemanticEvent.TagClosed("phase"),
            SemanticEvent.TagClosed("thinking"),
            SemanticEvent.FinalStart,
            SemanticEvent.FinalChunk("# 最终方案\n\n基于分析，推荐以下方案..."),
            SemanticEvent.FinalEnd,
        )

        // 处理每个事件
        aiStreamEvents.forEach { semanticEvent ->
            // 1. DomainMapper映射
            val mappingResult = domainMapper.mapSemanticToThinking(semanticEvent, mapperContext)
            mapperContext = mappingResult.context

            // 2. SegmentQueueReducer处理
            mappingResult.events.forEach { thinkingEvent ->
                val reduceResult = segmentQueueReducer.reduce(reducerState, thinkingEvent)
                reducerState = reduceResult.state
            }
        }

        // 验证最终状态
        assertEquals("integration-test", reducerState.messageId)
        assertTrue(reducerState.queue.size >= 2) // 至少有两个phase段
        assertTrue(reducerState.thinkingClosed)
        assertTrue(reducerState.finalClosed)
        // 注意：streaming属性已被删除，现在是Flow-derived

        // 验证final内容存在
        assertTrue(reducerState.finalBuffer.isNotEmpty())

        // 验证队列中包含我们期望的内容
        val queueList = reducerState.queue.toList()
        val hasAnalysisContent = queueList.any { it.getTextContent().contains("分析用户需求") }
        val hasSolutionContent = queueList.any { it.getTextContent().contains("制定解决方案") }
        assertTrue(hasAnalysisContent, "队列中应包含分析用户需求的内容")
        assertTrue(hasSolutionContent, "队列中应包含制定解决方案的内容")

        // 验证final内容
        assertTrue(reducerState.getFinalContent().contains("最终方案"))
        assertTrue(reducerState.getFinalContent().contains("推荐以下方案"))
    }
}
