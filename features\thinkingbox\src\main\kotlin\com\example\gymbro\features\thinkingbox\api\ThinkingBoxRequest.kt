package com.example.gymbro.features.thinkingbox.api

/**
 * ThinkingBoxRequest - AI思考处理请求数据类
 *
 * 🎯 职责分离架构设计：
 * - 封装Coach模块向ThinkingBox模块发起AI处理请求的所有必要信息
 * - 遵循Clean Architecture原则，作为模块间通信的数据载体
 * - 支持完整的上下文信息传递，确保AI处理的个性化和准确性
 *
 * 🔥 核心功能：
 * - 会话管理：sessionId用于关联整个对话会话
 * - 消息追踪：messageId用于唯一标识单次AI响应
 * - 上下文传递：context包含用户信息、对话历史等
 * - 内容载体：userMessage承载用户的原始输入
 *
 * 架构原则：
 * - 数据不可变：所有属性为val，确保线程安全
 * - 类型安全：使用强类型避免运行时错误
 * - 扩展性：context设计支持未来功能扩展
 * - 清晰性：每个字段都有明确的业务含义
 *
 * @since Coach-ThinkingBox重构v2.0
 */
data class ThinkingBoxRequest(
    /**
     * 会话唯一标识符
     *
     * 用于关联整个对话会话，支持多轮对话的上下文管理。
     * 在同一个会话中的所有消息都应该使用相同的sessionId。
     *
     * 格式建议：UUID或时间戳+用户ID的组合
     * 示例："session_20240802_user123" 或 "uuid-v4-string"
     */
    val sessionId: String,

    /**
     * 消息唯一标识符
     *
     * 用于唯一标识本次AI响应，支持消息级别的追踪和管理。
     * 每次AI处理请求都应该生成新的messageId。
     *
     * 格式建议：UUID或sessionId+序号的组合
     * 示例："msg_20240802_001" 或 "uuid-v4-string"
     */
    val messageId: String,

    /**
     * 用户输入消息
     *
     * 用户的原始输入内容，作为AI处理的主要输入。
     * 应该保持用户的原始表达，不进行预处理或修改。
     *
     * 内容要求：
     * - 非空字符串
     * - 保持原始格式（包括换行、标点等）
     * - 支持多语言输入
     */
    val userMessage: String,

    /**
     * 思考处理上下文
     *
     * 包含AI处理所需的所有上下文信息，如用户资料、对话历史、
     * 模板信息等。这些信息用于个性化AI响应和提高处理质量。
     *
     * 上下文内容：
     * - 用户个人资料和偏好
     * - 历史对话记录
     * - 模板和预设信息
     * - 其他业务相关上下文
     */
    val context: ThinkingBoxContext,
) {
    /**
     * 验证请求数据的完整性
     *
     * 检查所有必要字段是否已正确填充。
     *
     * @return 如果请求数据有效返回true，否则返回false
     */
    fun isValid(): Boolean {
        return sessionId.isNotBlank() &&
            messageId.isNotBlank() &&
            userMessage.isNotBlank() &&
            context.isValid()
    }

    /**
     * 获取请求摘要信息
     *
     * 用于日志记录和调试，提供请求的关键信息概览。
     *
     * @return 包含关键信息的摘要字符串
     */
    fun getSummary(): String {
        return "ThinkingBoxRequest(sessionId=$sessionId, messageId=$messageId, " +
            "userMessageLength=${userMessage.length}, contextValid=${context.isValid()})"
    }

    /**
     * 创建处理元数据
     *
     * 生成用于处理过程中的元数据信息。
     *
     * @return 包含处理元数据的Map
     */
    fun createProcessingMetadata(): Map<String, Any> {
        return mapOf(
            "sessionId" to sessionId,
            "messageId" to messageId,
            "userMessageLength" to userMessage.length,
            "requestTimestamp" to System.currentTimeMillis(),
            "contextType" to context::class.simpleName.orEmpty(),
            "hasUserProfile" to (context.userProfile != null),
            "hasConversationHistory" to context.conversationHistory.isNotEmpty(),
            "hasTemplateContext" to (context.templateContext != null),
        )
    }

    companion object {
        /**
         * 创建简单的思考请求
         *
         * 用于快速创建只包含基本信息的请求，适用于简单场景。
         *
         * @param sessionId 会话ID
         * @param messageId 消息ID
         * @param userMessage 用户消息
         * @return 简化的ThinkingBoxRequest实例
         */
        fun createSimple(
            sessionId: String,
            messageId: String,
            userMessage: String,
        ): ThinkingBoxRequest {
            return ThinkingBoxRequest(
                sessionId = sessionId,
                messageId = messageId,
                userMessage = userMessage,
                context = ThinkingBoxContext.createEmpty(),
            )
        }

        /**
         * 创建带用户资料的思考请求
         *
         * 用于创建包含用户个人资料的请求，支持个性化AI处理。
         *
         * @param sessionId 会话ID
         * @param messageId 消息ID
         * @param userMessage 用户消息
         * @param userProfile 用户资料上下文
         * @return 包含用户资料的ThinkingBoxRequest实例
         */
        fun createWithUserProfile(
            sessionId: String,
            messageId: String,
            userMessage: String,
            userProfile: UserProfileContext,
        ): ThinkingBoxRequest {
            return ThinkingBoxRequest(
                sessionId = sessionId,
                messageId = messageId,
                userMessage = userMessage,
                context = ThinkingBoxContext.createWithUserProfile(userProfile),
            )
        }
    }
}
