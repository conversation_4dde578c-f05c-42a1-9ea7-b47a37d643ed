package com.example.gymbro.core.logging

import android.annotation.SuppressLint
import android.util.Log
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🏷️ GymBro 统一日志标签管理系统
 *
 * 集中管理所有模块的日志标签，确保标签格式统一和易于维护
 */
object GymBroLogTags {

    // ==================== Core-Network 模块标签 ====================

    /**
     * Core-Network 模块日志标签
     * 格式：CNET-{子包名}-{功能}
     */
    object CoreNetwork {
        // 核心服务层
        const val SERVICE_UNIFIED_AI = "CNET-SERVICE-UnifiedAi"

        // 处理器层
        const val PROCESSOR_STREAM = "CNET-PROCESSOR-Stream"
        const val PROCESSOR_CONTENT = "CNET-PROCESSOR-Content"
        const val PROCESSOR_SANITIZER = "CNET-PROCESSOR-Sanitizer"

        // 输出层
        const val OUTPUT_DIRECT = "CNET-OUTPUT-Direct"

        // 网络层
        const val REST_CLIENT = "CNET-REST-Client"
        const val INTERCEPTOR_AUTH = "CNET-INTERCEPTOR-Auth"
        const val INTERCEPTOR_NETWORK = "CNET-INTERCEPTOR-Network"
        const val INTERCEPTOR_RETRY = "CNET-INTERCEPTOR-Retry"
        const val INTERCEPTOR_LOGGING = "CNET-INTERCEPTOR-Logging"

        // 配置层
        const val CONFIG_MANAGER = "CNET-CONFIG-Manager"

        // 监控层
        const val MONITOR_NETWORK = "CNET-MONITOR-Network"
        const val MONITOR_PERFORMANCE = "CNET-MONITOR-Performance"

        // 安全层
        const val SECURITY_PII = "CNET-SECURITY-Pii"

        // 状态层
        const val STATE_NETWORK = "CNET-STATE-Network"

        // 依赖注入层
        const val DI_MODULE = "CNET-DI-Module"

        // 兼容旧标签（逐步迁移）
        @Deprecated("使用 SERVICE_UNIFIED_AI 替代", ReplaceWith("SERVICE_UNIFIED_AI"))
        const val ERROR = "CNET-ERROR"
        @Deprecated("使用 PROCESSOR_STREAM 替代", ReplaceWith("PROCESSOR_STREAM"))
        const val STREAM = "CNET-STREAM"
        @Deprecated("使用 MONITOR_NETWORK 替代", ReplaceWith("MONITOR_NETWORK"))
        const val CHECK = "CNET-CHECK"
        @Deprecated("使用 PROCESSOR_STREAM 替代", ReplaceWith("PROCESSOR_STREAM"))
        const val SSE = "CNET-SSE"
        @Deprecated("使用 MONITOR_NETWORK 替代", ReplaceWith("MONITOR_NETWORK"))
        const val MONITOR = "CNET-MONITOR"
        @Deprecated("使用 INTERCEPTOR_RETRY 替代", ReplaceWith("INTERCEPTOR_RETRY"))
        const val RETRY = "CNET-RETRY"
        @Deprecated("使用 CONFIG_MANAGER 替代", ReplaceWith("CONFIG_MANAGER"))
        const val LIFECYCLE = "CNET-LIFECYCLE"
        @Deprecated("使用 SECURITY_PII 替代", ReplaceWith("SECURITY_PII"))
        const val SECURITY = "CNET-SECURITY"
        @Deprecated("使用 MONITOR_PERFORMANCE 替代", ReplaceWith("MONITOR_PERFORMANCE"))
        const val PERF = "CNET-PERF"
        const val GENERAL = "CNET-GENERAL"

        // Token相关标签（保留用于特殊调试）
        const val TOKEN_RECV = "CNET-TOKEN-RECV"
        const val TOKEN_OUT = "CNET-TOKEN-OUT"
        const val TOKEN_STATS = "CNET-TOKEN-STATS"
        const val TOKEN_BATCH = "CNET-TOKEN-BATCH"
        const val TOKEN_ERROR = "CNET-TOKEN-ERROR"
    }

    // ==================== ThinkingBox 模块标签 ====================

    /**
     * ThinkingBox 模块日志标签
     * 格式：TB-{子包名}-{功能}
     */
    object ThinkingBox {
        const val PARSER = "TB-PARSER"
        const val RENDERER = "TB-RENDERER"
        const val STATE = "TB-STATE"
        const val ADAPTER = "TB-ADAPTER"
        const val E2E_TRACE = "TB-E2E-TRACE"
        const val ADAPTER_TRACE = "TB-ADAPTER-TRACE"
    }

    // ==================== 其他模块标签 ====================

    /**
     * Core 模块日志标签
     */
    object Core {
        const val LOG_MANAGER = "LOG-MANAGER"
        const val ERROR_HANDLER = "CORE-ERROR"
        const val SERVICE_MANAGER = "CORE-SERVICE"
    }

    /**
     * 获取所有Core-Network标签列表
     */
    fun getAllCoreNetworkTags(): List<String> {
        return listOf(
            CoreNetwork.SERVICE_UNIFIED_AI,
            CoreNetwork.PROCESSOR_STREAM,
            CoreNetwork.PROCESSOR_CONTENT,
            CoreNetwork.PROCESSOR_SANITIZER,
            CoreNetwork.OUTPUT_DIRECT,
            CoreNetwork.REST_CLIENT,
            CoreNetwork.INTERCEPTOR_AUTH,
            CoreNetwork.INTERCEPTOR_NETWORK,
            CoreNetwork.INTERCEPTOR_RETRY,
            CoreNetwork.INTERCEPTOR_LOGGING,
            CoreNetwork.CONFIG_MANAGER,
            CoreNetwork.MONITOR_NETWORK,
            CoreNetwork.MONITOR_PERFORMANCE,
            CoreNetwork.SECURITY_PII,
            CoreNetwork.STATE_NETWORK,
            CoreNetwork.DI_MODULE
        )
    }

    /**
     * 获取所有兼容旧标签列表（用于迁移）
     */
    @Deprecated("仅用于迁移期间的兼容性")
    fun getLegacyCoreNetworkTags(): List<String> {
        return listOf(
            CoreNetwork.ERROR,
            CoreNetwork.STREAM,
            CoreNetwork.CHECK,
            CoreNetwork.SSE,
            CoreNetwork.MONITOR,
            CoreNetwork.RETRY,
            CoreNetwork.LIFECYCLE,
            CoreNetwork.SECURITY,
            CoreNetwork.PERF,
            CoreNetwork.GENERAL,
            CoreNetwork.TOKEN_RECV,
            CoreNetwork.TOKEN_OUT,
            CoreNetwork.TOKEN_STATS,
            CoreNetwork.TOKEN_BATCH,
            CoreNetwork.TOKEN_ERROR
        )
    }
}

/**
 * 🔥 【重构】增强的 Timber 日志管理器
 *
 * 统一管理Timber日志系统的初始化、配置和控制。
 * 集成模块级别的日志控制和性能优化。
 */
@Singleton
class TimberManager
@Inject
constructor(
    private val loggingConfig: LoggingConfig,
) {
    /**
     * 🔥 【修复】初始化日志系统 - 使用专用ThinkingBoxLogTree
     *
     * 移除ThinkingBoxAwareTree，使用功能更完整的ThinkingBoxLogTree作为主要日志处理器
     *
     * @param isDebug 是否为调试模式
     * @param environment 环境类型
     * @param enableThinkingBoxMode 是否启用ThinkingBox专用模式
     */
    fun initialize(
        isDebug: Boolean,
        environment: LoggingConfig.Environment = LoggingConfig.Environment.DEVELOPMENT,
        enableThinkingBoxMode: Boolean = true, // 🔥 【临时调试】启用ThinkingBox专用日志
    ) {
        // 设置环境
        loggingConfig.setEnvironment(environment)

        // 清除所有已有的Tree
        Timber.uprootAll()

        // 根据环境和模式安装适当的Tree
        when {
            isDebug && enableThinkingBoxMode -> {
                // 🔥 【修复】ThinkingBox专用模式：使用专用的ThinkingBoxLogTree作为主要日志处理器
                // 移除ThinkingBoxAwareTree，使用功能更完整的ThinkingBoxLogTree
                loadThinkingBoxLogTree()

                // 🔥 【重构清理】移除WorkoutLogTree动态加载，统一由ModuleAwareTree处理
                // WorkoutLogTree保留为工具类，避免重复Tree处理同一日志

                // 🔥 【新增】动态加载NetworkLogTree
                loadNetworkLogTree()

                Timber.tag(
                    "LOG-MANAGER",
                ).i("🔥 ThinkingBox专用日志系统已启动 - 使用专用ThinkingBoxLogTree + 统一模块路由 + 网络支持")
            }

            isDebug -> {
                // 开发环境：使用模块感知的Tree + NetworkLogTree
                Timber.plant(ModuleAwareTree(loggingConfig))

                // 🔥 【重构清理】移除WorkoutLogTree动态加载，统一由ModuleAwareTree处理
                // 避免同一日志被多个Tree重复处理

                // 🔥 【新增】动态加载NetworkLogTree
                loadNetworkLogTree()

                Timber.tag("LOG-MANAGER").i("🔥 开发环境日志系统已启动 - 模块感知模式 + 统一模块路由 + 网络支持")
            }

            environment == LoggingConfig.Environment.PRODUCTION -> {
                // 生产环境：只记录错误
                Timber.plant(ReleaseTree())

                // 🔥 【关键修复】生产环境也需要加载NetworkLogTree以处理CNET-ERROR
                loadNetworkLogTree()

                Timber.tag("LOG-MANAGER").i("🔥 生产环境日志系统已启动 - 仅错误模式 + 网络错误支持")
            }

            else -> {
                // 测试环境：适度日志
                Timber.plant(ModuleAwareTree(loggingConfig))

                // 🔥 【关键修复】测试环境也需要加载NetworkLogTree以处理CNET标签
                loadNetworkLogTree()

                Timber.tag("LOG-MANAGER").i("🔥 测试环境日志系统已启动 - 适度日志模式 + 网络支持")
            }
        }

        // 启用协程调试支持（仅在调试模式下）
        if (isDebug) {
            System.setProperty("kotlinx.coroutines.debug", "on")
        }
    }

    /**
     * 🔥 【新增】专用于应用层的ThinkingBox模式初始化
     *
     * 符合Clean Architecture原则，应用层通过此方法启用ThinkingBox功能
     * 而无需直接依赖features模块
     */
    fun initializeWithThinkingBoxSupport(isDebug: Boolean) {
        initialize(
            isDebug = isDebug,
            environment = if (isDebug) LoggingConfig.Environment.DEVELOPMENT else LoggingConfig.Environment.PRODUCTION,
            enableThinkingBoxMode = isDebug,
        )

        if (isDebug) {
            // 配置ThinkingBox专用的模块设置
            enableThinkingBoxDebugLogs()
            // 🔥 移除噪音日志：ThinkingBox专用模式启动信息
        }
    }

    /**
     * 启用ThinkingBox调试日志
     */
    fun enableThinkingBoxDebugLogs() {
        loggingConfig.updateModuleConfig(
            LoggingConfig.MODULE_THINKING_BOX,
            LoggingConfig.ModuleLogConfig(
                enabled = true,
                minLevel = Log.DEBUG,
                tags = setOf(
                    // 🔥 【核心流程标签】
                    "TB-CORE", "TB-STREAM", "TB-STATE",

                    // 🔥 【架构组件标签】
                    "TB-MAPPER", "TB-REDUCER", "TB-VM", "TB-EFFECT",

                    // 🔥 【数据处理标签】
                    "TB-TOKEN", "TB-XML", "TB-BUFFER",

                    // 🔥 【业务逻辑标签】
                    "TB-SEGMENT", "TB-BREAKPOINT", "TB-FINAL",

                    // 🔥 【UI渲染标签】
                    "TB-UI", "TB-RENDER",

                    // 🔥 【诊断调试标签】
                    "TB-INIT", "TB-ERROR", "TB-WARN", "TB-DEBUG",

                    // 🔥 【兼容旧标签 - 逐步迁移】
                    "TB-VIEWMODEL", "TB-PARSER", "TB-DOMAIN-MAPPER",
                    "TB-XML-SCANNER", "TB-GUARDRAIL",
                    "TB-TOKEN-FLOW", "TB-RAW-COLLECTOR", "TB-RAW-PROCESSOR",
                    "TB-LIFECYCLE", "TB-AUTO-SCROLL", "TB-FINAL-RENDERER",
                    "TB-XML-INPUT", "TB-XML-BUFFER", "TB-XML-PARSE", "TB-XML-OUTPUT",
                    "TB-FIX", "TB-TAG-PHASE", "TB-TAG-FINAL", "TB-TAG-CLOSE", "TB-PROCESSED",
                    "TB-XML-PARTIAL",
                ),
                sampleRate = 1,
            ),
        )

        // 🔥 【新增】启用 core-network 模块的日志
        enableCoreNetworkDebugLogs()

        Timber.tag("LOG-MANAGER").i("🔥 ThinkingBox 调试日志已启用 - 包含Token流修复验证标签")
    }

    /**
     * 🔥 【新增】启用 core-network 模块调试日志
     */
    fun enableCoreNetworkDebugLogs() {
        loggingConfig.updateModuleConfig(
            LoggingConfig.MODULE_CORE_NETWORK,
            LoggingConfig.ModuleLogConfig(
                enabled = true,
                minLevel = Log.DEBUG,
                tags = setOf(
                    // 🔥 【核心网络标签】
                    "CNET-ERROR", "CNET-STREAM", "CNET-CHECK", "CNET-SSE",
                    "CNET-MONITOR", "CNET-RETRY", "CNET-LIFECYCLE",
                    "CNET-SECURITY", "CNET-PERF", "CNET-GENERAL",
                    // 🔥 【Token日志采集标签】
                    "CNET-TOKEN-RECV", "CNET-TOKEN-OUT", "CNET-TOKEN-STATS",
                    "CNET-TOKEN-BATCH", "CNET-TOKEN-ERROR",
                    // 🔥 【Token路由标签】
                    // 旧架构组件已删除：CNT-TOKEN-ROUTER, ConversationScope
                    // 🔥 【消息流调试标签】
                    "CNT-MESSAGE-FLOW",
                    // 🔥 【AI响应管道调试标签】
                    "HTTP-RAW-RESPONSE", "HTTP-RAW-XML", "JSON-PARSE-INPUT",
                    "JSON-PARSE-CLEANED", "JSON-PARSE-CRITICAL", "JSON-PARSE-TEST",
                    "JSON-FIELD-EXTRACT", "JSON-FIELD-ALL", "JSON-DEBUG",
                    "XML-REASSEMBLY-RESULT", "FINAL-CONTENT",
                    "ASC-RAW-TOKENS", "CNT-RAW-TOKENS",
                ),
                sampleRate = 1,
            ),
        )
        Timber.tag("LOG-MANAGER").i("🔥 core-network 调试日志已启用 - 包含CNET标签和Token日志采集")
    }

    /**
     * 🏷️ 【统一日志标签】测试Core-Network日志系统
     *
     * 使用统一的日志标签管理系统测试core-network日志功能
     */
    fun testCoreNetworkLogging() {
        // 测试新的统一标签系统
        Timber.tag(GymBroLogTags.CoreNetwork.SERVICE_UNIFIED_AI).i("🧪 [测试] 统一AI响应服务日志")
        Timber.tag(GymBroLogTags.CoreNetwork.PROCESSOR_STREAM).d("🧪 [测试] 流式处理器日志")
        Timber.tag(GymBroLogTags.CoreNetwork.OUTPUT_DIRECT).i("🧪 [测试] 直接输出通道日志")
        Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).i("🧪 [测试] REST客户端日志")
        Timber.tag(GymBroLogTags.CoreNetwork.INTERCEPTOR_AUTH).d("🧪 [测试] 认证拦截器日志")
        Timber.tag(GymBroLogTags.CoreNetwork.INTERCEPTOR_RETRY).w("🧪 [测试] 重试拦截器日志")
        Timber.tag(GymBroLogTags.CoreNetwork.CONFIG_MANAGER).d("🧪 [测试] 配置管理器日志")
        Timber.tag(GymBroLogTags.CoreNetwork.MONITOR_PERFORMANCE).i("🧪 [测试] 性能监控日志")
        Timber.tag(GymBroLogTags.CoreNetwork.SECURITY_PII).w("🧪 [测试] PII安全处理日志")
        Timber.tag(GymBroLogTags.CoreNetwork.DI_MODULE).d("🧪 [测试] 依赖注入模块日志")

        // 测试敏感信息过滤
        Timber.tag(GymBroLogTags.CoreNetwork.SECURITY_PII).w("🧪 [测试] 敏感信息过滤: api_key=*****, token=*****")

        Timber.tag(GymBroLogTags.Core.LOG_MANAGER).i("🧪 [测试完成] Core-Network统一日志标签系统测试完成")
    }

    /**
     * 🔄 【兼容性】测试旧版网络日志标签（逐步迁移）
     *
     * @deprecated 使用 testCoreNetworkLogging() 替代
     */
    @Deprecated("使用 testCoreNetworkLogging() 替代", ReplaceWith("testCoreNetworkLogging()"))
    fun testNetworkLogging() {
        // 兼容旧标签测试
        Timber.tag(GymBroLogTags.CoreNetwork.ERROR).e("🧪 [兼容测试] CNET-ERROR标签测试")
        Timber.tag(GymBroLogTags.CoreNetwork.STREAM).d("🧪 [兼容测试] CNET-STREAM标签测试")
        Timber.tag(GymBroLogTags.CoreNetwork.CHECK).i("🧪 [兼容测试] CNET-CHECK标签测试")

        Timber.tag(GymBroLogTags.Core.LOG_MANAGER).w("🔄 [迁移提醒] 请使用 testCoreNetworkLogging() 方法")
    }

    /**
     * 运行时切换环境
     */

    /**
     * 获取当前环境
     */
    fun getCurrentEnvironment(): LoggingConfig.Environment = loggingConfig.getCurrentEnvironment()

    /**
     * 🔥 【修复】动态加载ThinkingBoxLogTree作为主要日志处理器
     *
     * 替换ThinkingBoxAwareTree，使用功能更完整的ThinkingBoxLogTree
     * 🔥 【Token流修复】确保ThinkingBox相关日志能够正确输出，包含所有修复验证功能
     */
    private fun loadThinkingBoxLogTree() {
        try {
            // 动态加载ThinkingBoxLogTree类
            val thinkingBoxLogTreeClass = Class.forName(
                "com.example.gymbro.features.thinkingbox.logging.ThinkingBoxLogTree",
            )

            // 🔥 【修复】ThinkingBoxLogTree使用无参构造函数（继承自Timber.DebugTree）
            val constructor = thinkingBoxLogTreeClass.getConstructor()
            val thinkingBoxLogTree = constructor.newInstance() as Timber.Tree

            // 植入ThinkingBoxLogTree作为主要日志处理器
            Timber.plant(thinkingBoxLogTree)

            Timber.tag("LOG-MANAGER").i("🔥 ThinkingBoxLogTree 已加载作为主要日志处理器 - 包含Token流修复调试功能")
        } catch (e: Exception) {
            Timber.tag("LOG-MANAGER").w("ThinkingBoxLogTree 加载失败，回退到基础日志: ${e.message}")
            // 回退到基础日志树
            Timber.plant(Timber.DebugTree())
        }
    }

    /**
     * 🔥 【新增】动态加载NetworkLogTree
     *
     * 遵循现有的模块化日志架构，动态加载网络模块的专用日志树
     * 🔥 【架构集成】使用LoggingConfig统一管理，与其他模块保持一致
     */
    private fun loadNetworkLogTree() {
        try {
            // 动态加载NetworkLogTree类
            val networkLogTreeClass = Class.forName(
                "com.example.gymbro.core.network.logging.NetworkLogTree",
            )

            // 🔥 【架构集成】使用LoggingConfig作为构造参数，与WorkoutLogTree保持一致
            val constructor = networkLogTreeClass.getConstructor(LoggingConfig::class.java)
            val networkLogTree = constructor.newInstance(loggingConfig) as Timber.Tree

            // 植入NetworkLogTree
            Timber.plant(networkLogTree)

            Timber.tag(
                "LOG-MANAGER",
            ).i("🔥 NetworkLogTree 已加载 - 环境: ${loggingConfig.getCurrentEnvironment()}")
        } catch (e: Exception) {
            Timber.tag("LOG-MANAGER").w("NetworkLogTree 加载失败: ${e.message}")
            // 网络日志树加载失败不影响其他功能，继续运行
        }
    }

    /**
     * 设置全局日志标签过滤器
     *
     * @param tagFilter 标签过滤函数
     */
    fun setGlobalTagFilter(tagFilter: (String?) -> String) {
        globalTagFilter = tagFilter
    }

    companion object {
        private const val LOG_STACK_INDICATOR = "LogStack"

        // 全局标签过滤器
        @Volatile
        private var globalTagFilter: ((String?) -> String)? = null

        /**
         * 应用全局标签过滤器
         */
        internal fun applyTagFilter(
            tag: String?,
        ): String = globalTagFilter?.invoke(tag) ?: tag ?: "GymBro"

        /**
         * 基于当前类名生成的标签
         */
        @SuppressLint("DefaultLocale")
        fun createTag(): String {
            val stackTrace = Thread.currentThread().stackTrace
            for (element in stackTrace) {
                val className = element.className
                if (!className.contains("TimberManager") &&
                    !className.contains("Thread") &&
                    !className.contains("VMStack") &&
                    !className.contains("Method") &&
                    !className.startsWith("java.") &&
                    !className.startsWith("android.") &&
                    !className.startsWith("dalvik.")
                ) {
                    val fullClassName = className.substringAfterLast('.')
                    return fullClassName.substring(fullClassName.lastIndexOf('.') + 1)
                }
            }
            return "Unknown"
        }
    }
}
