package com.example.gymbro.features.thinkingbox.internal.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * ThinkingBoxReducer测试套件
 *
 * 测试覆盖：
 * - MVI架构合规性验证（纯函数、状态不可变）
 * - Intent处理逻辑完整性
 * - 状态转换正确性
 * - Effect生成验证
 * - 错误处理和边界条件
 */
@DisplayName("ThinkingBoxReducer")
class ThinkingBoxReducerTest {

    private lateinit var segmentQueueReducer: SegmentQueueReducer
    private lateinit var thinkingBoxReducer: ThinkingBoxReducer

    private val testMessageId = "test-message-123"
    private val testSegmentId = "test-segment-456"

    @BeforeEach
    fun setUp() {
        segmentQueueReducer = mockk(relaxed = true)
        thinkingBoxReducer = ThinkingBoxReducer(segmentQueueReducer)
    }

    @Nested
    @DisplayName("初始化处理")
    inner class InitializeHandling {

        @Test
        @DisplayName("首次初始化应该设置messageId并启动Token流监听")
        fun `初始化时应该设置messageId并启动Token流监听`() = runTest {
            // Given
            val initialState = ThinkingBoxContract.State()
            val intent = ThinkingBoxContract.Intent.Initialize(testMessageId)

            // When
            val result = thinkingBoxReducer.reduceInternal(intent, initialState)

            // Then
            assertEquals(testMessageId, result.state.messageId)
            assertFalse(result.state.isLoading)
            assertTrue(result.state.segmentsQueue.isEmpty())

            // 验证Effect
            assertEquals(1, result.effects.size)
            val effect = result.effects.first()
            assertTrue(effect is ThinkingBoxContract.Effect.StartTokenStreamListening)
            assertEquals(
                testMessageId,
                (effect as ThinkingBoxContract.Effect.StartTokenStreamListening).messageId,
            )
        }

        @Test
        @DisplayName("重复初始化相同messageId应该无操作")
        fun `重复初始化相同messageId应该无操作`() = runTest {
            // Given - 先初始化一次
            val initialState = ThinkingBoxContract.State()
            val firstIntent = ThinkingBoxContract.Intent.Initialize(testMessageId)
            thinkingBoxReducer.reduceInternal(firstIntent, initialState)

            // When - 再次初始化相同messageId
            val currentState = ThinkingBoxContract.State(messageId = testMessageId)
            val result = thinkingBoxReducer.reduceInternal(firstIntent, currentState)

            // Then - 应该返回无变化结果
            assertEquals(currentState, result.state)
            assertTrue(result.effects.isEmpty())
        }

        @Test
        @DisplayName("初始化不同messageId应该重置状态")
        fun `初始化不同messageId应该重置状态`() = runTest {
            // Given - 已有状态
            val existingState = ThinkingBoxContract.State(
                messageId = "old-message",
                segmentsQueue = listOf(
                    ThinkingBoxContract.SegmentUi("old-seg", SegmentKind.PHASE, "Old", "content", true),
                ),
            )
            val newIntent = ThinkingBoxContract.Intent.Initialize(testMessageId)

            // When
            val result = thinkingBoxReducer.reduceInternal(newIntent, existingState)

            // Then
            assertEquals(testMessageId, result.state.messageId)
            assertTrue(result.state.segmentsQueue.isEmpty()) // 队列应该被重置
            assertFalse(result.state.thinkingClosed)
            assertFalse(result.state.finalReady)
        }
    }

    @Nested
    @DisplayName("重置处理")
    inner class ResetHandling {

        @Test
        @DisplayName("重置应该清空所有状态")
        fun `重置应该清空所有状态`() = runTest {
            // Given - 有复杂状态
            val complexState = ThinkingBoxContract.State(
                messageId = testMessageId,
                segmentsQueue = listOf(
                    ThinkingBoxContract.SegmentUi("seg1", SegmentKind.PHASE, "Title", "content", true),
                    ThinkingBoxContract.SegmentUi("seg2", SegmentKind.PERTHINK, null, "more content", false),
                ),
                finalReady = true,
                finalContent = "Final answer here",
                thinkingClosed = true,
                isLoading = true,
            )
            val resetIntent = ThinkingBoxContract.Intent.Reset

            // When
            val result = thinkingBoxReducer.reduceInternal(resetIntent, complexState)

            // Then - 所有状态应该被重置为默认值
            assertEquals("", result.state.messageId)
            assertTrue(result.state.segmentsQueue.isEmpty())
            assertFalse(result.state.finalReady)
            assertEquals("", result.state.finalContent)
            assertFalse(result.state.thinkingClosed)
            assertFalse(result.state.isLoading)
            assertNull(result.state.error)

            // 不应该产生任何Effect
            assertTrue(result.effects.isEmpty())
        }
    }

    @Nested
    @DisplayName("UI段渲染完成处理")
    inner class UiSegmentRenderedHandling {

        @Test
        @DisplayName("UI段渲染完成应该委托给SegmentQueueReducer")
        fun `UI段渲染完成应该委托给SegmentQueueReducer`() = runTest {
            // Given
            val currentState = ThinkingBoxContract.State(messageId = testMessageId)
            val intent = ThinkingBoxContract.Intent.UiSegmentRendered(testSegmentId)

            val expectedTBState = SegmentQueueReducer.TBState(messageId = testMessageId)
            val expectedResult = SegmentQueueReducer.ReduceResult(
                state = expectedTBState,
                effects = listOf(ThinkingBoxContract.Effect.ScrollToBottom),
            )

            every { segmentQueueReducer.reduce(any(), any()) } returns expectedResult

            // When
            val result = thinkingBoxReducer.reduceInternal(intent, currentState)

            // Then - 验证调用了SegmentQueueReducer
            verify {
                segmentQueueReducer.reduce(
                    any(),
                    match<ThinkingEvent> { it is ThinkingEvent.UiSegmentRendered && it.segmentId == testSegmentId },
                )
            }

            // 验证Effect被正确传递
            assertEquals(1, result.effects.size)
            assertTrue(result.effects.first() is ThinkingBoxContract.Effect.ScrollToBottom)
        }
    }

    @Nested
    @DisplayName("错误处理")
    inner class ErrorHandling {

        @Test
        @DisplayName("清除错误应该移除error状态")
        fun `清除错误应该移除error状态`() = runTest {
            // Given - 有错误状态
            val errorState = ThinkingBoxContract.State(
                messageId = testMessageId,
                error = com.example.gymbro.core.ui.text.UiText.StringResource(
                    com.example.gymbro.core.ui.text.UiText.StringResource.ResId(android.R.string.unknownName),
                ),
            )
            val clearErrorIntent = ThinkingBoxContract.Intent.ClearError

            // When
            val result = thinkingBoxReducer.reduceInternal(clearErrorIntent, errorState)

            // Then
            assertNull(result.state.error)
            assertEquals(testMessageId, result.state.messageId) // 其他状态保持不变
            assertTrue(result.effects.isEmpty()) // 不产生Effect
        }

        @Test
        @DisplayName("清除错误对无错误状态应该无影响")
        fun `清除错误对无错误状态应该无影响`() = runTest {
            // Given - 无错误状态
            val normalState = ThinkingBoxContract.State(messageId = testMessageId)
            val clearErrorIntent = ThinkingBoxContract.Intent.ClearError

            // When
            val result = thinkingBoxReducer.reduceInternal(clearErrorIntent, normalState)

            // Then
            assertNull(result.state.error)
            assertEquals(normalState.messageId, result.state.messageId)
        }
    }

    @Nested
    @DisplayName("ThinkingEvent处理")
    inner class ThinkingEventHandling {

        @Test
        @DisplayName("ThinkingEvent应该委托给SegmentQueueReducer")
        fun `ThinkingEvent应该委托给SegmentQueueReducer`() = runTest {
            // Given
            val currentState = ThinkingBoxContract.State(messageId = testMessageId)
            val thinkingEvent = ThinkingEvent.SegmentStarted("test-seg", SegmentKind.PHASE, "测试段")

            val expectedTBState = SegmentQueueReducer.TBState(messageId = testMessageId)
            val expectedResult = SegmentQueueReducer.ReduceResult(
                state = expectedTBState,
                effects = listOf(ThinkingBoxContract.Effect.LogDebug("段创建: test-seg")),
            )

            every { segmentQueueReducer.reduce(any(), thinkingEvent) } returns expectedResult

            // When
            val result = thinkingBoxReducer.handleThinkingEvent(thinkingEvent, currentState)

            // Then
            verify { segmentQueueReducer.reduce(any(), thinkingEvent) }
            assertEquals(1, result.effects.size)
            assertTrue(result.effects.first() is ThinkingBoxContract.Effect.LogDebug)
        }

        @Test
        @DisplayName("复杂ThinkingEvent序列应该正确处理")
        fun `复杂ThinkingEvent序列应该正确处理`() = runTest {
            // Given - 模拟完整的思考流程
            val initialState = ThinkingBoxContract.State(messageId = testMessageId)
            val events = listOf(
                ThinkingEvent.SegmentStarted("analysis", SegmentKind.PHASE, "问题分析"),
                ThinkingEvent.SegmentText("分析用户问题..."),
                ThinkingEvent.SegmentClosed("analysis"),
                ThinkingEvent.ThinkingClosed,
            )

            // Mock SegmentQueueReducer返回不同的状态
            var stateVersion = 1L
            every { segmentQueueReducer.reduce(any(), any()) } answers {
                SegmentQueueReducer.ReduceResult(
                    state = SegmentQueueReducer.TBState(
                        messageId = testMessageId,
                        version = stateVersion++,
                    ),
                )
            }

            // When - 依次处理所有事件
            var currentState = initialState
            events.forEach { event ->
                val result = thinkingBoxReducer.handleThinkingEvent(event, currentState)
                currentState = result.state
            }

            // Then - 验证所有事件都被处理
            verify(exactly = events.size) { segmentQueueReducer.reduce(any(), any()) }
        }
    }

    @Nested
    @DisplayName("状态转换验证")
    inner class StateConversionVerification {

        @Test
        @DisplayName("内部TBState应该正确转换为Contract State")
        fun `内部TBState应该正确转换为Contract State`() = runTest {
            // Given - 初始化Reducer并获取内部状态
            val initialState = ThinkingBoxContract.State()
            val initIntent = ThinkingBoxContract.Intent.Initialize(testMessageId)

            // When
            val result = thinkingBoxReducer.reduceInternal(initIntent, initialState)

            // Then - 验证状态转换正确
            assertEquals(testMessageId, result.state.messageId)
            assertTrue(result.state.segmentsQueue.isEmpty())
            assertFalse(result.state.finalReady)
            assertEquals("", result.state.finalContent)
            assertFalse(result.state.thinkingClosed)
        }
    }

    @Nested
    @DisplayName("MVI架构合规性")
    inner class MVIArchitectureCompliance {

        @Test
        @DisplayName("Reducer应该是纯函数 - 相同输入产生相同输出")
        fun `Reducer应该是纯函数`() = runTest {
            // Given - 相同的输入
            val state = ThinkingBoxContract.State()
            val intent = ThinkingBoxContract.Intent.Initialize(testMessageId)

            // When - 多次调用
            val result1 = thinkingBoxReducer.reduceInternal(intent, state)
            val result2 = thinkingBoxReducer.reduceInternal(intent, state)

            // Then - 应该产生相同结果
            assertEquals(result1.state.messageId, result2.state.messageId)
            assertEquals(result1.effects.size, result2.effects.size)
        }

        @Test
        @DisplayName("状态应该是不可变的")
        fun `状态应该是不可变的`() = runTest {
            // Given
            val originalState = ThinkingBoxContract.State(
                messageId = "original",
                segmentsQueue = listOf(
                    ThinkingBoxContract.SegmentUi("seg1", SegmentKind.PHASE, "Title", "content", true),
                ),
            )
            val intent = ThinkingBoxContract.Intent.Reset

            // When
            val result = thinkingBoxReducer.reduceInternal(intent, originalState)

            // Then - 原始状态不应该被修改
            assertEquals("original", originalState.messageId)
            assertEquals(1, originalState.segmentsQueue.size)

            // 新状态应该是不同的实例
            assertNotNull(result.state)
            assertEquals("", result.state.messageId)
            assertTrue(result.state.segmentsQueue.isEmpty())
        }

        @Test
        @DisplayName("所有Intent类型都应该被处理")
        fun `所有Intent类型都应该被处理`() = runTest {
            val state = ThinkingBoxContract.State()
            val intents = listOf(
                ThinkingBoxContract.Intent.Initialize(testMessageId),
                ThinkingBoxContract.Intent.Reset,
                ThinkingBoxContract.Intent.UiSegmentRendered(testSegmentId),
                ThinkingBoxContract.Intent.ClearError,
            )

            // When & Then - 所有Intent都应该能被处理而不抛异常
            intents.forEach { intent ->
                kotlin.runCatching {
                    thinkingBoxReducer.reduceInternal(intent, state)
                }.onFailure { error ->
                    throw AssertionError("Intent ${intent::class.simpleName} 处理失败: ${error.message}")
                }
            }
        }
    }

    @Nested
    @DisplayName("边界条件和异常处理")
    inner class BoundaryConditionsAndExceptionHandling {

        @Test
        @DisplayName("空messageId初始化应该正常处理")
        fun `空messageId初始化应该正常处理`() = runTest {
            // Given
            val state = ThinkingBoxContract.State()
            val intent = ThinkingBoxContract.Intent.Initialize("")

            // When & Then - 不应该抛异常
            val result = thinkingBoxReducer.reduceInternal(intent, state)
            assertEquals("", result.state.messageId)
        }

        @Test
        @DisplayName("空segmentId的UI渲染完成应该正常处理")
        fun `空segmentId的UI渲染完成应该正常处理`() = runTest {
            // Given
            val state = ThinkingBoxContract.State()
            val intent = ThinkingBoxContract.Intent.UiSegmentRendered("")

            // When & Then - 不应该抛异常
            kotlin.runCatching {
                thinkingBoxReducer.reduceInternal(intent, state)
            }.onFailure { error ->
                throw AssertionError("空segmentId处理失败: ${error.message}")
            }
        }
    }

    @Nested
    @DisplayName("性能和资源管理")
    inner class PerformanceAndResourceManagement {

        @Test
        @DisplayName("大量状态转换不应该导致内存泄漏")
        fun `大量状态转换不应该导致内存泄漏`() = runTest {
            // Given
            val initialState = ThinkingBoxContract.State()

            // When - 大量重复操作
            repeat(1000) { i ->
                val intent = ThinkingBoxContract.Intent.Initialize("message-$i")
                thinkingBoxReducer.reduceInternal(intent, initialState)
            }

            // Then - 应该正常完成而不抛OutOfMemoryError
            val finalIntent = ThinkingBoxContract.Intent.Reset
            val result = thinkingBoxReducer.reduceInternal(finalIntent, initialState)
            assertNotNull(result)
        }
    }
}