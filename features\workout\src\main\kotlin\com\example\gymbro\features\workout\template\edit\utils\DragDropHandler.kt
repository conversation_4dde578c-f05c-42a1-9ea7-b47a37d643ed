package com.example.gymbro.features.workout.template.edit.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import com.example.gymbro.features.workout.shared.components.drag.DragAnimations
import com.example.gymbro.features.workout.shared.components.drag.DragConfig
import com.example.gymbro.features.workout.shared.components.drag.DragResult
import com.example.gymbro.features.workout.shared.components.drag.DragState
import com.example.gymbro.features.workout.shared.components.drag.UnifiedDragHandler
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import timber.log.Timber

/**
 * 拖拽排序处理工具类 (统一拖拽适配器)
 *
 * 完全基于UnifiedDragHandler重构，保持现有API完全兼容，
 * 增强Material3动画效果、触觉反馈和性能优化。
 *
 * ✨ 迁移完成特性：
 * - 🎯 100%兼容现有TemplateEditContract API
 * - ⚡ UnifiedDragHandler核心引擎
 * - 🎨 Material3标准动画效果
 * - 📱 智能触觉反馈系统
 * - 🚀 高性能列表重排序算法
 * - 🔧 泛型类型安全设计
 *
 * 设计原则：
 * - 向后兼容：所有现有函数签名保持不变
 * - 增强功能：集成Material3动画和触觉反馈
 * - 纯函数式：无副作用，状态不可变
 * - 性能优先：优化列表操作和动画性能
 */
object DragDropHandler {

    /**
     * 创建统一拖拽状态实例 - 完全兼容TemplateEditContract
     *
     * 直接使用TemplateEditContract.State中的createDragState()方法，
     * 确保状态转换的一致性和可维护性。
     */
    private fun createInternalDragState(
        state: TemplateEditContract.State
    ): DragState<TemplateExerciseDto> {
        return state.createDragState()
    }

    /**
     * 将拖拽状态应用回TemplateEditContract.State
     *
     * 使用TemplateEditContract.State中的updateFromDragState()方法，
     * 确保状态同步的准确性，并保持未保存更改的标记。
     */
    private fun applyDragStateToTemplateState(
        templateState: TemplateEditContract.State,
        dragState: DragState<TemplateExerciseDto>
    ): TemplateEditContract.State {
        return templateState.updateFromDragState(dragState).copy(
            // 🔥 拖拽时自动标记为有未保存更改
            hasUnsavedChanges = templateState.hasUnsavedChanges || dragState.isDragInProgress
        )
    }

    /**
     * 获取Material3拖拽配置
     *
     * 根据当前状态和用户偏好返回最适合的拖拽配置。
     * 支持动态配置切换以优化不同场景的用户体验。
     */
    private fun getDragConfig(state: TemplateEditContract.State): DragConfig {
        return state.dragConfig.takeIf { it != DragConfig.Companion.Material3 }
            ?: DragConfig.Companion.Material3
    }
    /**
     * 处理拖拽开始事件 - 增强版
     *
     * ✨ 新增功能：
     * - Material3动画支持
     * - 智能触觉反馈
     * - 位置信息记录
     * - 完善的错误处理
     *
     * @param state 当前状态
     * @param exerciseId 被拖拽的动作ID
     * @param startIndex 开始拖拽的索引位置
     * @param startPosition 可选的开始位置（支持Material3动画）
     * @return 更新后的状态
     */
    fun handleDragStart(
        state: TemplateEditContract.State,
        exerciseId: String,
        startIndex: Int,
        startPosition: Offset = Offset.Companion.Zero,
    ): TemplateEditContract.State {
        Timber.Forest.d("拖拽开始: exerciseId=$exerciseId, startIndex=$startIndex, position=$startPosition")

        // 创建当前拖拽状态
        val currentDragState = createInternalDragState(state)

        // 查找被拖拽的动作
        val draggedExercise = state.exercises.find { it.id == exerciseId }

        if (draggedExercise == null) {
            Timber.Forest.w("拖拽失败: 未找到动作 exerciseId=$exerciseId")
            return state
        }

        // 验证索引有效性
        if (startIndex < 0 || startIndex >= state.exercises.size) {
            Timber.Forest.w("拖拽失败: 索引无效 startIndex=$startIndex, exerciseCount=${state.exercises.size}")
            return state
        }

        // 使用UnifiedDragHandler处理拖拽开始
        val newDragState = UnifiedDragHandler.handleDragStart(
            dragState = currentDragState,
            item = draggedExercise,
            itemId = exerciseId,
            startIndex = startIndex,
            startPosition = startPosition
        )

        // 转换回模板状态并应用增强配置
        return applyDragStateToTemplateState(state, newDragState).copy(
            hasUnsavedChanges = true,
            // 🎯 Material3动画配置应用
            dragConfig = getDragConfig(state),
            // 📱 启用触觉反馈标记
            shouldTriggerHaptic = true
        )
    }

    /**
     * 处理拖拽位置更新事件 - 增强版
     *
     * ✨ 新增功能：
     * - 智能位置计算
     * - 触觉反馈触发
     * - 预览状态管理
     * - 性能优化的状态更新
     *
     * @param state 当前状态
     * @param targetIndex 目标索引位置
     * @param offset 拖拽偏移量
     * @param currentPosition 可选的当前位置（支持2D拖拽）
     * @return 更新后的状态
     */
    fun handleDragUpdate(
        state: TemplateEditContract.State,
        targetIndex: Int,
        offset: Float,
        currentPosition: Offset = Offset(0f, offset),
    ): TemplateEditContract.State {
        if (!state.isDragInProgress) {
            return state
        }

        // 创建当前拖拽状态
        val currentDragState = createInternalDragState(state)

        // 使用UnifiedDragHandler处理拖拽移动
        val newDragState = UnifiedDragHandler.handleDragMove(
            dragState = currentDragState,
            currentPosition = currentPosition,
            targetIndex = targetIndex,
            itemCount = state.exercises.size
        )

        // 转换回模板状态
        return applyDragStateToTemplateState(state, newDragState)
    }

    /**
     * 处理拖拽完成事件 - 增强版
     *
     * ✨ 新增功能：
     * - 完整的结果处理
     * - 智能错误恢复
     * - 性能优化的列表重排序
     * - 完成状态清理
     *
     * @param state 当前状态
     * @param fromIndex 起始索引
     * @param toIndex 目标索引
     * @return 更新后的状态
     */
    fun handleDragComplete(
        state: TemplateEditContract.State,
        fromIndex: Int,
        toIndex: Int,
    ): TemplateEditContract.State {
        Timber.Forest.d("拖拽完成: fromIndex=$fromIndex, toIndex=$toIndex")

        // 创建当前拖拽状态
        val currentDragState = createInternalDragState(state)

        // 使用UnifiedDragHandler处理拖拽完成
        val dragResult = UnifiedDragHandler.handleDragComplete(
            dragState = currentDragState,
            items = state.exercises,
            fromIndex = fromIndex,
            toIndex = toIndex
        )

        // 根据拖拽结果更新状态
        return when (dragResult.result) {
            is DragResult.Success -> {
                Timber.Forest.d("拖拽成功完成: ${dragResult.result.item.id}")
                applyDragStateToTemplateState(state, dragResult.newState).copy(
                    exercises = dragResult.reorderedItems,
                    hasUnsavedChanges = true,
                    // 🎯 清理拖拽相关的UI状态
                    shouldTriggerHaptic = false,
                    showDropPreview = false
                )
            }
            is DragResult.Cancelled -> {
                Timber.Forest.d("拖拽被取消: ${dragResult.result.reason}")
                applyDragStateToTemplateState(state, dragResult.newState)
            }
            is DragResult.Failed -> {
                Timber.Forest.w("拖拽失败: ${dragResult.result.errorMessage}")
                applyDragStateToTemplateState(state, dragResult.newState)
            }
        }
    }

    /**
     * 处理拖拽取消事件 - 增强版
     *
     * ✨ 新增功能：
     * - 智能取消原因记录
     * - 状态完全清理
     * - 触觉反馈支持
     *
     * @param state 当前状态
     * @param reason 可选的取消原因
     * @return 更新后的状态
     */
    fun handleDragCancel(
        state: TemplateEditContract.State,
        reason: String = "用户取消",
    ): TemplateEditContract.State {
        Timber.Forest.d("拖拽取消: reason=$reason")

        // 创建当前拖拽状态
        val currentDragState = createInternalDragState(state)

        // 使用UnifiedDragHandler处理拖拽取消
        val newDragState = UnifiedDragHandler.handleDragCancel(
            dragState = currentDragState,
            reason = reason
        )

        // 转换回模板状态并清理所有拖拽相关状态
        return applyDragStateToTemplateState(state, newDragState).copy(
            shouldTriggerHaptic = false,
            showDropPreview = false,
            dropPreviewIndex = -1
        )
    }

    // === 向后兼容性方法（保持现有API不变） ===

    /**
     * 检查是否可以开始拖拽 - 保持兼容
     *
     * @param state 当前状态
     * @param exerciseId 动作ID
     * @return 是否可以拖拽
     */
    fun canStartDrag(
        state: TemplateEditContract.State,
        exerciseId: String,
    ): Boolean {
        // 创建当前拖拽状态用于检查
        val currentDragState = createInternalDragState(state)

        return UnifiedDragHandler.canStartDrag(
            dragState = currentDragState,
            itemId = exerciseId,
            items = state.exercises,
            getId = { it.id }
        )
    }

    /**
     * 获取拖拽状态摘要信息 - 保持兼容
     *
     * @param state 当前状态
     * @return 拖拽状态摘要
     */
    fun getDragStateSummary(state: TemplateEditContract.State): String {
        val currentDragState = createInternalDragState(state)
        return UnifiedDragHandler.getDragStateSummary(currentDragState)
    }

    // === 新增增强功能方法 ===

    /**
     * 获取增强的拖拽状态信息（新增功能）
     *
     * 提供更详细的拖拽状态信息，用于调试和监控
     */
    fun getEnhancedDragInfo(state: TemplateEditContract.State): Map<String, Any> {
        val currentDragState = createInternalDragState(state)
        return mapOf(
            "isDragInProgress" to currentDragState.isDragInProgress,
            "draggedItemId" to (currentDragState.draggedItemId ?: "none"),
            "draggedItemIndex" to currentDragState.draggedItemIndex,
            "dragTargetIndex" to currentDragState.dragTargetIndex,
            "dragDistance" to currentDragState.dragDistance,
            "isDragEnabled" to currentDragState.isDragEnabled,
            "shouldTriggerHaptic" to currentDragState.shouldTriggerHaptic,
            "showDropPreview" to currentDragState.showDropPreview,
            "animationProgress" to currentDragState.animationProgress,
            "dragConfig" to state.dragConfig.toString()
        )
    }

    /**
     * 创建Material3拖拽修饰符（新增功能）
     *
     * 为UI组件提供Material3标准的拖拽动画效果
     */
    @Composable
    fun createDragModifier(
        state: TemplateEditContract.State,
        itemId: String
    ): Modifier {
        val currentDragState = createInternalDragState(state)
        val isCurrentlyDragged = currentDragState.isDragging(itemId)

        return if (isCurrentlyDragged) {
            DragAnimations.dragAnimated(
                modifier = Modifier.Companion,
                dragState = currentDragState,
                config = getDragConfig(state)
            )
        } else {
            Modifier.Companion
        }
    }

    /**
     * 验证拖拽配置（新增功能）
     *
     * 确保拖拽配置的有效性和合理性
     */
    fun validateDragConfig(config: DragConfig): Boolean {
        return config.dragThreshold > 0f &&
               config.animationDuration > 0 &&
               config.dragScale > 0f &&
               config.dragAlpha in 0f..1f
    }

    /**
     * 优化拖拽性能配置（新增功能）
     *
     * 根据设备性能和列表大小动态调整拖拽配置
     */
    fun getOptimizedDragConfig(
        exerciseCount: Int,
        isHighPerformanceDevice: Boolean = true
    ): DragConfig {
        return when {
            exerciseCount > 50 -> DragConfig.Companion.Lightweight
            !isHighPerformanceDevice -> DragConfig.Companion.Lightweight
            exerciseCount > 20 -> DragConfig.Companion.Material3
            else -> DragConfig.Companion.Enhanced
        }
    }
}
