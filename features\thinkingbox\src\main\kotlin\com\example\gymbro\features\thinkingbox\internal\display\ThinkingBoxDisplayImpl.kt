package com.example.gymbro.features.thinkingbox.internal.display

import com.example.gymbro.features.thinkingbox.api.ThinkingBoxCompletionListener
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxDisplay
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingBoxDisplay接口的实现
 *
 * 负责管理ThinkingBox的显示生命周期和完成回调。
 * 遵循简化的职责分离原则：专注于显示控制和状态监听。
 *
 * @since Coach-ThinkingBox重构
 */
@Singleton
class ThinkingBoxDisplayImpl @Inject constructor(
    private val viewModelProvider:
    com.example.gymbro.features.thinkingbox.internal.provider.ThinkingBoxViewModelProvider,
) : ThinkingBoxDisplay {

    // 存储活跃的显示会话
    private val activeSessions = mutableMapOf<String, DisplaySession>()

    override fun startDisplaying(
        messageId: String,
        completionListener: ThinkingBoxCompletionListener,
    ) {
        Timber.d("TB-Display: 🚀 [ThinkingBoxDisplay] 启动显示: messageId=$messageId")

        try {
            // 如果已有相同messageId的会话，先停止它
            stopDisplaying(messageId)

            // 创建新的显示会话
            val session = DisplaySession(
                messageId = messageId,
                completionListener = completionListener,
            )

            activeSessions[messageId] = session
            session.start()

            Timber.d("TB-Display: ✅ [ThinkingBoxDisplay] 显示会话已启动: messageId=$messageId")
        } catch (e: Exception) {
            Timber.e(e, "TB-Display: ❌ [ThinkingBoxDisplay] 启动显示失败: messageId=$messageId")
            completionListener.onDisplayError(messageId, e, null)
        }
    }

    override fun stopDisplaying(messageId: String) {
        Timber.d("TB-Display: 🛑 [ThinkingBoxDisplay] 停止显示: messageId=$messageId")

        val session = activeSessions.remove(messageId)
        session?.stop()

        Timber.d("TB-Display: ✅ [ThinkingBoxDisplay] 显示会话已停止: messageId=$messageId")
    }

    override fun isDisplaying(messageId: String): Boolean {
        return activeSessions.containsKey(messageId)
    }

    override fun getActiveDisplays(): List<String> {
        return activeSessions.keys.toList()
    }

    override fun clearAllDisplays() {
        Timber.d("TB-Display: 🧹 [ThinkingBoxDisplay] 清理所有显示会话")

        activeSessions.values.forEach { session ->
            session.stop()
        }
        activeSessions.clear()

        Timber.d("TB-Display: ✅ [ThinkingBoxDisplay] 所有显示会话已清理")
    }

    /**
     * 内部显示会话类
     * 管理单个ThinkingBox显示的生命周期
     */
    private inner class DisplaySession(
        private val messageId: String,
        private val completionListener: ThinkingBoxCompletionListener,
    ) {
        private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
        private var isCompleted = false

        fun start() {
            Timber.d("TB-Display: 🎬 [DisplaySession] 开始监听: messageId=$messageId")

            // 启动状态监听
            scope.launch {
                try {
                    // TODO: 这里需要监听ThinkingBox的完成状态
                    // 由于当前ThinkingBox架构复杂，暂时使用模拟实现
                    // 在实际集成时，需要监听ThinkingBoxViewModel的状态变化

                    // 模拟监听ThinkingBox完成状态
                    monitorThinkingBoxCompletion()
                } catch (e: Exception) {
                    Timber.e(e, "TB-Display: ❌ [DisplaySession] 监听异常: messageId=$messageId")
                    if (!isCompleted) {
                        isCompleted = true
                        completionListener.onDisplayError(messageId, e, null)
                    }
                }
            }
        }

        fun stop() {
            Timber.d("TB-Display: 🛑 [DisplaySession] 停止会话: messageId=$messageId")
            scope.cancel()
        }

        /**
         * 监听ThinkingBox完成状态（真实实现）
         */
        private suspend fun monitorThinkingBoxCompletion() {
            Timber.d("TB-Display: 🔍 [DisplaySession] 开始监听ThinkingBox完成状态: messageId=$messageId")

            try {
                // 获取ThinkingBoxViewModel的状态流
                val stateFlow = viewModelProvider.getStateFlow(messageId)

                if (stateFlow == null) {
                    Timber.w("TB-Display: ⚠️ [DisplaySession] 未找到ViewModel状态流: messageId=$messageId")
                    // 如果没有找到ViewModel，等待一段时间后重试
                    kotlinx.coroutines.delay(1000)
                    val retryStateFlow = viewModelProvider.getStateFlow(messageId)

                    if (retryStateFlow == null) {
                        Timber.e("TB-Display: ❌ [DisplaySession] 重试后仍未找到ViewModel: messageId=$messageId")
                        if (!isCompleted) {
                            isCompleted = true
                            completionListener.onDisplayError(
                                messageId = messageId,
                                error = Exception("ThinkingBox ViewModel not found"),
                                partialResult = null,
                            )
                        }
                        return
                    }

                    // 使用重试获得的状态流
                    monitorStateFlow(retryStateFlow)
                } else {
                    // 使用找到的状态流
                    monitorStateFlow(stateFlow)
                }
            } catch (e: Exception) {
                Timber.e(e, "TB-Display: ❌ [DisplaySession] 监听状态流异常: messageId=$messageId")
                if (!isCompleted) {
                    isCompleted = true
                    completionListener.onDisplayError(messageId, e, null)
                }
            }
        }

        /**
         * 监听状态流变化
         */
        private suspend fun monitorStateFlow(
            stateFlow:
            kotlinx.coroutines.flow.StateFlow<com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract.State>,
        ) {
            stateFlow.collect { state ->
                Timber.d(
                    "TB-Display: 🔄 [DisplaySession] 状态更新: messageId=$messageId, finalReady=${state.finalReady}, thinkingClosed=${state.thinkingClosed}",
                )

                // 检查是否完成：finalReady为true且有最终内容，或者思考已关闭且有内容
                val isComplete = (state.finalReady && state.finalContent.isNotEmpty()) ||
                    (state.thinkingClosed && state.finalContent.isNotEmpty())

                if (isComplete && !isCompleted) {
                    isCompleted = true

                    // 提取思考过程
                    val thinkingProcess = extractThinkingProcess(state)

                    // 提取元数据
                    val metadata = extractMetadata(state)

                    Timber.d(
                        "TB-Display: ✅ [DisplaySession] ThinkingBox完成: messageId=$messageId, finalContent长度=${state.finalContent.length}",
                    )

                    completionListener.onDisplayComplete(
                        messageId = messageId,
                        thinkingProcess = thinkingProcess,
                        finalContent = state.finalContent,
                        metadata = metadata,
                    )

                    // 完成后停止监听
                    return@collect
                }
            }
        }

        /**
         * 提取思考过程
         */
        private fun extractThinkingProcess(
            state: com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract.State,
        ): String {
            return buildString {
                state.segmentsQueue.forEach { segment ->
                    if (segment.title != null) {
                        appendLine("## ${segment.title}")
                    }
                    appendLine(segment.content)
                    appendLine()
                }
            }
        }

        /**
         * 提取元数据
         */
        private fun extractMetadata(
            state: com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract.State,
        ): Map<String, Any> {
            return mapOf(
                "segmentCount" to state.segmentsQueue.size,
                "thinkingClosed" to state.thinkingClosed,
                "finalReady" to state.finalReady,
            )
        }
    }
}
