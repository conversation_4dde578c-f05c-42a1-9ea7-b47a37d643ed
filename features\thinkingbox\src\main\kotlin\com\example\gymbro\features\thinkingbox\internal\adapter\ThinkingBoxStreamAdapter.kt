package com.example.gymbro.features.thinkingbox.internal.adapter

import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.internal.provider.ThinkingBoxViewModelProvider
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【架构简化】ThinkingBoxStreamAdapter - 直接集成Core-Network
 *
 * 🎯 简化后职责：
 * - 管理ThinkingBox流式处理的完整生命周期（启动/暂停/恢复/取消）
 * - 直接订阅DirectOutputChannel，消除ThinkingBoxAdapter中间层
 * - 集成语义解析器和领域映射器，将OutputToken转换为ThinkingEvent
 * - 提供流式处理作业的状态管理和监控
 *
 * � 移除的重复层：
 * - ❌ 不再依赖ThinkingBoxAdapter（重复处理）
 * - ❌ 消除双重适配器架构
 * - ✅ 直接使用DirectOutputChannel（唯一数据源）
 */
@Singleton
class ThinkingBoxStreamAdapter @Inject constructor(
    private val directOutputChannel: DirectOutputChannel,
    private val streamingParser: StreamingThinkingMLParser,
    private val domainMapper: DomainMapper,
    private val viewModelProvider: ThinkingBoxViewModelProvider, // 🔥 【架构优化】添加ViewModel集成
) {
    companion object {
        private const val TAG = "TB-ADAPTER-Stream"
    }

    // 流式处理作业管理
    private val activeJobs = mutableMapOf<String, StreamingJob>()

    /**
     * 流式处理作业封装
     * 提供暂停/恢复功能和生命周期管理
     */
    data class StreamingJob(
        val messageId: String,
        val job: Job,
        val scope: CoroutineScope,
        @Volatile var isPaused: Boolean = false,
        @Volatile var isCompleted: Boolean = false,
    ) {
        /**
         * 暂停流式处理
         */
        fun pause() {
            isPaused = true
            Timber.tag("StreamingJob").d("⏸️ 暂停流式处理: $messageId")
        }

        /**
         * 恢复流式处理
         */
        fun resume() {
            isPaused = false
            Timber.tag("StreamingJob").d("▶️ 恢复流式处理: $messageId")
        }

        /**
         * 取消流式处理作业
         */
        fun cancel() {
            if (!isCompleted) {
                job.cancel()
                scope.cancel()
                isCompleted = true
                Timber.tag("StreamingJob").d("❌ 取消流式处理: $messageId")
            }
        }

        /**
         * 标记作业完成
         */
        fun markCompleted() {
            isCompleted = true
            Timber.tag("StreamingJob").d("✅ 流式处理完成: $messageId")
        }
    }

    /**
     * 启动Token流处理，基于已存在的token流
     *
     * @param tokenFlow 原始token流（通常来自网络请求）
     * @param messageId 消息ID，用于标识和管理流式处理会话
     * @param onTokenReceived Token接收回调，传递解析后的ThinkingEvent
     * @param onStreamComplete 流式处理完成回调
     * @param onError 错误处理回调
     * @return StreamingJob 流式处理作业，支持暂停/恢复/取消
     */
    suspend fun startTokenStreamProcessing(
        tokenFlow: Flow<String>,
        messageId: String,
        onTokenReceived: (ThinkingEvent) -> Unit,
        onStreamComplete: () -> Unit,
        onError: (Throwable) -> Unit,
    ): StreamingJob {
        Timber.tag(TAG).i("🚀 启动Token流处理: messageId=$messageId")

        // 检查是否已有相同messageId的活跃作业
        activeJobs[messageId]?.let { existingJob ->
            if (!existingJob.isCompleted) {
                Timber.tag(TAG).w("⚠️ 取消现有的流式处理作业: $messageId")
                existingJob.cancel()
            }
        }

        // 创建协程作用域
        val jobScope = CoroutineScope(Dispatchers.IO + Job())

        // 启动流式处理作业
        val processingJob = jobScope.launch {
            try {
                // 🔥 【架构简化】直接订阅DirectOutputChannel，消除ThinkingBoxAdapter中间层
                val outputFlow = directOutputChannel.subscribeToConversation(messageId)

                // 初始化解析器状态
                var mappingContext = DomainMapper.MappingContext()

                Timber.tag(TAG).i("🎯 [简化架构] 直接订阅DirectOutputChannel: messageId=$messageId")

                // 处理输出Token流
                outputFlow
                    .flowOn(Dispatchers.IO)
                    .collect { outputToken ->
                        // 🔥 【调试追踪】强制ERROR级别日志追踪Token处理
                        Timber.tag(
                            "TB-ADAPTER-TRACE",
                        ).e(
                            "🔍 [Token接收] messageId=$messageId, content='${outputToken.content.take(
                                100,
                            )}...'",
                        )

                        // 检查暂停状态
                        val streamingJob = activeJobs[messageId]
                        if (streamingJob?.isPaused == true) {
                            Timber.tag("TB-ADAPTER-TRACE").e("⏸️ [流式处理暂停] 跳过Token: $messageId")
                            return@collect
                        }

                        try {
                            // 使用StreamingThinkingMLParser解析Token内容
                            streamingParser.parseTokenChunk(
                                tokenChunk = outputToken.content,
                                messageId = messageId,
                            ) { semanticEvent ->
                                // 🔥 【调试追踪】记录语义事件
                                Timber.tag(
                                    "TB-ADAPTER-TRACE",
                                ).e("📥 [语义事件] ${semanticEvent::class.simpleName}")

                                // 将SemanticEvent转换为ThinkingEvent
                                val mappingResult = domainMapper.mapSemanticToThinking(
                                    event = semanticEvent,
                                    context = mappingContext,
                                )

                                // 更新映射上下文
                                mappingContext = mappingResult.context

                                // 分发ThinkingEvent
                                mappingResult.events.forEach { thinkingEvent ->
                                    // 🔥 【调试追踪】记录思考事件分发
                                    Timber.tag(
                                        "TB-ADAPTER-TRACE",
                                    ).e("📤 [思考事件分发] ${thinkingEvent::class.simpleName}")
                                    onTokenReceived(thinkingEvent)
                                }
                            }
                        } catch (parseError: Exception) {
                            Timber.tag(
                                "TB-ADAPTER-TRACE",
                            ).e(
                                parseError,
                                "❌ [Token解析失败] messageId=$messageId, token=${outputToken.content.take(100)}",
                            )

                            // 创建解析错误事件
                            val errorEvent = ThinkingEvent.ParseError("Token解析失败: ${parseError.message}")
                            onTokenReceived(errorEvent)
                        }
                    }

                // 流式处理完成
                onStreamComplete()
                activeJobs[messageId]?.markCompleted()

                Timber.tag(TAG).i("✅ Token流处理完成: messageId=$messageId")
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "❌ Token流处理失败: messageId=$messageId")
                onError(e)

                // 清理资源
                activeJobs.remove(messageId)
            }
        }

        // 创建并注册StreamingJob
        val streamingJob = StreamingJob(
            messageId = messageId,
            job = processingJob,
            scope = jobScope,
        )

        activeJobs[messageId] = streamingJob

        return streamingJob
    }

    /**
     * 🔥 【架构解耦】启动DirectOutputChannel处理，无需外部tokenFlow
     *
     * 这是ThinkingBox的推荐使用方式：
     * - 直接订阅DirectOutputChannel获取已解析的清洁文本内容
     * - 不依赖外部提供的原始token流
     * - 接收Core-Network已经处理过的OutputToken对象
     * - 保持所有现有功能：暂停/恢复/取消、ThinkingEvent转换、生命周期管理
     *
     * @param messageId 消息ID，用于标识和管理流式处理会话
     * @param onTokenReceived Token接收回调，传递解析后的ThinkingEvent
     * @param onStreamComplete 流式处理完成回调
     * @param onError 错误处理回调
     * @return StreamingJob 流式处理作业，支持暂停/恢复/取消
     */
    suspend fun startDirectOutputProcessing(
        messageId: String,
        onTokenReceived: (ThinkingEvent) -> Unit,
        onStreamComplete: () -> Unit,
        onError: (Throwable) -> Unit,
    ): StreamingJob {
        Timber.tag(TAG).i("🚀 启动DirectOutput处理: messageId=$messageId")

        // 检查是否已有相同messageId的活跃作业
        activeJobs[messageId]?.let { existingJob ->
            if (!existingJob.isCompleted) {
                Timber.tag(TAG).w("⚠️ 取消现有的DirectOutput处理作业: $messageId")
                existingJob.cancel()
            }
        }

        // 创建协程作用域
        val jobScope = CoroutineScope(Dispatchers.IO + Job())

        // 启动DirectOutput处理作业
        val processingJob = jobScope.launch {
            try {
                // 🎯 【架构解耦】直接订阅DirectOutputChannel，无需外部tokenFlow
                // Core-Network已经完成了原始token的解析和清理工作
                Timber.tag(TAG).i("📡 订阅DirectOutputChannel: messageId=$messageId")

                // 初始化解析器状态
                var mappingContext = DomainMapper.MappingContext()

                // 🔥 【修复验证】添加订阅开始日志
                Timber.tag("TB-ADAPTER-Fix").e("🔗 [修复验证] 开始订阅DirectOutputChannel: messageId=$messageId")

                // 直接订阅DirectOutputChannel获取已处理的OutputToken
                directOutputChannel.subscribeToConversation(messageId)
                    .flowOn(Dispatchers.IO)
                    .collect { outputToken ->
                        // 🔥 【修复验证】记录首次接收
                        Timber.tag(
                            "TB-ADAPTER-Fix",
                        ).e(
                            "🎉 [修复成功] ThinkingBox接收到数据! messageId=$messageId, content='${outputToken.content.take(
                                100,
                            )}...', length=${outputToken.content.length}",
                        )

                        // 🔥 【调试追踪】记录DirectOutput接收
                        Timber.tag(
                            "TB-ADAPTER-TRACE",
                        ).e(
                            "🔍 [DirectOutput接收] messageId=$messageId, content='${outputToken.content.take(
                                100,
                            )}...'",
                        )

                        // 检查暂停状态
                        val streamingJob = activeJobs[messageId]
                        if (streamingJob?.isPaused == true) {
                            Timber.tag("TB-ADAPTER-TRACE").e("⏸️ [DirectOutput暂停] 跳过Token: $messageId")
                            return@collect
                        }

                        try {
                            // 🎯 【核心处理】使用StreamingThinkingMLParser解析已清理的内容
                            // outputToken.content已经是Core-Network处理过的清洁文本
                            streamingParser.parseTokenChunk(
                                tokenChunk = outputToken.content,
                                messageId = messageId,
                            ) { semanticEvent ->
                                // 🔥 【调试追踪】记录语义事件
                                Timber.tag(
                                    "TB-ADAPTER-TRACE",
                                ).e("📥 [DirectOutput语义事件] ${semanticEvent::class.simpleName}")

                                // 将SemanticEvent转换为ThinkingEvent
                                val mappingResult = domainMapper.mapSemanticToThinking(
                                    event = semanticEvent,
                                    context = mappingContext,
                                )

                                // 更新映射上下文
                                mappingContext = mappingResult.context

                                // 🔥 【架构优化】直接调用ViewModel方法，避免回调复杂性
                                mappingResult.events.forEach { thinkingEvent ->
                                    // 🔥 【调试追踪】记录思考事件分发
                                    Timber.tag(
                                        "TB-ADAPTER-TRACE",
                                    ).e("📤 [DirectOutput思考事件分发] ${thinkingEvent::class.simpleName}")

                                    // 🔥 【架构优化】直接调用ViewModel方法
                                    viewModelProvider.getViewModel(messageId)?.let { viewModel ->
                                        viewModel.processThinkingEvent(thinkingEvent)
                                    }

                                    // 保持向后兼容的回调
                                    onTokenReceived(thinkingEvent)
                                }
                            }
                        } catch (parseError: Exception) {
                            Timber.tag(
                                "TB-ADAPTER-TRACE",
                            ).e(
                                parseError,
                                "❌ [DirectOutput解析失败] messageId=$messageId, content=${outputToken.content.take(
                                    100,
                                )}",
                            )

                            // 创建解析错误事件
                            val errorEvent = ThinkingEvent.ParseError(
                                "DirectOutput解析失败: ${parseError.message}",
                            )

                            // 🔥 【架构优化】直接调用ViewModel方法处理错误
                            viewModelProvider.getViewModel(messageId)?.let { viewModel ->
                                viewModel.processThinkingEvent(errorEvent)
                            }

                            // 保持向后兼容的回调
                            onTokenReceived(errorEvent)
                        }
                    }

                // DirectOutput处理完成
                onStreamComplete()
                activeJobs[messageId]?.markCompleted()

                Timber.tag(TAG).i("✅ DirectOutput处理完成: messageId=$messageId")
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "❌ DirectOutput处理失败: messageId=$messageId")
                onError(e)

                // 清理资源
                activeJobs.remove(messageId)
            }
        }

        // 创建并注册StreamingJob
        val streamingJob = StreamingJob(
            messageId = messageId,
            job = processingJob,
            scope = jobScope,
        )

        activeJobs[messageId] = streamingJob

        Timber.tag(TAG).i("✅ DirectOutput处理作业已创建: messageId=$messageId")
        return streamingJob
    }

    /**
     * 暂停指定消息的流式处理
     *
     * @param messageId 消息ID
     * @return 是否成功暂停
     */
    fun pauseStreamProcessing(messageId: String): Boolean {
        val job = activeJobs[messageId]
        return if (job != null && !job.isCompleted) {
            job.pause()
            true
        } else {
            Timber.tag(TAG).w("⚠️ 未找到活跃的流式处理作业: $messageId")
            false
        }
    }

    /**
     * 恢复指定消息的流式处理
     *
     * @param messageId 消息ID
     * @return 是否成功恢复
     */
    fun resumeStreamProcessing(messageId: String): Boolean {
        val job = activeJobs[messageId]
        return if (job != null && !job.isCompleted) {
            job.resume()
            true
        } else {
            Timber.tag(TAG).w("⚠️ 未找到活跃的流式处理作业: $messageId")
            false
        }
    }

    /**
     * 取消指定消息的流式处理
     *
     * @param messageId 消息ID
     * @return 是否成功取消
     */
    fun cancelStreamProcessing(messageId: String): Boolean {
        val job = activeJobs.remove(messageId)
        return if (job != null) {
            job.cancel()
            Timber.tag(TAG).i("✅ 已取消流式处理: $messageId")
            true
        } else {
            Timber.tag(TAG).w("⚠️ 未找到流式处理作业: $messageId")
            false
        }
    }

    /**
     * 获取流式处理状态
     *
     * @param messageId 消息ID
     * @return 流式处理状态信息
     */
    fun getStreamProcessingStatus(messageId: String): StreamProcessingStatus? {
        val job = activeJobs[messageId]
        return job?.let {
            StreamProcessingStatus(
                messageId = messageId,
                isActive = !it.isCompleted,
                isPaused = it.isPaused,
                isCompleted = it.isCompleted,
            )
        }
    }

    /**
     * 获取所有活跃的流式处理状态
     *
     * @return 活跃流式处理状态列表
     */
    fun getAllActiveStreamProcessingStatus(): List<StreamProcessingStatus> {
        return activeJobs.values.map { job ->
            StreamProcessingStatus(
                messageId = job.messageId,
                isActive = !job.isCompleted,
                isPaused = job.isPaused,
                isCompleted = job.isCompleted,
            )
        }
    }

    /**
     * 清理所有流式处理作业
     * 通常在应用关闭或重置时调用
     */
    fun cleanupAll() {
        Timber.tag(TAG).i("🧹 清理所有流式处理作业，数量: ${activeJobs.size}")

        activeJobs.values.forEach { job ->
            job.cancel()
        }

        activeJobs.clear()

        Timber.tag(TAG).i("✅ 所有流式处理作业已清理")
    }

    /**
     * 获取适配器整体状态信息
     * 用于监控和调试
     */
    fun getAdapterStatus(): AdapterStatus {
        val networkAdapterStatus = thinkingBoxAdapter.getAdapterStatus()

        return AdapterStatus(
            totalActiveJobs = activeJobs.size,
            totalCompletedJobs = activeJobs.values.count { it.isCompleted },
            totalPausedJobs = activeJobs.values.count { it.isPaused },
            totalTokensReceived = networkAdapterStatus.totalTokensReceived,
            totalTokensOutput = networkAdapterStatus.totalTokensOutput,
        )
    }
}

/**
 * 流式处理状态信息
 */
data class StreamProcessingStatus(
    val messageId: String,
    val isActive: Boolean,
    val isPaused: Boolean,
    val isCompleted: Boolean,
)

/**
 * 适配器状态信息
 */
data class AdapterStatus(
    val totalActiveJobs: Int,
    val totalCompletedJobs: Int,
    val totalPausedJobs: Int,
    val totalTokensReceived: Long,
    val totalTokensOutput: Long,
)
