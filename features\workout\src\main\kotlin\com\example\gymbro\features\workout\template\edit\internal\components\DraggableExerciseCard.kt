package com.example.gymbro.features.workout.template.edit.internal.components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import com.example.gymbro.features.workout.json.processor.TemplateJsonProcessor
import com.example.gymbro.features.workout.shared.components.SwipeToDeleteWrapper
import com.example.gymbro.features.workout.shared.components.exercise.ExerciseComponentMode
import com.example.gymbro.features.workout.shared.components.exercise.ExerciseDisplayMode
import com.example.gymbro.features.workout.shared.components.exercise.WorkoutExerciseComponent
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import timber.log.Timber

/**
 * 模板动作卡片组件 - 简化版
 *
 * 🔥 重构原则：
 * - 移除所有拖拽手势，避免与SwipeToDeleteWrapper冲突
 * - 所有拖拽功能统一在WorkoutExerciseComponent中实现
 * - 仅保留滑动删除功能，使用shared的SwipeToDeleteWrapper
 * - 完全依赖WorkoutExerciseComponent的内部状态管理
 *
 * @param exercise 动作数据
 * @param onExerciseUpdate 动作更新回调
 * @param onDeleteExercise 删除动作回调
 * @param modifier 修饰符
 */
@Composable
fun TemplateExerciseCard(
    exercise: TemplateExerciseDto,
    onExerciseUpdate: (TemplateExerciseDto) -> Unit,
    onDeleteExercise: ((String) -> Unit)? = null,
    modifier: Modifier = Modifier,
) {
    // 🔥 使用统一的滑动删除包装器
    SwipeToDeleteWrapper(
        onDelete = {
            onDeleteExercise?.invoke(exercise.id)
            true // 确认删除
        },
        modifier = modifier,
    ) {
        // 🔥 性能优化：缓存数据转换，避免重复计算
        val exerciseDto = remember(exercise.id, exercise.customSets.size) {
            Timber.d("🔧 [RENDER-DEBUG] TemplateExerciseCard 开始渲染: ${exercise.exerciseName}")
            TemplateJsonProcessor.run { exercise.toExerciseDto() }
        }

        // 🔥 添加渲染监控：确保组件能正常显示
        LaunchedEffect(exercise.id) {
            Timber.d(
                "🔧 [RENDER-DEBUG] TemplateExerciseCard LaunchedEffect: exerciseId=${exercise.id}, name=${exercise.exerciseName}",
            )
        }

        // 🔥 直接使用 WorkoutExerciseComponent，让其内部处理所有状态
        WorkoutExerciseComponent(
            exercise = exerciseDto,
            mode = ExerciseComponentMode.TEMPLATE, // 🎯 使用 TEMPLATE 模式
            initialDisplayMode = ExerciseDisplayMode.EXPANDED, // 🔥 修复：默认展开模式，确保用户能看到内容
            allowManualToggle = true, // 允许用户手动切换展开/收起
            onExerciseUpdate = { updatedExerciseDto ->
                // 🔥 性能优化：减少调试日志，仅保留关键日志
                Timber.d(
                    "🔧 [UPDATE] DraggableExerciseCard 收到更新: ${updatedExerciseDto.name}, sets=${updatedExerciseDto.targetSets.size}",
                )

                // 数据转换回 TemplateExerciseDto
                val updatedTemplateExercise = TemplateJsonProcessor.run {
                    exercise.updateFromExerciseDto(updatedExerciseDto)
                }

                Timber.d("🔧 [UPDATE] DraggableExerciseCard 调用 onExerciseUpdate")
                onExerciseUpdate(updatedTemplateExercise)
            },
            modifier = Modifier.fillMaxWidth(),
        )
    }
}
