package com.example.gymbro.features.workout.template.edit.internal.effect

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.model.template.toDomain
import com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TemplateEdit 状态管理器
 *
 * 🎯 职责：
 * - 模板初始化逻辑
 * - 状态变更管理
 * - 退出逻辑处理
 * - 用户ID管理
 *
 * 🔥 重构改进：
 * - 从ViewModel中提取状态管理逻辑
 * - 简化初始化流程
 * - 统一用户ID获取
 * - 优化退出逻辑
 */
@Singleton
class TemplateEditStateManager @Inject constructor(
    private val templateManagementUseCase: TemplateManagementUseCase,
    private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
) {

    /**
     * 🔥 P3修复：初始化状态时获取用户ID
     * 确保用户ID在整个编辑流程中保持一致
     */
    suspend fun initializeState(): TemplateEditContract.State {
        return try {
            val userId = getCurrentUserId()
            Timber.d("✅ 初始化状态成功，用户ID: $userId")

            TemplateEditContract.State(
                currentUserId = userId,
                templateName = "",
                templateDescription = "",
                exercises = emptyList(),
                isLoading = false,
                error = null,
                hasUnsavedChanges = false,
                autoSaveState = TemplateContract.AutoSaveState.Inactive,
                currentVersion = 1,
                isDraft = true,
                isPublished = false,
                lastPublishedAt = null,
                template = null,
            )
        } catch (e: Exception) {
            Timber.e(e, "❌ 初始化状态失败，使用默认状态")
            // 降级方案：使用默认状态，但标记错误
            TemplateEditContract.State(
                currentUserId = "", // 空用户ID，需要后续处理
                templateName = "",
                templateDescription = "",
                exercises = emptyList(),
                isLoading = false,
                error = UiText.DynamicString("用户认证失败，请重新登录"),
                hasUnsavedChanges = false,
                autoSaveState = TemplateContract.AutoSaveState.Inactive,
                currentVersion = 1,
                isDraft = true,
                isPublished = false,
                lastPublishedAt = null,
                template = null,
            )
        }
    }

    /**
     * 🔥 初始化模板
     * 简化的初始化逻辑，根据templateId决定加载现有模板或创建新模板
     */
    suspend fun initializeTemplate(
        templateId: String?,
        onTemplateLoaded: (WorkoutTemplate) -> Unit,
        onEmptyTemplateCreated: (String) -> Unit,
    ) {
        try {
            when {
                templateId != null -> {
                    // 加载现有模板
                    loadExistingTemplate(templateId, onTemplateLoaded)
                }
                else -> {
                    // 创建新模板
                    createEmptyTemplate(onEmptyTemplateCreated)
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ 模板初始化失败")
            // 发生错误时创建空模板作为fallback
            createEmptyTemplate(onEmptyTemplateCreated)
        }
    }

    /**
     * 🔥 加载现有模板
     */
    private suspend fun loadExistingTemplate(
        templateId: String,
        onTemplateLoaded: (WorkoutTemplate) -> Unit,
    ) {
        try {
            Timber.d("📋 加载现有模板: $templateId")

            val result = templateManagementUseCase.getTemplate(templateId)
            when (result) {
                is ModernResult.Success -> {
                    val templateDto = result.data
                    if (templateDto != null) {
                        Timber.d("✅ 模板加载成功: ${templateDto.name}")
                        // 转换DTO为Domain模型，使用正确的用户ID
                        val currentUserId = getCurrentUserId()
                        val domainTemplate = templateDto.toDomain(currentUserId)
                        onTemplateLoaded(domainTemplate)
                    } else {
                        Timber.w("⚠️ 模板不存在，创建新模板")
                        val emptyTemplate = createEmptyWorkoutTemplate(templateId)
                        onTemplateLoaded(emptyTemplate)
                    }
                }
                is ModernResult.Error -> {
                    Timber.e("❌ 模板加载失败: ${result.error}")
                    // 加载失败时创建新模板
                    val emptyTemplate = createEmptyWorkoutTemplate(templateId)
                    onTemplateLoaded(emptyTemplate)
                }
                is ModernResult.Loading -> {
                    Timber.d("⏳ 模板加载中...")
                    // Loading状态通常不应该在这里出现，但处理以防万一
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ 加载模板异常")
            val emptyTemplate = createEmptyWorkoutTemplate(templateId)
            onTemplateLoaded(emptyTemplate)
        }
    }

    /**
     * 🔥 创建空模板
     */
    private fun createEmptyTemplate(
        onEmptyTemplateCreated: (String) -> Unit,
    ) {
        try {
            // 生成新的模板ID
            val newTemplateId = WorkoutTemplateDto.generateId()
            Timber.d("🆕 创建新模板: $newTemplateId")

            onEmptyTemplateCreated(newTemplateId)
        } catch (e: Exception) {
            Timber.e(e, "❌ 创建空模板失败")
            // 使用fallback ID
            val fallbackId = "template_${System.currentTimeMillis()}"
            onEmptyTemplateCreated(fallbackId)
        }
    }

    /**
     * 🔥 创建空的WorkoutTemplate对象
     */
    private suspend fun createEmptyWorkoutTemplate(templateId: String): WorkoutTemplate {
        val currentUserId = getCurrentUserId()

        return WorkoutTemplate(
            id = templateId,
            name = "",
            description = "",
            exercises = emptyList(),
            userId = currentUserId,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            isDraft = true,
            isPublished = false,
        )
    }

    /**
     * 🔥 准备退出逻辑
     * 简化的退出处理，检查是否需要保存
     */
    fun prepareToExit(
        currentState: TemplateEditContract.State,
        onSaveRequired: (WorkoutTemplate) -> Unit,
        onDirectExit: () -> Unit,
    ) {
        try {
            Timber.d("🚪 准备退出，检查保存状态")

            // 检查是否有未保存的更改
            if (currentState.hasUnsavedChanges) {
                val template = currentState.template
                if (template != null && hasRealContentChanges(currentState)) {
                    Timber.d("💾 检测到未保存的更改，需要保存")
                    onSaveRequired(template)
                    return
                }
            }

            Timber.d("✅ 无需保存，直接退出")
            onDirectExit()
        } catch (e: Exception) {
            Timber.e(e, "❌ 退出准备异常，强制退出")
            onDirectExit()
        }
    }

    /**
     * 🔥 检查是否有实际内容变更
     */
    private fun hasRealContentChanges(state: TemplateEditContract.State): Boolean {
        return state.templateName.isNotBlank() ||
            state.templateDescription.isNotBlank() ||
            state.exercises.isNotEmpty()
    }

    /**
     * 🔥 获取当前用户ID
     * 修复的用户ID获取逻辑，支持匿名用户
     */
    private suspend fun getCurrentUserId(): String {
        return try {
            withTimeoutOrNull(3000) {
                getCurrentUserIdUseCase().firstOrNull()?.let { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            val userId = result.data
                            if (!userId.isNullOrBlank()) {
                                userId // 使用真实的用户ID（登录用户或匿名用户）
                            } else {
                                // 🔥 用户ID为空表示认证异常
                                Timber.e("❌ 获取用户ID为空，用户可能未认证")
                                throw IllegalStateException("用户未认证")
                            }
                        }
                        is ModernResult.Error -> {
                            Timber.e("❌ 获取用户ID失败: ${result.error}")
                            throw IllegalStateException("认证服务异常: ${result.error}")
                        }
                        is ModernResult.Loading -> {
                            Timber.w("⏳ 用户ID仍在加载中")
                            throw IllegalStateException("用户认证状态异常")
                        }
                    }
                }
            } ?: run {
                Timber.e("❌ 获取用户ID超时")
                throw IllegalStateException("认证超时")
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ 获取用户ID异常，使用认证异常处理")
            throw IllegalStateException("用户认证失败: ${e.message}", e)
        }
    }

    /**
     * 🔥 清理资源
     */
    fun cleanup() {
        // 清理状态管理器资源
        Timber.d("🧹 TemplateEditStateManager 清理完成")
    }
}
