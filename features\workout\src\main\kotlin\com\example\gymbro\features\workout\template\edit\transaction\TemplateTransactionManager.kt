package com.example.gymbro.features.workout.template.edit.transaction

import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.repository.TemplateRepository
import com.example.gymbro.features.workout.template.edit.config.TemplateEditConfig
import com.example.gymbro.features.workout.template.edit.data.TemplateDataMapper
import com.example.gymbro.features.workout.template.edit.internal.components.functioncall.FunctionCallCompatibilityValidator
import com.example.gymbro.features.workout.template.edit.validation.JsonValidationUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 模板事务管理器 - P3 架构重构：Save/Load事务统一
 *
 * P3 新增功能：
 * 1. 统一Save/Load事务处理逻辑
 * 2. 原子性加载操作支持
 * 3. 数据一致性验证增强
 * 4. JSON处理和验证统一
 * 5. 事务日志和状态追踪
 *
 * 负责模板操作的原子性和一致性：
 * 1. 事务性保存操作
 * 2. 事务性加载操作（P3新增）
 * 3. 数据完整性验证
 * 4. Function Call兼容性验证
 * 5. 回滚机制
 * 6. 并发安全控制
 *
 * 设计原则：
 * - 原子性：要么全部成功，要么全部失败
 * - 一致性：确保数据库状态始终一致
 * - 隔离性：并发操作不会相互干扰
 * - 持久性：成功的操作永久保存
 * - Function Call兼容：确保与Coach模块的AI系统完全兼容
 *
 * <AUTHOR> 4.0 sonnet
 */
@Singleton
class TemplateTransactionManager @Inject constructor(
    private val templateRepository: TemplateRepository,
    private val functionCallValidator: FunctionCallCompatibilityValidator,
) {

    // ==================== P3: 统一Save/Load事务操作 ====================

    /**
     * P3: 原子性保存模板 - 增强版本
     * 确保所有相关操作要么全部成功，要么全部回滚
     */
    suspend fun saveTemplateAtomically(
        template: WorkoutTemplate,
        validateBeforeSave: Boolean = true,
    ): ModernResult<String> = withContext(Dispatchers.IO) {
        val transactionId = generateTransactionId()

        // 🔥 WK前缀事务日志跟踪
        com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveStep(
            "TX-START",
            template.name,
            "开始事务保存 - ID: $transactionId",
        )

        try {
            // P3 Phase 1: 预验证阶段 - 增强验证
            if (validateBeforeSave) {
                com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveStep(
                    "TX-VALIDATE",
                    template.name,
                    "开始预验证阶段",
                )
                val validationResult = validateTemplateForSave(template)
                if (validationResult is ModernResult.Error) {
                    com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveError(
                        "TX-VALIDATE-FAILED",
                        template.name,
                        "预验证失败",
                    )
                    return@withContext validationResult
                }
                com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveStep(
                    "TX-VALIDATE-OK",
                    template.name,
                    "预验证通过",
                )
            }

            // Phase 2: 数据准备阶段
            com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveStep(
                "TX-PREPARE",
                template.name,
                "数据准备阶段",
            )
            val templateDto = TemplateDataMapper.mapDomainToDto(template)

            // Phase 2 JSON兼容性验证
            com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveStep(
                "TX-JSON-CHECK",
                template.name,
                "JSON兼容性验证",
            )
            val compatibilityReport = JsonValidationUtils.validateTemplateJsonCompatibility(templateDto)
            if (!compatibilityReport.isValid) {
                val errors: List<String> = compatibilityReport.issues.map { issue ->
                    when (issue) {
                        is com.example.gymbro.features.workout.template.edit.validation.JsonValidationUtils.ValidationIssue.Error -> issue.message
                        is com.example.gymbro.features.workout.template.edit.validation.JsonValidationUtils.ValidationIssue.Warning -> issue.message
                    }
                }
                com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveError(
                    "TX-JSON-WARNING",
                    template.name,
                    "JSON兼容性警告: ${errors.joinToString(", ")}",
                )
                // 继续执行但记录警告
            }

            // Phase 3: Function Call兼容性验证
            com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveStep(
                "TX-FC-CHECK",
                template.name,
                "Function Call兼容性验证",
            )
            val functionCallReport = functionCallValidator.validateTemplateCompatibility(templateDto)
            if (!functionCallReport.isCompatible) {
                com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveError(
                    "TX-FC-WARNING",
                    template.name,
                    "兼容性评分: ${functionCallReport.compatibilityScore}, 问题: ${functionCallReport.issues.size}",
                )
                // 记录警告但不阻止保存，确保向后兼容
            } else {
                com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveStep(
                    "TX-FC-OK",
                    template.name,
                    "Function Call兼容性验证通过",
                )
            }

            // Phase 3: 原子性保存阶段
            com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveStep(
                "TX-ATOMIC",
                template.name,
                "执行原子性保存",
            )
            val saveResult = executeAtomicSave(template, transactionId)

            when (saveResult) {
                is ModernResult.Success -> {
                    com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveStep(
                        "TX-SUCCESS",
                        template.name,
                        "事务保存成功 - 模板ID: ${saveResult.data}",
                    )
                    return@withContext saveResult
                }
                is ModernResult.Error -> {
                    com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveError(
                        "TX-FAILED",
                        template.name,
                        "事务保存失败: ${saveResult.error}",
                    )
                    return@withContext saveResult
                }
                is ModernResult.Loading -> {
                    // 不应该发生
                    com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveError(
                        "TX-STATE-ERROR",
                        template.name,
                        "保存操作状态异常",
                    )
                    return@withContext ModernResult.Error(
                        ModernDataError(
                            operationName = "saveTemplateAtomically",
                            errorType = GlobalErrorType.System.General,
                            uiMessage = UiText.DynamicString("保存操作状态异常"),
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            com.example.gymbro.features.workout.logging.WorkoutLogUtils.logSaveError(
                "TX-EXCEPTION",
                template.name,
                "事务保存异常: ${e.message}",
            )
            return@withContext ModernResult.Error(
                ModernDataError(
                    operationName = "saveTemplateAtomically",
                    errorType = GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("保存失败: ${e.message}"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * P3: 原子性加载模板 - 新增功能
     * 确保加载操作的数据一致性和完整性验证
     */
    suspend fun loadTemplateAtomically(
        templateId: String,
        validateAfterLoad: Boolean = true,
    ): ModernResult<WorkoutTemplate> = withContext(Dispatchers.IO) {
        val transactionId = generateTransactionId()

        // 🔥 P3: CRITICAL日志 - 追踪事务性加载
        Timber.tag("CRITICAL-TRANSACTION").i("🔥 [P3-LOAD-TX] 开始事务性加载: $transactionId")
        Timber.tag("CRITICAL-TRANSACTION").i("🔥 [P3-LOAD-TX] 模板ID: $templateId")

        try {
            // P3 Phase 1: 原子性加载阶段
            Timber.tag("CRITICAL-TRANSACTION").i("🔥 [P3-LOAD-TX] 开始原子性加载")
            val loadResult = templateRepository.getTemplateById(templateId)

            when (loadResult) {
                is ModernResult.Success -> {
                    val template = loadResult.data
                    if (template == null) {
                        Timber.tag("CRITICAL-TRANSACTION").e("🔥 [P3-LOAD-TX] 模板不存在: $templateId")
                        return@withContext ModernResult.Error(
                            ModernDataError(
                                operationName = "loadTemplateAtomically",
                                errorType = GlobalErrorType.Validation.InvalidInput,
                                uiMessage = UiText.DynamicString("模板不存在"),
                            ),
                        )
                    }

                    Timber.tag("CRITICAL-TRANSACTION").i("🔥 [P3-LOAD-TX] 加载成功: ${template.name}")

                    // P3 Phase 2: 加载后验证阶段
                    if (validateAfterLoad) {
                        Timber.tag("CRITICAL-TRANSACTION").i("🔥 [P3-LOAD-TX] 开始加载后验证")
                        val validationResult = validateTemplateAfterLoad(template)
                        if (validationResult is ModernResult.Error) {
                            Timber.tag("CRITICAL-TRANSACTION").e("🔥 [P3-LOAD-TX] 加载后验证失败: $transactionId")
                            return@withContext validationResult
                        }
                        Timber.tag("CRITICAL-TRANSACTION").i("🔥 [P3-LOAD-TX] 加载后验证通过")
                    }

                    // P3 Phase 3: 数据完整性检查
                    val integrityResult = validateDataIntegrity(template)
                    if (integrityResult is ModernResult.Error) {
                        Timber.tag("CRITICAL-TRANSACTION").e("🔥 [P3-LOAD-TX] 数据完整性检查失败: $transactionId")
                        return@withContext integrityResult
                    }

                    Timber.tag("CRITICAL-TRANSACTION").i("🔥 [P3-LOAD-TX] 事务性加载成功: $transactionId")
                    return@withContext ModernResult.Success(template)
                }
                is ModernResult.Error -> {
                    Timber.tag(
                        "CRITICAL-TRANSACTION",
                    ).e("🔥 [P3-LOAD-TX] 加载失败: $transactionId, 错误: ${loadResult.error}")
                    return@withContext loadResult
                }
                is ModernResult.Loading -> {
                    return@withContext ModernResult.Error(
                        ModernDataError(
                            operationName = "loadTemplateAtomically",
                            errorType = GlobalErrorType.System.General,
                            uiMessage = UiText.DynamicString("加载操作状态异常"),
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            Timber.tag("CRITICAL-TRANSACTION").e(e, "🔥 [P3-LOAD-TX] 事务性加载异常: $transactionId")
            return@withContext ModernResult.Error(
                ModernDataError(
                    operationName = "loadTemplateAtomically",
                    errorType = GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("加载失败: ${e.message}"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * 批量原子性保存 - P3 增强
     * 支持多个模板的事务性保存和Function Call兼容性验证
     */
    suspend fun saveTemplatesBatch(
        templates: List<WorkoutTemplate>,
    ): ModernResult<List<String>> = withContext(Dispatchers.IO) {
        val transactionId = generateTransactionId()
        Timber.d("🔄 开始批量事务性保存: $transactionId, 模板数量: ${templates.size}")

        try {
            // Phase 3: 批量Function Call兼容性预检
            val templateDtos = templates.map { TemplateDataMapper.mapDomainToDto(it) }
            val batchReport = functionCallValidator.validateBatchCompatibility(templateDtos)

            Timber.d("📊 批量Function Call兼容性报告: $transactionId")
            Timber.d("   - 总模板数: ${batchReport.totalTemplates}")
            Timber.d("   - 兼容模板数: ${batchReport.compatibleTemplates}")
            Timber.d("   - 整体兼容率: ${String.format("%.1f", batchReport.overallCompatibilityRate * 100)}%")
            Timber.d("   - 平均评分: ${String.format("%.2f", batchReport.averageCompatibilityScore)}")

            if (batchReport.incompatibleTemplates > 0) {
                Timber.w("⚠️ 发现 ${batchReport.incompatibleTemplates} 个不兼容模板，将继续保存但记录警告")
                batchReport.commonIssues.forEach { issue ->
                    Timber.w("   - 常见问题: $issue")
                }
            }

            val results = mutableListOf<String>()

            // 使用事务确保原子性
            for ((index, template) in templates.withIndex()) {
                val saveResult = saveTemplateAtomically(template, validateBeforeSave = true)

                when (saveResult) {
                    is ModernResult.Success -> {
                        results.add(saveResult.data)
                        Timber.d("✅ 批量保存进度: $transactionId, ${index + 1}/${templates.size}")
                    }
                    is ModernResult.Error -> {
                        Timber.e("❌ 批量保存失败: $transactionId, 模板: ${template.name}")
                        // 批量操作中的单个失败不应该影响整个批次
                        // 但我们记录错误并继续
                        return@withContext saveResult
                    }
                    is ModernResult.Loading -> {
                        return@withContext ModernResult.Error(
                            ModernDataError(
                                operationName = "saveTemplatesBatch",
                                errorType = GlobalErrorType.System.General,
                                uiMessage = UiText.DynamicString("批量保存状态异常"),
                            ),
                        )
                    }
                }
            }

            Timber.d("✅ 批量事务性保存成功: $transactionId, 成功数量: ${results.size}")
            return@withContext ModernResult.Success(results)
        } catch (e: Exception) {
            Timber.e(e, "❌ 批量事务性保存异常: $transactionId")
            return@withContext ModernResult.Error(
                ModernDataError(
                    operationName = "saveTemplatesBatch",
                    errorType = GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("批量保存失败: ${e.message}"),
                    cause = e,
                ),
            )
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 执行原子性保存操作
     */
    private suspend fun executeAtomicSave(
        template: WorkoutTemplate,
        transactionId: String,
    ): ModernResult<String> {
        return try {
            // 确保写入 templateDB
            val saveResult = templateRepository.saveTemplate(template)

            when (saveResult) {
                is ModernResult.Success -> {
                    Timber.d("✅ 原子性保存完成: $transactionId, 模板ID: ${saveResult.data}")
                    saveResult
                }
                is ModernResult.Error -> {
                    Timber.e("❌ 原子性保存失败: $transactionId, 错误: ${saveResult.error}")
                    saveResult
                }
                is ModernResult.Loading -> {
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "executeAtomicSave",
                            errorType = GlobalErrorType.System.General,
                            uiMessage = UiText.DynamicString("保存操作状态异常"),
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ 原子性保存异常: $transactionId")
            ModernResult.Error(
                ModernDataError(
                    operationName = "executeAtomicSave",
                    errorType = GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("保存操作失败: ${e.message}"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * P3: 验证模板加载后的数据完整性
     */
    private suspend fun validateTemplateAfterLoad(template: WorkoutTemplate): ModernResult<Unit> {
        return try {
            // 基础数据完整性检查
            if (template.id.isBlank()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "validateTemplateAfterLoad",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("加载的模板ID为空"),
                    ),
                )
            }

            if (template.name.isBlank()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "validateTemplateAfterLoad",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("加载的模板名称为空"),
                    ),
                )
            }

            // 检查时间戳合理性
            if (template.createdAt <= 0 || template.updatedAt <= 0) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "validateTemplateAfterLoad",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("加载的模板时间戳无效"),
                    ),
                )
            }

            ModernResult.Success(Unit)
        } catch (e: Exception) {
            ModernResult.Error(
                ModernDataError(
                    operationName = "validateTemplateAfterLoad",
                    errorType = GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("加载后验证失败: ${e.message}"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * P3: 验证数据完整性
     */
    private suspend fun validateDataIntegrity(template: WorkoutTemplate): ModernResult<Unit> {
        return try {
            // 检查动作数据完整性
            template.exercises.forEach { exercise ->
                if (exercise.id.isBlank() || exercise.exerciseId.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "validateDataIntegrity",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("动作数据不完整: ${exercise.name}"),
                        ),
                    )
                }
            }

            // 检查版本一致性
            if (template.currentVersion <= 0) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "validateDataIntegrity",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("模板版本号无效"),
                    ),
                )
            }

            ModernResult.Success(Unit)
        } catch (e: Exception) {
            ModernResult.Error(
                ModernDataError(
                    operationName = "validateDataIntegrity",
                    errorType = GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("数据完整性验证失败: ${e.message}"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * 验证模板是否可以保存
     */
    private suspend fun validateTemplateForSave(template: WorkoutTemplate): ModernResult<Unit> {
        return try {
            // 基础验证
            if (template.name.isBlank()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "validateTemplateForSave",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("模板名称不能为空"),
                    ),
                )
            }

            if (template.userId.isBlank()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "validateTemplateForSave",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("用户ID不能为空"),
                    ),
                )
            }

            if (template.exercises.isEmpty()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "validateTemplateForSave",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("模板必须包含至少一个动作"),
                    ),
                )
            }

            // 动作验证
            template.exercises.forEach { exercise ->
                if (exercise.exerciseId.isBlank()) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "validateTemplateForSave",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("动作ID不能为空: ${exercise.name}"),
                        ),
                    )
                }

                if (exercise.sets <= 0) {
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "validateTemplateForSave",
                            errorType = GlobalErrorType.Validation.InvalidInput,
                            uiMessage = UiText.DynamicString("动作组数必须大于0: ${exercise.name}"),
                        ),
                    )
                }
            }

            ModernResult.Success(Unit)
        } catch (e: Exception) {
            ModernResult.Error(
                ModernDataError(
                    operationName = "validateTemplateForSave",
                    errorType = GlobalErrorType.System.General,
                    uiMessage = UiText.DynamicString("验证失败: ${e.message}"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * 生成事务ID
     */
    private fun generateTransactionId(): String {
        return "${TemplateEditConfig.TRANSACTION_ID_PREFIX}${System.currentTimeMillis()}_${(TemplateEditConfig.TRANSACTION_ID_MIN..TemplateEditConfig.TRANSACTION_ID_MAX).random()}"
    }

    // ==================== Phase 3: Function Call 专项验证 ====================

    /**
     * 验证模板的Function Call兼容性
     * 不执行保存，仅进行兼容性检查
     */
    suspend fun validateFunctionCallCompatibility(
        template: WorkoutTemplate,
    ): FunctionCallCompatibilityValidator.FunctionCallCompatibilityReport {
        return withContext(Dispatchers.IO) {
            try {
                val templateDto = TemplateDataMapper.mapDomainToDto(template)
                functionCallValidator.validateTemplateCompatibility(templateDto)
            } catch (e: Exception) {
                Timber.e(e, "Function Call兼容性验证异常")
                FunctionCallCompatibilityValidator.FunctionCallCompatibilityReport(
                    templateId = template.id,
                    templateName = template.name,
                    isCompatible = false,
                    issues = listOf(
                        FunctionCallCompatibilityValidator.CompatibilityIssue.Error("验证异常: ${e.message}"),
                    ),
                    exerciseReports = emptyList(),
                    compatibilityScore = 0.0f,
                    recommendations = listOf("请检查模板数据完整性"),
                )
            }
        }
    }

    /**
     * 批量验证Function Call兼容性
     */
    suspend fun validateBatchFunctionCallCompatibility(
        templates: List<WorkoutTemplate>,
    ): FunctionCallCompatibilityValidator.BatchCompatibilityReport {
        return withContext(Dispatchers.IO) {
            try {
                val templateDtos = templates.map { TemplateDataMapper.mapDomainToDto(it) }
                functionCallValidator.validateBatchCompatibility(templateDtos)
            } catch (e: Exception) {
                Timber.e(e, "批量Function Call兼容性验证异常")
                FunctionCallCompatibilityValidator.BatchCompatibilityReport(
                    totalTemplates = templates.size,
                    compatibleTemplates = 0,
                    incompatibleTemplates = templates.size,
                    reports = emptyList(),
                    overallCompatibilityRate = 0.0f,
                    averageCompatibilityScore = 0.0f,
                    commonIssues = listOf("验证异常: ${e.message}"),
                    batchRecommendations = listOf("请检查模板数据完整性"),
                )
            }
        }
    }

    // ==================== 事务状态查询 ====================

    /**
     * 检查模板是否存在
     */
    suspend fun isTemplateExists(templateId: String): ModernResult<Boolean> {
        return try {
            val result = templateRepository.getTemplateById(templateId)
            when (result) {
                is ModernResult.Success -> ModernResult.Success(result.data != null)
                is ModernResult.Error -> ModernResult.Success(false) // 查询失败视为不存在
                is ModernResult.Loading -> ModernResult.Success(false)
            }
        } catch (e: Exception) {
            ModernResult.Success(false)
        }
    }

    /**
     * 获取模板保存统计信息
     */
    suspend fun getTemplateSaveStats(): TemplateSaveStats {
        return try {
            // 这里可以添加统计逻辑
            TemplateSaveStats(
                totalTemplates = 0,
                draftTemplates = 0,
                publishedTemplates = 0,
                lastSaveTime = System.currentTimeMillis(),
            )
        } catch (e: Exception) {
            Timber.w(e, "获取模板统计信息失败")
            TemplateSaveStats()
        }
    }

    // ==================== 数据类定义 ====================

    data class TemplateSaveStats(
        val totalTemplates: Int = 0,
        val draftTemplates: Int = 0,
        val publishedTemplates: Int = 0,
        val lastSaveTime: Long = 0L,
    )
}
