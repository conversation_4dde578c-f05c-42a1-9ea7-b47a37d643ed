package com.example.gymbro.features.thinkingbox.integration

import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel.ThinkingBoxViewModel
import io.mockk.mockk
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * AI流模拟测试
 * 模拟完整的AI流处理：从XML输入到ThinkingBox输出
 *
 * 测试场景：
 * 1. 模拟AI输出的XML结构
 * 2. 验证ThinkingBox的解析和状态管理
 * 3. 确保最终输出的正确性
 */
@DisplayName("AI流模拟测试")
class AIStreamSimulationTest {

    private lateinit var testScope: TestScope
    private lateinit var viewModel: ThinkingBoxViewModel

    @BeforeEach
    fun setup() {
        testScope = TestScope(StandardTestDispatcher())

        // 创建ViewModel，使用mock的依赖
        viewModel = ThinkingBoxViewModel(
            directOutputChannel = mockk<DirectOutputChannel>(relaxed = true),
            streamingParser = mockk(relaxed = true),
            domainMapper = mockk(relaxed = true),
            segmentQueueReducer = mockk(relaxed = true),
        )
    }

    @Test
    @DisplayName("AI流数据结构模拟测试")
    fun `AI stream data structure simulation test`() = testScope.runTest {
        // 模拟AI输出的XML结构转换为ThinkingEvent序列
        val aiStreamEvents = createAIStreamEvents()

        // 验证事件序列的正确性
        assertEquals(10, aiStreamEvents.size)

        // 验证thinking阶段事件
        assertTrue(aiStreamEvents[0] is ThinkingEvent.SegmentStarted)
        assertTrue(aiStreamEvents[1] is ThinkingEvent.SegmentText)
        assertTrue(aiStreamEvents[2] is ThinkingEvent.SegmentClosed)

        // 验证final阶段事件
        assertTrue(aiStreamEvents[6] is ThinkingEvent.ThinkingClosed)
        assertTrue(aiStreamEvents[7] is ThinkingEvent.FinalStart)
        assertTrue(aiStreamEvents[8] is ThinkingEvent.FinalContent)
        assertTrue(aiStreamEvents[9] is ThinkingEvent.FinalComplete)

        // 验证事件内容
        val phase1Start = aiStreamEvents[0] as ThinkingEvent.SegmentStarted
        assertEquals("1", phase1Start.id)
        assertEquals(SegmentKind.PHASE, phase1Start.kind)
        assertEquals("小结目标", phase1Start.title)

        val finalContent = aiStreamEvents[8] as ThinkingEvent.FinalContent
        assertTrue(finalContent.text.contains("个性化健身计划"))
    }

    /**
     * 创建模拟的AI流事件序列
     * 对应XML结构：
     * <thinking>
     *   <phase id="1"><title>小结目标</title>...</phase>
     *   <phase id="2"><title>实现步骤</title>...</phase>
     * </thinking>
     * <final>...</final>
     */
    private fun createAIStreamEvents(): List<ThinkingEvent> {
        return listOf(
            // Phase 1: 小结目标
            ThinkingEvent.SegmentStarted(
                id = "1",
                kind = SegmentKind.PHASE,
                title = "小结目标",
            ),
            ThinkingEvent.SegmentText("我需要分析用户的健身目标，制定合适的训练计划。"),
            ThinkingEvent.SegmentClosed("1"),

            // Phase 2: 实现步骤
            ThinkingEvent.SegmentStarted(
                id = "2",
                kind = SegmentKind.PHASE,
                title = "实现步骤",
            ),
            ThinkingEvent.SegmentText("1. 体能评估\n2. 目标设定\n3. 计划制定"),
            ThinkingEvent.SegmentClosed("2"),

            // Thinking结束
            ThinkingEvent.ThinkingClosed,

            // Final阶段
            ThinkingEvent.FinalStart, // 开始final
            ThinkingEvent.FinalContent(
                """
                # 🏋️ 个性化健身计划

                ## 训练安排
                - 周一、三、五：有氧训练
                - 周二、四：力量训练
                - 周六：柔韧性训练
                - 周日：休息

                ## 重要提醒
                1. 训练前热身
                2. 循序渐进
                3. 充足休息
                4. 营养补充

                开始您的健身之旅吧！💪
                """.trimIndent(),
            ),
            ThinkingEvent.FinalComplete,
        )
    }

    @Test
    @DisplayName("复杂XML流结构验证测试")
    fun `complex XML stream structure validation test`() = testScope.runTest {
        // 模拟复杂的XML结构，包含多个phase
        val complexXmlStructure = """
            <thinking>
              <phase id="1">
                <title>小结目标</title>
                用户希望制定一个全面的健身计划
              </phase>
              <phase id="2">
                <title>实现步骤</title>
                1. 评估体能
                2. 制定计划
                3. 执行监控
              </phase>
              <phase id="3">
                <title>风险评估</title>
                需要考虑运动伤害风险和过度训练问题
              </phase>
              <phase id="4">
                <title>最终确认</title>
                确认计划的可行性和用户接受度
              </phase>
            </thinking>
            <final>
            # 综合健身方案

            经过全面分析，为您提供最适合的训练计划。

            ## 训练重点
            - 安全第一
            - 循序渐进
            - 个性化定制
            </final>
        """.trimIndent()

        // 验证XML结构包含预期的标签
        assertTrue(complexXmlStructure.contains("<thinking>"))
        assertTrue(complexXmlStructure.contains("</thinking>"))
        assertTrue(complexXmlStructure.contains("<final>"))
        assertTrue(complexXmlStructure.contains("</final>"))
        assertTrue(complexXmlStructure.contains("<phase id=\"1\">"))
        assertTrue(complexXmlStructure.contains("<phase id=\"4\">"))
        assertTrue(complexXmlStructure.contains("<title>小结目标</title>"))
        assertTrue(complexXmlStructure.contains("<title>最终确认</title>"))

        // 验证内容结构
        assertTrue(complexXmlStructure.contains("综合健身方案"))
        assertTrue(complexXmlStructure.contains("安全第一"))
        assertTrue(complexXmlStructure.contains("个性化定制"))

        // 模拟对应的事件序列
        val expectedEvents = createComplexEventSequence()
        assertEquals(15, expectedEvents.size) // 4个phase * 3个事件 + 3个final事件

        // 验证事件类型分布
        val segmentStartedCount = expectedEvents.count { it is ThinkingEvent.SegmentStarted }
        val segmentTextCount = expectedEvents.count { it is ThinkingEvent.SegmentText }
        val segmentClosedCount = expectedEvents.count { it is ThinkingEvent.SegmentClosed }

        assertEquals(4, segmentStartedCount)
        assertEquals(4, segmentTextCount)
        assertEquals(4, segmentClosedCount)
    }

    /**
     * 创建复杂事件序列
     */
    private fun createComplexEventSequence(): List<ThinkingEvent> {
        return listOf(
            // Phase 1
            ThinkingEvent.SegmentStarted("1", SegmentKind.PHASE, "小结目标"),
            ThinkingEvent.SegmentText("用户希望制定一个全面的健身计划"),
            ThinkingEvent.SegmentClosed("1"),

            // Phase 2
            ThinkingEvent.SegmentStarted("2", SegmentKind.PHASE, "实现步骤"),
            ThinkingEvent.SegmentText("1. 评估体能\n2. 制定计划\n3. 执行监控"),
            ThinkingEvent.SegmentClosed("2"),

            // Phase 3
            ThinkingEvent.SegmentStarted("3", SegmentKind.PHASE, "风险评估"),
            ThinkingEvent.SegmentText("需要考虑运动伤害风险和过度训练问题"),
            ThinkingEvent.SegmentClosed("3"),

            // Phase 4
            ThinkingEvent.SegmentStarted("4", SegmentKind.PHASE, "最终确认"),
            ThinkingEvent.SegmentText("确认计划的可行性和用户接受度"),
            ThinkingEvent.SegmentClosed("4"),

            // Final
            ThinkingEvent.ThinkingClosed,
            ThinkingEvent.FinalStart,
            ThinkingEvent.FinalContent("# 综合健身方案\n\n经过全面分析，为您提供最适合的训练计划。"),
        )
    }
}
