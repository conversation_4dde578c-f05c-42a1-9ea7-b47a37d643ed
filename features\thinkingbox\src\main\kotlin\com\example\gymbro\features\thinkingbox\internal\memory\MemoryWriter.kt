package com.example.gymbro.features.thinkingbox.internal.memory

import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * MemoryWriter - ThinkingBox记忆系统写入器
 *
 * 🔥 根据大纲要求实现：
 * - 监听UiState.Final，将markdown内容写入向量库
 * - 支持Memory系统的语义检索
 * - 包含元数据：duration、token count等
 *
 * 🔥 【简化实现】暂时使用日志记录，等待完整的向量数据库实现
 */
@Singleton
class MemoryWriter @Inject constructor(
    private val coroutineScope: CoroutineScope,
) {

    private val TAG = "TB-MEMORY"

    /**
     * 开始监听UiState并写入Memory
     */
    fun startWriting(uiStateFlow: Flow<UiState>) {
        coroutineScope.launch {
            uiStateFlow.collect { uiState ->
                try {
                    handleUiState(uiState)
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "❌ 处理UiState失败")
                }
            }
        }
    }

    /**
     * 处理UiState变化
     */
    private suspend fun handleUiState(uiState: UiState) {
        // 🔥 只处理Final状态，且有markdown内容
        if (uiState.finalMarkdown != null && !uiState.finalMarkdown.isBlank()) {
            writeToMemory(uiState)
        }
    }

    /**
     * 将Final内容写入Memory向量库
     * 🔥 【简化实现】暂时使用日志记录，等待完整实现
     */
    private suspend fun writeToMemory(uiState: UiState) {
        val markdown = uiState.finalMarkdown ?: return
        val messageId = "thinking-${System.currentTimeMillis()}" // TODO: 需要从上下文获取真实messageId

        try {
            // 🔥 准备元数据
            val metadata = buildMap<String, Any> {
                put("messageId", messageId)
                put("contentType", "thinking_final")
                put("timestamp", System.currentTimeMillis())

                // 🔥 添加思考过程元数据
                put("duration", uiState.elapsed.inWholeMilliseconds)
                put("phaseCount", uiState.phases.size)

                // 🔥 添加内容统计
                put("contentLength", markdown.length)
                put("wordCount", markdown.split("\\s+".toRegex()).size)

                // 🔥 添加阶段标题（用于检索）
                val phaseTitles = uiState.phases.mapNotNull { it.title }.joinToString(", ")
                if (phaseTitles.isNotEmpty()) {
                    put("phaseTitles", phaseTitles)
                }

                // 🔥 添加状态信息
                put("isCompleted", uiState.finalMarkdown != null && !uiState.isStreaming)
                put("isCollapsed", uiState.isCollapsed)
            }

            // 🔥 【简化实现】暂时记录到日志，等待向量数据库集成
            Timber.tag(TAG).d(
                "💾 Memory写入记录: $messageId (${markdown.length} chars, ${uiState.phases.size} phases)\n" +
                    "元数据: $metadata\n" +
                    "内容预览: ${markdown.take(100)}...",
            )

            // TODO: 实际的向量数据库写入逻辑
            // vectorDatabase.insert(
            //     id = messageId,
            //     vector = textEmbedder.embed(markdown),
            //     metadata = metadata,
            //     content = markdown,
            // )
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ Memory写入失败: $messageId")
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        Timber.tag(TAG).d("🧹 MemoryWriter已清理")
    }
}

/**
 * 向量数据库接口
 * 🔥 【接口定义】为未来的完整实现预留
 */
interface VectorDatabase {
    suspend fun insert(
        id: String,
        vector: FloatArray,
        metadata: Map<String, Any>,
        content: String,
    )

    suspend fun search(
        vector: FloatArray,
        limit: Int = 10,
        threshold: Float = 0.7f,
    ): List<VectorSearchResult>
}

/**
 * 文本嵌入器接口
 * 🔥 【接口定义】为未来的完整实现预留
 */
interface TextEmbedder {
    suspend fun embed(text: String): FloatArray
}

/**
 * 向量搜索结果
 */
data class VectorSearchResult(
    val id: String,
    val score: Float,
    val metadata: Map<String, Any>,
    val content: String,
)