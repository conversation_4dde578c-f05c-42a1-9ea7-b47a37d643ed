package com.example.gymbro.features.thinkingbox

import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithText
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.ThinkingStageCard
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * ThinkingBox UI组件Android仪器化测试
 *
 * 🎯 测试目标：验证UI组件的基本渲染
 * 📊 覆盖范围：ThinkingStageCard等核心UI组件
 * 🔥 关键验证：
 * 1. UI组件能够正常渲染
 * 2. 基本属性显示正确
 * 3. 组件不会崩溃
 */
@LargeTest
@RunWith(AndroidJUnit4::class)
@HiltAndroidTest
class ThinkingBoxUIComponentsInstrumentedTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    @get:Rule
    val composeTestRule = createComposeRule()

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun testThinkingStageCardBasicRendering() = runTest {
        val testSegment = ThinkingBoxContract.SegmentUi(
            id = "test-1",
            kind = "PHASE",
            title = "测试阶段",
            content = "这是测试内容",
            isComplete = false,
        )

        composeTestRule.setContent {
            ThinkingStageCard(
                segment = testSegment,
                isActive = true,
            )
        }

        // 验证基本内容显示
        composeTestRule.onNodeWithText("测试阶段").assertIsDisplayed()
        composeTestRule.onNodeWithText("这是测试内容").assertIsDisplayed()
    }

    @Test
    fun testThinkingStageCardWithDifferentStates() = runTest {
        val completedSegment = ThinkingBoxContract.SegmentUi(
            id = "test-2",
            kind = "PHASE",
            title = "已完成阶段",
            content = "已完成的内容",
            isComplete = true,
        )

        composeTestRule.setContent {
            ThinkingStageCard(
                segment = completedSegment,
                isActive = false,
            )
        }

        // 验证已完成状态的显示
        composeTestRule.onNodeWithText("已完成阶段").assertIsDisplayed()
        composeTestRule.onNodeWithText("已完成的内容").assertIsDisplayed()
    }

    @Test
    fun testThinkingBoxMainComponentRendering() = runTest {
        val messageId = "ui-test-001"

        composeTestRule.setContent {
            ThinkingBox(
                messageId = messageId,
            )
        }

        // 验证主组件能够正常渲染
        composeTestRule.waitForIdle()

        // 基本渲染验证 - 确保组件不崩溃
    }

    @Test
    fun testThinkingBoxWithEmptyMessageId() = runTest {
        val messageId = ""

        composeTestRule.setContent {
            ThinkingBox(
                messageId = messageId,
            )
        }

        // 验证空messageId的处理
        composeTestRule.waitForIdle()

        // 确保组件能够处理边界情况
    }

    @Test
    fun testThinkingBoxWithLongMessageId() = runTest {
        val messageId = "very-long-message-id-that-might-cause-issues-in-ui-rendering-test-case"

        composeTestRule.setContent {
            ThinkingBox(
                messageId = messageId,
            )
        }

        // 验证长messageId的处理
        composeTestRule.waitForIdle()

        // 确保组件能够处理长ID
    }
}
