package com.example.gymbro.features.workout.template.di

import android.content.Context
import com.example.gymbro.core.di.qualifiers.ApplicationScope
import com.example.gymbro.features.workout.template.cache.TemplateAutoSaveManager
import com.example.gymbro.features.workout.template.cache.TemplateCacheManager
import com.example.gymbro.features.workout.template.edit.TemplateSaver
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.exercise.repository.ExerciseRepository
import kotlinx.coroutines.CoroutineDispatcher
import com.example.gymbro.domain.workout.usecase.exercise.GetExerciseByIdUseCase

/**
 * 模板功能依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object TemplateModule {
    @Provides
    @Singleton
    fun provideGetExerciseByIdUseCase(
        exerciseRepository: ExerciseRepository,
        @IoDispatcher dispatcher: CoroutineDispatcher,
        logger: Logger,
    ): GetExerciseByIdUseCase = GetExerciseByIdUseCase(exerciseRepository, dispatcher, logger)

    @Provides
    @Singleton
    fun provideTemplateCacheManager(
        @ApplicationContext context: Context,
        json: Json,
    ): TemplateCacheManager {
        return TemplateCacheManager(context, json)
    }

    @Provides
    @Singleton
    fun provideTemplateAutoSaveManager(
        templateSaver: TemplateSaver,
        @ApplicationScope applicationScope: CoroutineScope,
    ): TemplateAutoSaveManager = TemplateAutoSaveManager(templateSaver, applicationScope)
}
