package com.example.gymbro.features.thinkingbox.api

/**
 * ThinkingBoxContext - AI思考处理上下文
 *
 * 🎯 职责分离架构设计：
 * - 封装AI处理所需的所有上下文信息
 * - 支持个性化AI响应和智能内容生成
 * - 遵循Clean Architecture原则，作为领域数据的载体
 * - 提供灵活的上下文组合和扩展能力
 *
 * 🔥 核心功能：
 * - 用户资料：个人信息、健身水平、偏好等
 * - 对话历史：历史消息记录，支持上下文连续性
 * - 模板信息：预设模板和格式化信息
 * - 扩展数据：支持未来功能的额外数据字段
 *
 * 架构原则：
 * - 组合优于继承：通过组合不同上下文类型
 * - 可选性设计：所有上下文都是可选的，支持渐进式增强
 * - 类型安全：使用强类型确保数据一致性
 * - 扩展性：支持未来新增上下文类型
 *
 * @since Coach-ThinkingBox重构v2.0
 */
data class ThinkingBoxContext(
    /**
     * 用户个人资料上下文
     *
     * 包含用户的个人信息、健身水平、偏好设置等，
     * 用于AI生成个性化的响应和建议。
     *
     * 内容包括：
     * - 基本信息：年龄、性别、健身经验
     * - 身体数据：身高、体重、健康状况
     * - 偏好设置：训练类型、时间安排、目标
     * - 限制条件：伤病史、设备限制等
     */
    val userProfile: UserProfileContext? = null,

    /**
     * 对话历史上下文
     *
     * 包含当前会话的历史消息记录，支持AI理解对话上下文
     * 和生成连贯的响应。
     *
     * 内容包括：
     * - 历史用户消息
     * - 历史AI响应
     * - 消息时间戳
     * - 消息类型和元数据
     */
    val conversationHistory: List<ConversationTurn> = emptyList(),

    /**
     * 模板上下文
     *
     * 包含预设的模板信息和格式化规则，用于指导AI
     * 生成符合特定格式和风格的响应。
     *
     * 内容包括：
     * - 响应模板
     * - 格式化规则
     * - 风格指导
     * - 内容结构要求
     */
    val templateContext: Map<String, Any>? = null,

    /**
     * 扩展数据
     *
     * 支持未来功能扩展的额外数据字段，采用键值对形式
     * 存储任意类型的上下文信息。
     *
     * 用途：
     * - 实验性功能的数据传递
     * - 第三方集成的上下文信息
     * - 临时性的业务数据
     * - 调试和诊断信息
     */
    val extensionData: Map<String, Any> = emptyMap(),
) {
    /**
     * 验证上下文数据的有效性
     *
     * 检查上下文数据是否符合基本要求，确保AI处理的质量。
     *
     * @return 如果上下文数据有效返回true，否则返回false
     */
    fun isValid(): Boolean {
        // 检查用户资料的有效性（如果存在）
        if (userProfile != null && !userProfile.isValid()) {
            return false
        }

        // 检查对话历史的有效性
        if (conversationHistory.any { !it.isValid() }) {
            return false
        }

        // 检查模板上下文的有效性（如果存在）
        if (templateContext != null && templateContext.isEmpty()) {
            return false
        }

        return true
    }

    /**
     * 获取上下文摘要信息
     *
     * 用于日志记录和调试，提供上下文的关键信息概览。
     *
     * @return 包含关键信息的摘要字符串
     */
    fun getSummary(): String {
        return "ThinkingBoxContext(" +
            "hasUserProfile=${userProfile != null}, " +
            "conversationHistorySize=${conversationHistory.size}, " +
            "hasTemplateContext=${templateContext != null}, " +
            "extensionDataSize=${extensionData.size})"
    }

    /**
     * 检查是否包含用户个人信息
     *
     * @return 如果包含有效的用户个人信息返回true
     */
    fun hasUserProfile(): Boolean {
        return userProfile != null && userProfile.isValid()
    }

    /**
     * 检查是否包含对话历史
     *
     * @return 如果包含对话历史返回true
     */
    fun hasConversationHistory(): Boolean {
        return conversationHistory.isNotEmpty()
    }

    /**
     * 检查是否包含模板信息
     *
     * @return 如果包含有效的模板信息返回true
     */
    fun hasTemplateContext(): Boolean {
        return templateContext != null && templateContext.isNotEmpty()
    }

    /**
     * 获取最近的对话轮次
     *
     * @param count 要获取的轮次数量，默认为5
     * @return 最近的对话轮次列表
     */
    fun getRecentConversationTurns(count: Int = 5): List<ConversationTurn> {
        return conversationHistory.takeLast(count)
    }

    /**
     * 添加对话轮次
     *
     * 创建包含新对话轮次的上下文副本。
     *
     * @param turn 要添加的对话轮次
     * @return 包含新对话轮次的上下文副本
     */
    fun addConversationTurn(turn: ConversationTurn): ThinkingBoxContext {
        return copy(conversationHistory = conversationHistory + turn)
    }

    /**
     * 更新用户资料
     *
     * 创建包含更新用户资料的上下文副本。
     *
     * @param userProfile 新的用户资料
     * @return 包含更新用户资料的上下文副本
     */
    fun updateUserProfile(userProfile: UserProfileContext): ThinkingBoxContext {
        return copy(userProfile = userProfile)
    }

    companion object {
        /**
         * 创建空的上下文
         *
         * 用于创建不包含任何上下文信息的基础实例。
         *
         * @return 空的ThinkingBoxContext实例
         */
        fun createEmpty(): ThinkingBoxContext {
            return ThinkingBoxContext()
        }

        /**
         * 创建只包含用户资料的上下文
         *
         * @param userProfile 用户资料上下文
         * @return 包含用户资料的ThinkingBoxContext实例
         */
        fun createWithUserProfile(userProfile: UserProfileContext): ThinkingBoxContext {
            return ThinkingBoxContext(userProfile = userProfile)
        }

        /**
         * 创建包含对话历史的上下文
         *
         * @param conversationHistory 对话历史列表
         * @return 包含对话历史的ThinkingBoxContext实例
         */
        fun createWithHistory(conversationHistory: List<ConversationTurn>): ThinkingBoxContext {
            return ThinkingBoxContext(conversationHistory = conversationHistory)
        }

        /**
         * 创建完整的上下文
         *
         * @param userProfile 用户资料上下文
         * @param conversationHistory 对话历史列表
         * @param templateContext 模板上下文
         * @return 完整的ThinkingBoxContext实例
         */
        fun createComplete(
            userProfile: UserProfileContext,
            conversationHistory: List<ConversationTurn> = emptyList(),
            templateContext: Map<String, Any>? = null,
        ): ThinkingBoxContext {
            return ThinkingBoxContext(
                userProfile = userProfile,
                conversationHistory = conversationHistory,
                templateContext = templateContext,
            )
        }
    }
}
