package com.example.gymbro.core.network.output

import com.example.gymbro.core.logging.GymBroLogTags
import com.example.gymbro.core.network.logging.TokenLogCollector
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.onEach
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 直接输出通道 - 新架构输出层
 *
 * 设计目标：
 * - 直接流式输出到ThinkingBox
 * - 零中间缓冲，最小延迟
 * - 支持多个订阅者
 * - 背压控制和错误恢复
 * - 🔥 【新增】集成RAW TOKEN批量日志采集
 */
@Singleton
class DirectOutputChannel @Inject constructor(
    private val tokenLogCollector: TokenLogCollector, // 🔥 【新增】Token日志采集器
) {

    companion object {
        private val TAG = GymBroLogTags.CoreNetwork.OUTPUT_DIRECT
        private const val OUTPUT_BUFFER_CAPACITY = 64 // 输出缓冲：64个token

        // 🏷️ 【日志统计】输出统计配置
        private const val STATS_LOG_INTERVAL = 60_000L // 60秒记录一次统计
    }

    // 直接输出流
    private val _outputFlow = MutableSharedFlow<OutputToken>(
        replay = 0,
        extraBufferCapacity = OUTPUT_BUFFER_CAPACITY,
        onBufferOverflow = kotlinx.coroutines.channels.BufferOverflow.SUSPEND,
    )

    /**
     * 只读的输出流，供ThinkingBox订阅
     */
    val outputFlow: SharedFlow<OutputToken> = _outputFlow.asSharedFlow()

    // 🏷️ 【日志统计】输出统计信息
    @Volatile
    private var totalTokensOutput = 0L
    @Volatile
    private var totalSubscribers = 0L
    @Volatile
    private var activeSubscribers = 0
    @Volatile
    private var lastStatsLogTime = 0L

    /**
     * 发送处理后的token到输出通道
     *
     * @param token 处理后的token内容
     * @param conversationId 会话ID
     * @param contentType 内容类型
     * @param metadata 附加元数据
     */
    suspend fun sendToken(
        token: String,
        conversationId: String,
        contentType: com.example.gymbro.core.network.detector.ContentType,
        metadata: Map<String, Any> = emptyMap(),
    ) {
        // 🔥 【端到端追踪】强制ERROR级别日志追踪输出通道
        Timber.tag(
            "TB-E2E-TRACE",
        ).e(
            "🔍 [输出通道接收] conversationId=$conversationId, contentType=$contentType, token='${token.take(
                100,
            )}...'",
        )

        if (token.isEmpty()) {
            Timber.tag("TB-E2E-TRACE").e("⚠️ [输出通道跳过] 空token")
            return
        }

        val outputToken = OutputToken(
            content = token,
            conversationId = conversationId,
            contentType = contentType,
            timestamp = System.currentTimeMillis(),
            metadata = metadata,
        )

        try {
            _outputFlow.emit(outputToken)
            totalTokensOutput++

            // 🏷️ 【日志统计】定期记录输出统计
            logOutputStatsIfNeeded()

            // 🔥 【RAW TOKEN日志采集】收集输出token
            collectOutputTokenForLogging(token, "ThinkingBox", conversationId)

        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ [输出失败] conversationId=$conversationId, error=${e.message}")
            throw e
        }
    }

    /**
     * 批量发送token（用于缓冲区清空）
     *
     * @param tokens token列表
     * @param conversationId 会话ID
     * @param contentType 内容类型
     */
    suspend fun sendTokenBatch(
        tokens: List<String>,
        conversationId: String,
        contentType: com.example.gymbro.core.network.detector.ContentType,
    ) {
        if (tokens.isEmpty()) return

        val timestamp = System.currentTimeMillis()

        tokens.forEach { token ->
            if (token.isNotEmpty()) {
                val outputToken = OutputToken(
                    content = token,
                    conversationId = conversationId,
                    contentType = contentType,
                    timestamp = timestamp,
                    metadata = mapOf("batch" to true),
                )

                _outputFlow.emit(outputToken)
                totalTokensOutput++

                // 🔥 【RAW TOKEN日志采集】收集批量输出token
                collectOutputTokenForLogging(token, "ThinkingBox", conversationId)
            }
        }

        Timber.tag(TAG).d(
            "📤 批量输出: conversationId=$conversationId, " +
                "count=${tokens.size}, type=$contentType",
        )
    }

    /**
     * 订阅特定会话的输出流
     *
     * @param conversationId 会话ID
     * @return 过滤后的输出流
     */
    fun subscribeToConversation(conversationId: String): Flow<OutputToken> {
        activeSubscribers++
        totalSubscribers++

        Timber.tag(TAG).d("🔗 [订阅] conversationId=$conversationId, 活跃订阅者=$activeSubscribers")

        return outputFlow
            .filter { token ->
                token.conversationId == conversationId
            }
            .onEach { outputToken ->
                // 🔥 【端到端追踪】记录每个token的分发
                Timber.tag(
                    "TB-E2E-TRACE",
                ).e("📨 [输出通道分发] conversationId=$conversationId, token='${outputToken.content.take(100)}...'")
            }
    }

    /**
     * 取消订阅
     */
    fun unsubscribe() {
        activeSubscribers = maxOf(0, activeSubscribers - 1)
    }

    /**
     * 获取输出通道状态
     */
    fun getChannelStatus(): OutputChannelStatus {
        return OutputChannelStatus(
            totalTokensOutput = totalTokensOutput,
            activeSubscribers = activeSubscribers,
            bufferCapacity = OUTPUT_BUFFER_CAPACITY,
        )
    }

    /**
     * 清理输出通道
     */
    suspend fun cleanup() {
        // 🔥 【RAW TOKEN日志采集】最终刷新输出token缓冲区
        flushOutputTokenCollectionBuffer("ThinkingBox", "cleanup")

        Timber.tag(TAG).i(
            "🧹 清理DirectOutputChannel: totalTokens=$totalTokensOutput, " +
                "subscribers=$activeSubscribers",
        )
        activeSubscribers = 0
    }

    /**
     * 🔥 【RAW TOKEN日志采集】收集输出token用于批量日志记录
     */
    private suspend fun collectOutputTokenForLogging(token: String, target: String, conversationId: String) {
        // 使用一个局部变量来避免在临界区内调用挂起函数
        val shouldFlush: Boolean
        val tokensToFlush: List<String>

        synchronized(outputTokenCollectionBuffer) {
            outputTokenCollectionBuffer.add(token)

            // 检查是否需要刷新
            shouldFlush = outputTokenCollectionBuffer.size >= OUTPUT_TOKEN_BATCH_SIZE
            tokensToFlush = if (shouldFlush) {
                val tokens = outputTokenCollectionBuffer.toList()
                outputTokenCollectionBuffer.clear()
                tokens
            } else {
                emptyList()
            }
        }

        // 在临界区外执行挂起函数
        if (shouldFlush) {
            try {
                tokenLogCollector.collectOutputTokens(
                    tokens = tokensToFlush,
                    target = target,
                    conversationId = conversationId,
                )
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "⚠️ 输出Token日志采集失败: target=$target")
            }
        }
    }

    /**
     * 🔥 【RAW TOKEN日志采集】刷新输出缓冲区中剩余的token
     */
    private suspend fun flushOutputTokenCollectionBuffer(target: String, conversationId: String) {
        val tokensToFlush: List<String>

        synchronized(outputTokenCollectionBuffer) {
            tokensToFlush = if (outputTokenCollectionBuffer.isNotEmpty()) {
                val tokens = outputTokenCollectionBuffer.toList()
                outputTokenCollectionBuffer.clear()
                tokens
            } else {
                emptyList()
            }
        }

        // 在临界区外执行挂起函数
        if (tokensToFlush.isNotEmpty()) {
            try {
                tokenLogCollector.collectOutputTokens(
                    tokens = tokensToFlush,
                    target = target,
                    conversationId = conversationId,
                )
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "⚠️ 最终输出Token刷新失败: target=$target")
            }
        }
    }
}

/**
 * 📤 输出Token数据结构
 */
data class OutputToken(
    val content: String,
    val conversationId: String,
    val contentType: com.example.gymbro.core.network.detector.ContentType,
    val timestamp: Long,
    val metadata: Map<String, Any> = emptyMap(),
)

/**
 * 📊 输出通道状态信息
 */
data class OutputChannelStatus(
    val totalTokensOutput: Long,
    val activeSubscribers: Int,
    val bufferCapacity: Int,
)

    /**
     * 🏷️ 【日志统计】定期记录输出统计
     */
    private fun logOutputStatsIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastStatsLogTime >= STATS_LOG_INTERVAL) {
            Timber.tag(TAG).i(
                "📊 [输出统计] 已输出tokens=$totalTokensOutput, " +
                "活跃订阅者=$activeSubscribers, 总订阅者=$totalSubscribers"
            )

            lastStatsLogTime = currentTime
        }
    }
}
