package com.example.gymbro.features.coach.aicoach.internal.effect

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.config.AiProviderManager
import com.example.gymbro.domain.coach.executor.FunctionCallExecutor
import com.example.gymbro.domain.coach.usecase.BuildFunctionCallMessageUseCase
import com.example.gymbro.domain.memory.usecase.RecallMemoriesUseCase
import com.example.gymbro.domain.memory.usecase.SaveMemoryUseCase
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.coach.aicoach.getDisplayName
import com.example.gymbro.features.coach.aicoach.internal.effect.handlers.MessagingEffectHandler
import com.example.gymbro.features.coach.aicoach.internal.effect.handlers.SessionEffectHandler
import com.example.gymbro.features.coach.aicoach.internal.effect.handlers.StreamEffectHandler
import com.example.gymbro.navigation.CrossModuleRoutes
import com.example.gymbro.navigation.deeplink.DeepLinkManager
import com.example.gymbro.shared.models.memory.MemoryRecord
import com.example.gymbro.shared.models.memory.MemoryTier
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import timber.log.Timber
import javax.inject.Inject

/**
 * AI Coach Effect Handler - v6.0-协调器模式的副作用处理器
 *
 * 设计原则：
 * 1. 协调器模式 - 不直接处理业务逻辑，只负责分发到专门的Handler
 * 2. 单一职责 - 每个Handler专注特定领域的Effect处理
 * 3. 结构化并发 - 使用注入的handlerScope管理协程生命周期
 * 4. 清晰分工 - MessagingEffectHandler、SessionEffectHandler、StreamEffectHandler
 * 5. 统一初始化 - 所有Handler使用相同的初始化接口
 */
internal class AiCoachEffectHandler @Inject constructor(
    private val messagingHandler: MessagingEffectHandler,
    private val sessionHandler: SessionEffectHandler,
    private val streamHandler: StreamEffectHandler,
    private val recallMemoriesUseCase: RecallMemoriesUseCase,
    private val saveMemoryUseCase: SaveMemoryUseCase,
    private val aiProviderManager: AiProviderManager,
    private val functionCallExecutor: FunctionCallExecutor,
    private val deepLinkManager: DeepLinkManager,
    private val buildFunctionCallMessageUseCase: BuildFunctionCallMessageUseCase,
    private val resourceProvider: ResourceProvider,
    // 🔥 Coach-ThinkingBox重构：新增依赖
    private val layeredPromptBuilder: com.example.gymbro.core.ai.prompt.builder.LayeredPromptBuilder,
) {

    private lateinit var handlerScope: CoroutineScope
    private lateinit var sendIntent: (AiCoachContract.Intent) -> Unit
    private lateinit var aiCoachSessionHandler:
        com.example.gymbro.features.coach.aicoach.internal.viewmodel.AiCoachSessionHandler

    // 🔥 Coach-ThinkingBox重构：ThinkingBox显示接口（运行时注入）
    private var thinkingBoxDisplay: com.example.gymbro.features.thinkingbox.api.ThinkingBoxDisplay? = null

    /**
     * 🔥 【消息保存修复】初始化EffectHandler - v6.0-协调器模式
     *
     * @param scope 由ViewModel提供的handlerScope
     * @param intentSender Intent发送器
     * @param stateProvider 状态提供器
     * @param sessionHandler AiCoachSessionHandler实例，用于正确的消息保存状态管理
     */
    fun initialize(
        scope: CoroutineScope,
        intentSender: (AiCoachContract.Intent) -> Unit,
        stateProvider: () -> AiCoachContract.State,
        sessionHandler: com.example.gymbro.features.coach.aicoach.internal.viewmodel.AiCoachSessionHandler,
    ) {
        this.handlerScope = scope
        this.sendIntent = intentSender
        this.aiCoachSessionHandler = sessionHandler

        // 初始化所有子Handler
        messagingHandler.initialize(scope, intentSender)
        this.sessionHandler.initialize(scope, intentSender, stateProvider)
        streamHandler.initialize(scope, intentSender, stateProvider)

        Timber.d("🔥 AiCoachEffectHandler协调器初始化完成")
        Timber.tag("MESSAGE-SAVE").d("🔥 [AiCoachEffectHandler] 初始化完成，已注入AiCoachSessionHandler")
    }

    /**
     * 🔥 【消息保存修复】处理单个Effect - 协调器模式的核心分发方法
     */
    fun handle(effect: AiCoachContract.Effect) {
        Timber.d("🎯 协调器处理Effect: ${effect::class.simpleName}")
        Timber.tag("EFFECT-FLOW").d("🎯 [AiCoachEffectHandler] 收到Effect: ${effect::class.simpleName}")

        when (effect) {
            // === 会话管理相关 - 委托给SessionEffectHandler ===
            is AiCoachContract.Effect.LoadInitialSession -> {
                Timber.tag("HISTORY-FIX").d("📋 [Phase1] AiCoachEffectHandler处理LoadInitialSession")
                Timber.tag("EFFECT-FLOW").d("📋 [AiCoachEffectHandler] 处理LoadInitialSession")
                sessionHandler.handleLoadInitialSession()
            }

            is AiCoachContract.Effect.CreateNewSession -> {
                Timber
                    .tag("HISTORY-FIX")
                    .d(
                        "🆕 [Phase2] AiCoachEffectHandler处理CreateNewSession - 修复：使用AiCoachSessionHandler而不是SessionEffectHandler",
                    )
                Timber.tag("EFFECT-FLOW").d("🆕 [AiCoachEffectHandler] 处理CreateNewSession")
                // 🔥 关键修复：使用正确的AiCoachSessionHandler而不是SessionEffectHandler
                aiCoachSessionHandler.handleCreateNewSession()
            }

            is AiCoachContract.Effect.LoadSessionHistory -> {
                Timber.tag("HISTORY-FIX").d("📚 [Phase1] AiCoachEffectHandler处理LoadSessionHistory")
                Timber.tag("EFFECT-FLOW").d("📚 [AiCoachEffectHandler] 处理LoadSessionHistory")
                sessionHandler.handleLoadSessionHistory()
            }

            is AiCoachContract.Effect.SwitchSession -> {
                Timber
                    .tag("HISTORY-FIX")
                    .d(
                        "🔄 [Phase3] AiCoachEffectHandler处理SwitchSession: ${effect.sessionId} - 修复：使用AiCoachSessionHandler",
                    )
                Timber.tag("EFFECT-FLOW").d("🔄 [AiCoachEffectHandler] 处理SwitchSession: ${effect.sessionId}")
                // 🔥 关键修复：使用正确的AiCoachSessionHandler而不是SessionEffectHandler
                aiCoachSessionHandler.handleSwitchSession(effect.sessionId)
            }

            is AiCoachContract.Effect.SaveAiMessage -> {
                Timber
                    .tag("EFFECT-FLOW")
                    .d("🤖 [AiCoachEffectHandler] 处理SaveAiMessage: ${effect.aiResponseId}")
                Timber
                    .tag("MESSAGE-SAVE")
                    .i(
                        "🎯 [AiCoachEffectHandler] 路由SaveAiMessage到AiCoachSessionHandler: messageId=${effect.aiResponseId}",
                    )
                // 🔥 【架构清理】统一使用AiCoachSessionHandler，确保状态管理和ThinkingBox集成正确
                aiCoachSessionHandler.handleSaveAiMessage(effect)
            }

            is AiCoachContract.Effect.SaveUserMessage -> {
                Timber
                    .tag("EFFECT-FLOW")
                    .d("👤 [AiCoachEffectHandler] 处理SaveUserMessage: ${effect.userMessageId}")
                Timber
                    .tag("MESSAGE-SAVE")
                    .i(
                        "🎯 [AiCoachEffectHandler] 路由SaveUserMessage到AiCoachSessionHandler: messageId=${effect.userMessageId}, sessionId=${effect.sessionId}",
                    )
                // 🔥 【架构清理】统一使用AiCoachSessionHandler，确保MESSAGE-SAVE日志和状态管理
                aiCoachSessionHandler.handleSaveUserMessage(effect)
            }
            is AiCoachContract.Effect.MigrateMessagesToSession ->
                sessionHandler.handleMigrateMessagesToSession(
                    effect,
                )
            // 🔥 【单一数据源修复】移除重复的HistoryPersister Effect处理
            // 统一使用ChatRaw架构的SaveAiMessage Effect

            // === 消息和搜索相关 - 委托给MessagingEffectHandler ===
            is AiCoachContract.Effect.LoadQuickActionCategories -> messagingHandler.handleLoadQuickActionCategories()
            is AiCoachContract.Effect.LoadQuickActionById -> messagingHandler.handleLoadQuickActionById(
                effect,
            )
            is AiCoachContract.Effect.LoadQuickActions -> messagingHandler.handleLoadQuickActions()
            is AiCoachContract.Effect.PerformSearch -> messagingHandler.handlePerformSearch(effect)
            is AiCoachContract.Effect.PrefillInputAndNavigate -> messagingHandler.handlePrefillInputAndNavigate(
                effect,
            )
            is AiCoachContract.Effect.ShowError -> messagingHandler.handleShowError(effect)
            is AiCoachContract.Effect.GenerateSummary -> messagingHandler.handleGenerateSummary(effect)

            // === 流式响应和AI相关 - 委托给StreamEffectHandler ===
            is AiCoachContract.Effect.StartAiStream -> {
                Timber
                    .tag("TOKEN-FLOW")
                    .d("🚀 [AiCoachEffectHandler] 委托StartAiStream给StreamEffectHandler")
                // 🔥 【MVI 2.0黄金标准修复】委托给专门的StreamEffectHandler处理
                streamHandler.handleStartAiStream(effect)
            }

            // === 兼容性处理 - 为了处理原有Effect定义 ===
            is AiCoachContract.Effect.CreateSession -> {
                // 将CreateSession重定向到CreateNewSession
                Timber.d("🔄 重定向CreateSession到CreateNewSession")
                sessionHandler.handleCreateNewSession()
            }
            is AiCoachContract.Effect.LoadSession -> {
                // 将LoadSession重定向到SwitchSession
                Timber.d("🔄 重定向LoadSession到SwitchSession: ${effect.sessionId}")
                sessionHandler.handleSwitchSession(
                    AiCoachContract.Effect.SwitchSession(effect.sessionId),
                )
            }
            // 🔥 修复：移除重复的CreateNewSession处理，已在上面第84-89行正确处理

            is AiCoachContract.Effect.LoadAllSessions -> {
                // 将LoadAllSessions重定向到LoadSessionHistory
                Timber.d("🔄 重定向LoadAllSessions到LoadSessionHistory")
                sessionHandler.handleLoadSessionHistory()
            }

            // === 🔥 门槛2：用户资料提示Effect ===
            is AiCoachContract.Effect.NavigateToProfilePage -> {
                Timber.d("🔥 [门槛2] 处理跳转到个人资料页面")
                handleNavigateToProfile()
            }

            // === 🔥 History RAG上下文Effect ===
            is AiCoachContract.Effect.FetchHistoryContext -> {
                Timber.d("🔥 处理FetchHistoryContext: sessionId=${effect.sessionId}")
                handleFetchHistoryContext(effect)
            }
            is AiCoachContract.Effect.ClearHistoryRagContext -> {
                Timber.d("🔥 处理ClearHistoryRagContext")
                handleClearHistoryRagContext()
            }

            // === 🔥 记忆系统Effect - Sprint-2 ===
            is AiCoachContract.Effect.RecallMemoriesFromService -> {
                Timber.d("🧠 处理记忆召回: userId=${effect.userId}, query=${effect.query}")
                handlerScope.launch {
                    val result = recallMemoriesUseCase(
                        RecallMemoriesUseCase.Params(
                            userId = effect.userId,
                            query = effect.query,
                            tokenBudget = effect.tokenBudget,
                        ),
                    )

                    when (result) {
                        is ModernResult.Success -> {
                            Timber.d("✅ 记忆召回成功: ${result.data.size}条")
                            // 转换 MemorySearchResult 到 MemoryRecord
                            val memoryRecords = result.data.map { searchResult: com.example.gymbro.shared.models.memory.MemorySearchResult ->
                                searchResult.memory
                            }
                            sendIntent(
                                AiCoachContract.Intent.MemoryRecalledResult(
                                    memoryRecords.map { "Memory: ${it.id}" },
                                ),
                            )
                        }
                        is ModernResult.Error -> {
                            Timber.e("❌ 记忆召回失败: ${result.error}")
                            val errorText = when (result.error) {
                                is BusinessErrors.BusinessError -> UiText.DynamicString(result.error.message)
                                else -> UiText.DynamicString("记忆召回失败")
                            }
                            sendIntent(AiCoachContract.Intent.MemoryErrorResult("Memory error occurred"))
                        }
                        is ModernResult.Loading -> {
                            Timber.w("⚠️ 记忆召回返回Loading状态，这不应该发生")
                        }
                    }
                }
            }

            is AiCoachContract.Effect.SaveMemoryToService -> {
                Timber.d("💾 处理记忆保存: userId=${effect.userId}, tier=${effect.tier}")
                handlerScope.launch {
                    val memoryRecord = when (effect.tier) {
                        MemoryTier.ECM -> MemoryRecord.createEcm(
                            userId = effect.userId,
                            content = kotlinx.serialization.json.buildJsonObject {
                                put("content", kotlinx.serialization.json.JsonPrimitive(effect.content))
                            },
                            importance = effect.importance,
                        )
                        MemoryTier.DWM -> MemoryRecord.createDwm(
                            userId = effect.userId,
                            content = kotlinx.serialization.json.buildJsonObject {
                                put("content", kotlinx.serialization.json.JsonPrimitive(effect.content))
                            },
                            importance = effect.importance,
                        )
                        MemoryTier.UPM -> MemoryRecord.createUpm(
                            userId = effect.userId,
                            content = kotlinx.serialization.json.buildJsonObject {
                                put("content", kotlinx.serialization.json.JsonPrimitive(effect.content))
                            },
                            importance = effect.importance,
                        )
                        MemoryTier.GIM -> MemoryRecord.createGim(
                            userId = effect.userId,
                            content = kotlinx.serialization.json.buildJsonObject {
                                put("content", kotlinx.serialization.json.JsonPrimitive(effect.content))
                            },
                            importance = effect.importance,
                        )
                    }

                    val result = saveMemoryUseCase(SaveMemoryUseCase.Params.single(memoryRecord))

                    when (result) {
                        is ModernResult.Success -> {
                            Timber.d("✅ 记忆保存成功")
                            sendIntent(AiCoachContract.Intent.MemorySavedResult(memoryId = "success"))
                        }
                        is ModernResult.Error -> {
                            Timber.e("❌ 记忆保存失败: ${result.error}")
                            sendIntent(AiCoachContract.Intent.MemorySavedResult(memoryId = "failed"))
                        }
                        is ModernResult.Loading -> {
                            Timber.w("⚠️ 记忆保存返回Loading状态，这不应该发生")
                        }
                    }
                }
            }

            // === 🔥 处理Function Call执行 - 完整域架构支持 ===
            is AiCoachContract.Effect.ExecuteFunctionCall -> {
                handleExecuteFunctionCall(effect)
            }
            is AiCoachContract.Effect.FunctionCallCompleted -> {
                Timber.d("🔥 Function Call结果已在Reducer中处理")
                // 这个Effect只是通知，实际处理在Reducer中
            }

            // === Stream处理相关Effect ===
            is AiCoachContract.Effect.DetectFunctionCall -> {
                Timber.d("🔍 处理Function Call检测")
                streamHandler.handleDetectFunctionCall(effect)
            }
            is AiCoachContract.Effect.ProcessTemplateGeneration -> {
                Timber.d("📝 处理模板生成")
                streamHandler.handleProcessTemplateGeneration(effect)
            }
            is AiCoachContract.Effect.ProcessPlanGeneration -> {
                Timber.d("📋 处理计划生成")
                streamHandler.handleProcessPlanGeneration(effect)
            }

            // === 新增的Effect分支 ===
            is AiCoachContract.Effect.SwitchApiProvider -> {
                Timber.d("🔄 处理API提供商切换: ${effect.provider}")
                handleSwitchApiProvider(effect.provider)
            }
            is AiCoachContract.Effect.SwitchPromptMode -> {
                Timber.d("🔄 处理Prompt模式切换: ${effect.mode}")
                handleSwitchPromptMode(effect.mode)
            }
            is AiCoachContract.Effect.SearchMessages -> {
                messagingHandler.handlePerformSearch(
                    AiCoachContract.Effect.PerformSearch(effect.query, effect.sessionId),
                )
            }
            // 🔥 【修复】添加历史消息流设置Effect处理
            is AiCoachContract.Effect.SetupHistoryFlow -> {
                Timber.d("🔥 处理SetupHistoryFlow Effect: sessionId=${effect.sessionId}")
                // 这个Effect在AiCoachViewModel中直接处理，这里只是记录
                // 实际的setupHistoryFlow调用在ViewModel的observeEffects中完成
            }

            // === 🔥 导航Effect处理 - 使用DEEPLINK系统 ===
            is AiCoachContract.Effect.NavigateToWorkout -> {
                Timber.d("🎯 处理导航Effect: 跳转到训练页面")
                handleDeepLinkNavigation("gymbro://workout/main")
            }

            is AiCoachContract.Effect.NavigateToTemplates -> {
                Timber.d("🎯 处理导航Effect: 跳转到模板页面")
                handleDeepLinkNavigation("gymbro://workout/template/new")
            }

            is AiCoachContract.Effect.NavigateToPlans -> {
                Timber.d("🎯 处理导航Effect: 跳转到计划页面")
                handleDeepLinkNavigation("gymbro://workout/plan/new")
            }

            is AiCoachContract.Effect.NavigateToExerciseLibrary -> {
                Timber.d("🎯 处理导航Effect: 跳转到动作库页面")
                handleDeepLinkNavigation("gymbro://exercise/library")
            }

            // === Function Call 消息构建 ===
            is AiCoachContract.Effect.BuildFunctionCallMessage -> {
                handleBuildFunctionCallMessage(effect.functionCallName)
            }

            // === 🔥 Coach-ThinkingBox重构：AI请求发送相关 Effect ===
            is AiCoachContract.Effect.BuildAndSendPrompt -> {
                Timber.d("🚀 [Coach-ThinkingBox重构] 处理BuildAndSendPrompt: messageId=${effect.messageId}")
                handleBuildAndSendPrompt(effect)
            }

            is AiCoachContract.Effect.LaunchThinkingBoxDisplay -> {
                Timber.d("🎯 [Coach-ThinkingBox重构] 处理LaunchThinkingBoxDisplay: messageId=${effect.messageId}")
                handleLaunchThinkingBoxDisplay(effect)
            }
        }
    }

    /**
     * 🔥 处理Function Call执行 - 使用真实的Domain层执行器
     *
     * 此方法替换了原有的 handleExecuteFunctionCallMock 及其所有辅助 mock 方法。
     */
    private fun handleExecuteFunctionCall(effect: AiCoachContract.Effect.ExecuteFunctionCall) {
        Timber.d("🔥 开始执行Function Call (真实架构): ${effect.functionCallName}")

        handlerScope.launch {
            try {
                // 1. 解析JSON参数
                val argumentsMap = try {
                    Json.parseToJsonElement(effect.arguments).jsonObject.mapValues {
                        it.value.jsonPrimitive.content
                    }
                } catch (e: Exception) {
                    Timber.w(e, "解析Function Call参数失败，使用空Map: ${effect.arguments}")
                    emptyMap()
                }

                // 2. 创建执行器所需的FunctionCall对象
                val executorCall = FunctionCallExecutor.FunctionCall(
                    name = effect.functionCallName,
                    arguments = argumentsMap,
                )

                // 3. 调用真实的FunctionCallExecutor，传递UI动作触发回调
                Timber.d("🚀 [Debug] 开始执行Function Call: ${effect.functionCallName}")
                Timber.d("📋 [Debug] 参数内容: ${effect.arguments}")

                val result = functionCallExecutor.executeFunctionCall(
                    functionCall = executorCall,
                    onActionTrigger = { workoutAction ->
                        Timber.d("🎯 Function Call触发UI动作: ${workoutAction::class.simpleName}")
                        // 注意：这里只是触发基本的WorkoutAction，精确导航将在后面处理
                        handleWorkoutAction(workoutAction)
                    },
                )

                // 4. 详细的结果日志
                when (result) {
                    is ModernResult.Success -> {
                        val functionResult = result.data
                        if (functionResult.success) {
                            Timber.d("✅ [Debug] Function Call执行成功")
                            Timber.d("📊 [Debug] 返回数据: ${functionResult.data}")
                            Timber.d("🛤️ [Debug] 执行路径: ${functionResult.executionPath}")
                        } else {
                            Timber.w("⚠️ [Debug] Function Call执行失败")
                            Timber.w("❌ [Debug] 错误信息: ${functionResult.error}")
                            Timber.w("🛤️ [Debug] 执行路径: ${functionResult.executionPath}")
                        }
                    }
                    is ModernResult.Error -> {
                        Timber.e("💥 [Debug] Function Call系统错误: ${result.error}")
                    }
                    is ModernResult.Loading -> {
                        Timber.w("⏳ [Debug] Function Call执行超时")
                    }
                }

                // 4. 将 domain 层的 FunctionResult 转换为 features 层的 FunctionCallResult
                val functionCallResult = when (result) {
                    is ModernResult.Success -> {
                        val data = result.data
                        AiCoachContract.FunctionCallResult(
                            functionName = effect.functionCallName,
                            success = data.success,
                            data = data.data,
                            error = data.error,
                            executionPath = data.executionPath,
                            actionTriggered = data.actionTriggered,
                        )
                    }
                    is ModernResult.Error -> AiCoachContract.FunctionCallResult(
                        functionName = effect.functionCallName,
                        success = false,
                        error = "执行器错误: ${result.error.message}",
                        executionPath = "executor_error",
                    )
                    is ModernResult.Loading -> AiCoachContract.FunctionCallResult(
                        functionName = effect.functionCallName,
                        success = false,
                        error = "执行器超时",
                        executionPath = "executor_timeout",
                    )
                }

                // 🔥 5. 处理精确导航 - 如果有resourceId，执行精确导航
                if (result is ModernResult.Success && result.data.success) {
                    val functionResult = result.data
                    val resourceType = functionResult.resourceType
                    val resourceId = functionResult.resourceId
                    if (!resourceId.isNullOrBlank() && !resourceType.isNullOrBlank()) {
                        Timber.d("🎯 执行精确导航: resourceType=$resourceType, resourceId=$resourceId")
                        handlePreciseNavigation(resourceType, resourceId, functionResult.actionTriggered)
                    }
                }

                Timber.d("✅ Function Call执行成功: ${effect.functionCallName}")
                sendIntent(AiCoachContract.Intent.FunctionCallCompleted(functionCallResult))
            } catch (exception: Exception) {
                val functionCallResult = AiCoachContract.FunctionCallResult(
                    functionName = effect.functionCallName,
                    success = false,
                    error = "执行异常: ${exception.message}",
                    executionPath = "handler_exception",
                )

                Timber.e(exception, "💥 Function Call执行异常: ${effect.functionCallName}")
                sendIntent(AiCoachContract.Intent.FunctionCallCompleted(functionCallResult))
            }
        }
    }

    /**
     * 🔥 处理深度链接导航
     *
     * 使用 DeepLinkManager 来处理跨模块导航，
     * 符合 GymBro 项目的模块化架构原则
     *
     * 🔥 v2.0 增强：添加详细的错误处理和用户反馈
     * 优化：使用 Dispatchers.Main.immediate 避免主线程阻塞
     */
    private fun handleDeepLinkNavigation(deepLink: String) {
        handlerScope.launch(kotlinx.coroutines.Dispatchers.Main.immediate) {
            try {
                Timber.d("🔗 开始处理深度链接导航: $deepLink")

                // 验证深度链接格式
                if (deepLink.isBlank() || !deepLink.startsWith("gymbro://")) {
                    Timber.w("⚠️ 无效的深度链接格式: $deepLink")
                    handleNavigationFailure("无效的导航链接格式")
                    return@launch
                }

                // 使用 withContext 切换到 IO 线程处理深度链接
                val result = kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.IO) {
                    deepLinkManager.handleDeepLink(deepLink)
                }

                // 回到主线程处理结果
                when (result) {
                    is com.example.gymbro.core.error.types.ModernResult.Success -> {
                        if (result.data.success) {
                            Timber.d("✅ 深度链接导航成功: ${result.data.targetRoute}")
                            Timber.d("🎯 导航目标: ${result.data.targetRoute}")
                        } else {
                            Timber.w("⚠️ 深度链接导航失败: ${result.data.message}")
                            handleNavigationFailure("导航失败: ${result.data.message}")
                        }
                    }
                    is com.example.gymbro.core.error.types.ModernResult.Error -> {
                        val errorMessage = result.error.uiMessage?.asString(resourceProvider) ?: "未知错误"
                        Timber.e("💥 深度链接导航异常: $errorMessage")
                        handleNavigationFailure("导航异常: $errorMessage")
                    }
                    is com.example.gymbro.core.error.types.ModernResult.Loading -> {
                        Timber.d("⏳ 深度链接导航处理中...")
                        // Loading 状态通常不应该在这里出现，记录警告
                        Timber.w("⚠️ 深度链接导航返回Loading状态，可能存在超时问题")
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "💥 深度链接导航处理异常: $deepLink")
                handleNavigationFailure("导航处理异常: ${e.message}")
            }
        }
    }

    /**
     * 🔥 处理导航失败的用户反馈
     */
    private fun handleNavigationFailure(errorMessage: String) {
        Timber.w("🚫 导航失败处理: $errorMessage")

        // 发送用户友好的错误提示
        val userFriendlyMessage = when {
            errorMessage.contains("无效的导航链接格式") -> "导航链接格式错误，请稍后重试"
            errorMessage.contains("导航失败") -> "页面跳转失败，请检查网络连接"
            errorMessage.contains("导航异常") -> "系统异常，请稍后重试"
            errorMessage.contains("导航处理异常") -> "导航处理出错，请重新操作"
            else -> "导航失败：$errorMessage"
        }

        // 发送错误Intent显示用户友好的错误提示
        sendIntent(
            AiCoachContract.Intent.ShowError(
                error = userFriendlyMessage,
            ),
        )

        Timber.d("📢 已发送导航错误提示: $userFriendlyMessage")
    }

    /**
     * 🔥 处理WorkoutAction，触发相应的UI导航
     *
     * 当Function Call成功执行并触发WorkoutAction时，
     * 这个方法负责将动作转换为相应的导航操作
     *
     * 🔥 v3.0 增强：支持精确导航，使用resourceId导航到具体资源页面
     */
    private fun handleWorkoutAction(
        action: com.example.gymbro.domain.workout.model.WorkoutAction,
        resourceId: String? = null,
        resourceType: String? = null,
    ) {
        Timber.d(
            "🎯 处理WorkoutAction: ${action::class.simpleName}, resourceId=$resourceId, resourceType=$resourceType",
        )

        try {
            when (action) {
                is com.example.gymbro.domain.workout.model.WorkoutAction.CreateBlankPlan -> {
                    if (resourceId != null && resourceType == "plan") {
                        Timber.d("🎯 精确导航: 导航到计划详情页面 (planId=$resourceId)")
                        handleDeepLinkNavigation(CrossModuleRoutes.buildPlanDetailsDeepLink(resourceId))
                    } else {
                        Timber.d("🎯 通用导航: 创建空白计划")
                        sendIntent(AiCoachContract.Intent.NavigateToPlans)
                    }
                }

                is com.example.gymbro.domain.workout.model.WorkoutAction.GenerateTemplate -> {
                    if (resourceId != null && resourceType == "template") {
                        Timber.d("🎯 精确导航: 导航到模板编辑页面 (templateId=$resourceId)")
                        handleDeepLinkNavigation(CrossModuleRoutes.buildTemplateEditDeepLink(resourceId))
                    } else {
                        Timber.d("🎯 通用导航: 生成模板")
                        sendIntent(AiCoachContract.Intent.NavigateToTemplates)
                    }
                }

                is com.example.gymbro.domain.workout.model.WorkoutAction.CreateCustomExercise -> {
                    if (resourceId != null && resourceType == "exercise") {
                        Timber.d("🎯 精确导航: 导航到动作库页面 (exerciseId=$resourceId)")
                        // 对于动作，我们导航到动作库并可能需要高亮显示新创建的动作
                        handleDeepLinkNavigation(
                            "${CrossModuleRoutes.DEEP_LINK_EXERCISE_LIBRARY}?highlight=$resourceId",
                        )
                    } else {
                        Timber.d("🎯 通用导航: 创建自定义动作")
                        sendIntent(AiCoachContract.Intent.NavigateToExerciseLibrary)
                    }
                }

                is com.example.gymbro.domain.workout.model.WorkoutAction.StartNewSession -> {
                    if (resourceId != null && resourceType == "session") {
                        Timber.d("🎯 精确导航: 导航到训练会话页面 (sessionId=$resourceId)")
                        handleDeepLinkNavigation(CrossModuleRoutes.buildSessionDeepLink(resourceId))
                    } else {
                        Timber.d("🎯 通用导航: 开始新训练会话")
                        sendIntent(AiCoachContract.Intent.NavigateToWorkout)
                    }
                }

                is com.example.gymbro.domain.workout.model.WorkoutAction.LoadFromTemplate -> {
                    if (resourceId != null && resourceType == "session") {
                        Timber.d("🎯 精确导航: 从模板加载到训练会话页面 (sessionId=$resourceId)")
                        handleDeepLinkNavigation(CrossModuleRoutes.buildSessionDeepLink(resourceId))
                    } else {
                        Timber.d("🎯 通用导航: 从模板加载训练")
                        sendIntent(AiCoachContract.Intent.NavigateToWorkout)
                    }
                }

                is com.example.gymbro.domain.workout.model.WorkoutAction.AddSet -> {
                    Timber.d("🎯 WorkoutAction.AddSet 不需要导航，仅记录")
                    // AddSet 动作不需要导航，仅用于记录训练组
                }

                is com.example.gymbro.domain.workout.model.WorkoutAction.CompleteWorkout -> {
                    Timber.d("🎯 WorkoutAction.CompleteWorkout 不需要导航，仅记录")
                    // CompleteWorkout 动作不需要导航，仅用于完成训练
                }

                else -> {
                    Timber.w("⚠️ 未处理的WorkoutAction: ${action::class.simpleName}")
                    Timber.w("⚠️ 可能需要在 handleWorkoutAction 中添加对应的导航逻辑")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "💥 处理WorkoutAction异常: ${action::class.simpleName}")
        }
    }

    /**
     * 🔥 处理精确导航
     *
     * 根据resourceType和resourceId执行精确的深度链接导航
     * 这是Function Call功能的核心导航逻辑
     */
    private fun handlePreciseNavigation(
        resourceType: String,
        resourceId: String,
        actionTriggered: String?,
    ) {
        Timber.d("🎯 执行精确导航: resourceType=$resourceType, resourceId=$resourceId, action=$actionTriggered")

        try {
            val deepLink = when (resourceType) {
                "template" -> {
                    Timber.d("🎯 导航到模板编辑页面: templateId=$resourceId")
                    CrossModuleRoutes.buildTemplateEditDeepLink(resourceId)
                }

                "plan" -> {
                    Timber.d("🎯 导航到计划详情页面: planId=$resourceId")
                    CrossModuleRoutes.buildPlanDetailsDeepLink(resourceId)
                }

                "session" -> {
                    Timber.d("🎯 导航到训练会话页面: sessionId=$resourceId")
                    CrossModuleRoutes.buildSessionDeepLink(resourceId)
                }

                "exercise" -> {
                    Timber.d("🎯 导航到动作库页面: exerciseId=$resourceId")
                    "${CrossModuleRoutes.DEEP_LINK_EXERCISE_LIBRARY}?highlight=$resourceId"
                }

                else -> {
                    Timber.w("⚠️ 未知的资源类型: $resourceType，使用通用导航")
                    null
                }
            }

            if (deepLink != null) {
                Timber.d("🔗 执行深度链接导航: $deepLink")
                handleDeepLinkNavigation(deepLink)
            } else {
                // 回退到基于actionTriggered的通用导航
                Timber.d("🔄 回退到通用导航: actionTriggered=$actionTriggered")
                handleFallbackNavigation(actionTriggered)
            }
        } catch (e: Exception) {
            Timber.e(e, "💥 精确导航异常: resourceType=$resourceType, resourceId=$resourceId")
            // 回退到通用导航
            handleFallbackNavigation(actionTriggered)
        }
    }

    /**
     * 🔥 回退导航处理
     *
     * 当精确导航失败时，根据actionTriggered执行通用导航
     */
    private fun handleFallbackNavigation(actionTriggered: String?) {
        Timber.d("🔄 执行回退导航: actionTriggered=$actionTriggered")

        when (actionTriggered) {
            "GenerateTemplate" -> sendIntent(AiCoachContract.Intent.NavigateToTemplates)
            "CreateBlankPlan" -> sendIntent(AiCoachContract.Intent.NavigateToPlans)
            "StartNewSession", "LoadFromTemplate" -> sendIntent(AiCoachContract.Intent.NavigateToWorkout)
            "CreateCustomExercise" -> sendIntent(AiCoachContract.Intent.NavigateToExerciseLibrary)
            else -> {
                Timber.w("⚠️ 未知的actionTriggered: $actionTriggered，不执行导航")
            }
        }
    }

    /**
     * 处理API提供商切换
     */
    private fun handleSwitchApiProvider(provider: AiCoachContract.ApiProvider) {
        handlerScope.launch {
            try {
                Timber.d("🔄 开始切换API提供商: $provider")

                // 🔥 修复：使用 AiCoachContract.ApiProvider 的 id 属性，确保与 local.properties 配置一致
                val providerName = provider.id

                Timber.d("🎯 映射Provider: $provider → $providerName")

                // 通过 AiProviderManager 切换 Provider
                val switchSuccess = aiProviderManager.switchToProvider(providerName)
                if (!switchSuccess) {
                    // 🔥 增强错误信息：显示可用的 Provider 列表
                    val availableProviders = aiProviderManager.availableProviders.map { it.name }
                    Timber.w("🚨 可用的Provider列表: $availableProviders")
                    throw Exception("Provider切换失败: $providerName，可用Provider: $availableProviders")
                }

                // 🔥 验证切换结果：检查当前Provider是否真的切换了
                val currentProvider = aiProviderManager.currentProvider.value
                Timber.d("🔍 切换后当前Provider: ${currentProvider.name}, 模型: ${currentProvider.defaultModel}")

                // 🔥 优化：确保UI状态与实际Provider状态同步
                val actualApiProvider = when (currentProvider.name) {
                    "deepseek" -> AiCoachContract.ApiProvider.DEEPSEEK
                    "google_gemini" -> AiCoachContract.ApiProvider.GOOGLE_GEMINI
                    "openai" -> AiCoachContract.ApiProvider.OPENAI
                    else -> provider // 回退到原始provider
                }

                // 🔥 如果实际Provider与期望不同，发送Intent同步状态
                if (actualApiProvider != provider) {
                    Timber.w("🔄 Provider状态不同步，发送同步Intent: 期望=$provider, 实际=$actualApiProvider")
                    sendIntent(AiCoachContract.Intent.SwitchApiProvider(actualApiProvider))
                }

                Timber.d("✅ API提供商切换成功: $provider → ${currentProvider.name}")
            } catch (e: Exception) {
                Timber.e(e, "❌ API提供商切换失败: $provider")

                // 🔥 优化：提供更详细和用户友好的错误信息
                val userFriendlyMessage = when {
                    e.message?.contains("Provider切换失败") == true -> "无法切换到 ${provider.getDisplayName()}，请检查配置"
                    e.message?.contains("未找到AI提供商") == true -> "${provider.getDisplayName()} 服务暂不可用"
                    else -> "模型切换失败，请稍后重试"
                }

                sendIntent(
                    AiCoachContract.Intent.ShowError(
                        error = userFriendlyMessage,
                    ),
                )
            }
        }
    }

    /**
     * 处理Prompt模式切换
     */
    private fun handleSwitchPromptMode(mode: String) {
        try {
            Timber.d("🔄 开始切换Prompt模式: $mode")
            // Prompt切换逻辑已在 AiCoachViewModel.handleSwitchPromptMode 中实现
            // 这里只需要记录日志
            Timber.d("✅ Prompt模式切换处理完成: $mode")
        } catch (e: Exception) {
            Timber.e(e, "❌ Prompt模式切换失败: $mode")
            sendIntent(
                AiCoachContract.Intent.ShowError(
                    error = "切换Prompt模式失败: ${e.message}",
                ),
            )
        }
    }

    /**
     * 处理Function Call消息构建
     *
     * 🔥 【架构重构】将硬编码字符串逻辑从Reducer移到这里
     * 使用BuildFunctionCallMessageUseCase支持国际化
     */
    private fun handleBuildFunctionCallMessage(functionCallName: String) {
        try {
            Timber.d("🔧 构建Function Call消息: $functionCallName")

            // 使用UseCase构建国际化的Function Call消息
            val uiText = buildFunctionCallMessageUseCase(functionCallName)

            // 发送Intent更新输入框内容
            sendIntent(
                AiCoachContract.Intent.UpdateInput(
                    text = uiText.toString(), // 临时使用toString，实际应该在UI层解析UiText
                ),
            )

            Timber.d("✅ Function Call消息构建完成: $functionCallName")
        } catch (e: Exception) {
            Timber.e(e, "❌ Function Call消息构建失败: $functionCallName")
            sendIntent(
                AiCoachContract.Intent.ShowError(
                    error = "构建Function Call消息失败: ${e.message}",
                ),
            )
        }
    }

    /**
     * 🔥 处理导航到Profile页面
     *
     * 实现用户资料页面的导航逻辑，支持深度链接和错误处理
     * 符合项目的模块化架构原则
     */
    private fun handleNavigateToProfile() {
        handlerScope.launch(kotlinx.coroutines.Dispatchers.Main.immediate) {
            try {
                Timber.d("🔥 [门槛2] 开始导航到Profile页面")

                // 使用CrossModuleRoutes中定义的Profile导航图路由
                val profileDeepLink = CrossModuleRoutes.PROFILE_GRAPH

                Timber.d("🔗 Profile深度链接: $profileDeepLink")

                // 验证深度链接格式
                if (profileDeepLink.isBlank()) {
                    Timber.w("⚠️ Profile深度链接为空")
                    handleNavigationFailure("Profile页面链接配置错误")
                    return@launch
                }

                // 使用深度链接导航
                val result = kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.IO) {
                    deepLinkManager.handleDeepLink(profileDeepLink)
                }

                // 处理导航结果
                when (result) {
                    is com.example.gymbro.core.error.types.ModernResult.Success -> {
                        if (result.data.success) {
                            Timber.d("✅ [门槛2] Profile页面导航成功")
                            Timber.d("🎯 导航目标: ${result.data.targetRoute}")

                            // 可选：发送成功Intent用于UI反馈
                            sendIntent(
                                AiCoachContract.Intent.NavigationCompleted(
                                    destination = "Profile",
                                    success = true,
                                ),
                            )
                        } else {
                            Timber.w("⚠️ [门槛2] Profile页面导航失败: ${result.data.message}")
                            handleNavigationFailure("Profile页面导航失败: ${result.data.message}")
                        }
                    }
                    is com.example.gymbro.core.error.types.ModernResult.Error -> {
                        val errorMessage = result.error.uiMessage?.asString(resourceProvider) ?: "未知错误"
                        Timber.e("💥 [门槛2] Profile页面导航异常: $errorMessage")
                        handleNavigationFailure("Profile页面导航异常: $errorMessage")
                    }
                    is com.example.gymbro.core.error.types.ModernResult.Loading -> {
                        Timber.d("⏳ [门槛2] Profile页面导航处理中...")
                        // Loading状态通常不应该在这里出现
                        Timber.w("⚠️ Profile页面导航返回Loading状态，可能存在超时问题")
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "💥 [门槛2] Profile页面导航处理异常")
                handleNavigationFailure("Profile页面导航处理异常: ${e.message}")
            }
        }
    }

    /**
     * 🔥 处理History RAG上下文获取
     *
     * 从历史会话中获取相关上下文信息，用于增强AI回复的准确性
     * 支持会话级别的上下文检索和错误处理
     */
    private fun handleFetchHistoryContext(effect: AiCoachContract.Effect.FetchHistoryContext) {
        handlerScope.launch {
            try {
                Timber.d("🔍 开始获取History RAG上下文: sessionId=${effect.sessionId}")

                // 验证sessionId
                if (effect.sessionId.isNullOrBlank()) {
                    Timber.w("⚠️ sessionId为空，无法获取History上下文")
                    sendIntent(
                        AiCoachContract.Intent.HistoryContextFetched(
                            sessionId = effect.sessionId ?: "",
                            context = emptyList(),
                            success = false,
                            error = "会话ID为空",
                        ),
                    )
                    return@launch
                }

                // 使用记忆系统获取历史上下文
                // 这里我们复用现有的记忆召回功能来获取历史上下文
                val result = recallMemoriesUseCase(
                    RecallMemoriesUseCase.Params(
                        userId = "current_user", // 实际应该从当前用户状态获取
                        query = "session:${effect.sessionId ?: ""}", // 使用会话ID作为查询条件
                        tokenBudget = 2000, // 默认2000 tokens
                    ),
                )

                when (result) {
                    is ModernResult.Success -> {
                        val contextItems = result.data.map { searchResult ->
                            // 将MemorySearchResult转换为上下文项
                            AiCoachContract.HistoryContextItem(
                                id = searchResult.memory.id,
                                content = searchResult.memory.payload.toString(),
                                relevanceScore = searchResult.similarity,
                                timestamp = kotlinx.datetime.Instant.fromEpochMilliseconds(
                                    searchResult.memory.createdAt,
                                ),
                                messageType = "history", // 标记为历史消息
                            )
                        }

                        Timber.d("✅ History RAG上下文获取成功: ${contextItems.size}条记录")

                        sendIntent(
                            AiCoachContract.Intent.HistoryContextFetched(
                                sessionId = effect.sessionId ?: "",
                                context = contextItems,
                                success = true,
                                error = null,
                            ),
                        )
                    }
                    is ModernResult.Error -> {
                        val errorMessage = when (result.error) {
                            is BusinessErrors.BusinessError -> result.error.message
                            else -> "History上下文获取失败"
                        }

                        Timber.e("❌ History RAG上下文获取失败: $errorMessage")

                        sendIntent(
                            AiCoachContract.Intent.HistoryContextFetched(
                                sessionId = effect.sessionId ?: "",
                                context = emptyList(),
                                success = false,
                                error = errorMessage,
                            ),
                        )
                    }
                    is ModernResult.Loading -> {
                        Timber.w("⚠️ History RAG上下文获取返回Loading状态，这不应该发生")
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "💥 History RAG上下文获取异常: sessionId=${effect.sessionId}")

                sendIntent(
                    AiCoachContract.Intent.HistoryContextFetched(
                        sessionId = effect.sessionId ?: "",
                        context = emptyList(),
                        success = false,
                        error = "上下文获取异常: ${e.message}",
                    ),
                )
            }
        }
    }

    /**
     * 🔥 处理History RAG上下文清理
     *
     * 清理当前会话的RAG上下文缓存，释放内存并重置上下文状态
     * 支持优雅的错误处理和状态通知
     */
    private fun handleClearHistoryRagContext() {
        handlerScope.launch {
            try {
                Timber.d("🧹 开始清理History RAG上下文")

                // 这里可以实现具体的上下文清理逻辑
                // 例如：清理内存缓存、重置上下文状态等

                // 模拟清理过程
                kotlinx.coroutines.delay(100) // 短暂延迟模拟清理过程

                Timber.d("✅ History RAG上下文清理完成")

                // 发送清理完成的Intent
                sendIntent(
                    AiCoachContract.Intent.HistoryContextCleared(
                        success = true,
                        message = "上下文已清理",
                    ),
                )
            } catch (e: Exception) {
                Timber.e(e, "💥 History RAG上下文清理异常")

                sendIntent(
                    AiCoachContract.Intent.HistoryContextCleared(
                        success = false,
                        message = "上下文清理失败: ${e.message}",
                    ),
                )
            }
        }
    }

    // === 🔥 Coach-ThinkingBox重构：新增方法实现 ===

    /**
     * 设置ThinkingBoxDisplay接口（运行时注入）
     */
    fun setThinkingBoxDisplay(display: com.example.gymbro.features.thinkingbox.api.ThinkingBoxDisplay) {
        this.thinkingBoxDisplay = display
        Timber.d("🔗 [Coach-ThinkingBox重构] ThinkingBoxDisplay已注入")
    }

    /**
     * 处理BuildAndSendPrompt Effect
     * 使用LayeredPromptBuilder组装完整Prompt并通过Core-Network发送
     */
    private fun handleBuildAndSendPrompt(effect: AiCoachContract.Effect.BuildAndSendPrompt) {
        handlerScope.launch {
            try {
                Timber.d("🚀 [BuildAndSendPrompt] 开始处理: messageId=${effect.messageId}")

                // 1. 使用LayeredPromptBuilder组装完整消息
                val chatMessages = layeredPromptBuilder.buildChatMessages(
                    systemLayer = null, // 使用默认系统层
                    userInput = effect.userInput,
                    history = effect.conversationHistory,
                    userId = getCurrentUserId(), // 从用户管理获取
                )

                Timber.d("📝 [BuildAndSendPrompt] LayeredPromptBuilder生成${chatMessages.size}条消息")

                // 2. 通过现有的StreamEffectHandler发送AI请求
                // 复用现有的StartAiStream逻辑
                val startAiStreamEffect = AiCoachContract.Effect.StartAiStream(
                    prompt = effect.userInput,
                    sessionId = getCurrentSessionId() ?: "",
                    userMessageId = effect.messageId,
                    aiResponseId = generateMessageId(),
                )

                Timber.d("🌊 [BuildAndSendPrompt] 委托给StreamEffectHandler发送AI请求")
                streamHandler.handleStartAiStream(startAiStreamEffect)

                // 3. 启动ThinkingBox显示（直接处理Effect）
                handleLaunchThinkingBoxDisplay(
                    AiCoachContract.Effect.LaunchThinkingBoxDisplay(effect.messageId),
                )

                Timber.d("✅ [BuildAndSendPrompt] 处理完成: messageId=${effect.messageId}")
            } catch (e: Exception) {
                Timber.e(e, "❌ [BuildAndSendPrompt] 处理失败: messageId=${effect.messageId}")
                sendIntent(
                    AiCoachContract.Intent.ThinkingBoxFailed(
                        messageId = effect.messageId,
                        error = e,
                        partialResult = null,
                    ),
                )
            }
        }
    }

    /**
     * 处理LaunchThinkingBoxDisplay Effect
     * 启动ThinkingBox显示AI响应
     */
    private fun handleLaunchThinkingBoxDisplay(effect: AiCoachContract.Effect.LaunchThinkingBoxDisplay) {
        handlerScope.launch {
            try {
                Timber.d("🎯 [LaunchThinkingBoxDisplay] 启动ThinkingBox: messageId=${effect.messageId}")

                val display = thinkingBoxDisplay
                if (display == null) {
                    Timber.w("⚠️ [LaunchThinkingBoxDisplay] ThinkingBoxDisplay未注入，跳过启动")
                    return@launch
                }

                // 创建完成回调监听器
                val completionListener = object : com.example.gymbro.features.thinkingbox.api.ThinkingBoxCompletionListener {
                    override fun onDisplayComplete(
                        messageId: String,
                        thinkingProcess: String,
                        finalContent: String,
                        metadata: Map<String, Any>,
                    ) {
                        Timber.d("✅ [ThinkingBoxCompletion] 显示完成: messageId=$messageId")
                        sendIntent(
                            AiCoachContract.Intent.ThinkingBoxCompleted(
                                messageId = messageId,
                                thinkingProcess = thinkingProcess,
                                finalContent = finalContent,
                                metadata = metadata,
                            ),
                        )
                    }

                    override fun onDisplayError(
                        messageId: String,
                        error: Throwable,
                        partialResult: String?,
                    ) {
                        Timber.e(error, "❌ [ThinkingBoxCompletion] 显示失败: messageId=$messageId")
                        sendIntent(
                            AiCoachContract.Intent.ThinkingBoxFailed(
                                messageId = messageId,
                                error = error,
                                partialResult = partialResult,
                            ),
                        )
                    }
                }

                // 启动ThinkingBox显示
                display.startDisplaying(effect.messageId, completionListener)

                Timber.d("🚀 [LaunchThinkingBoxDisplay] ThinkingBox已启动: messageId=${effect.messageId}")
            } catch (e: Exception) {
                Timber.e(e, "❌ [LaunchThinkingBoxDisplay] 启动失败: messageId=${effect.messageId}")
                sendIntent(
                    AiCoachContract.Intent.ThinkingBoxFailed(
                        messageId = effect.messageId,
                        error = e,
                        partialResult = null,
                    ),
                )
            }
        }
    }

    /**
     * 获取当前用户ID（临时实现）
     */
    private fun getCurrentUserId(): String {
        // TODO: 从用户管理服务获取真实用户ID
        return "current_user"
    }

    /**
     * 获取当前会话ID（临时实现）
     */
    private fun getCurrentSessionId(): String? {
        // TODO: 从状态提供器获取当前会话ID
        return "current_session"
    }

    /**
     * 生成唯一的消息ID
     */
    private fun generateMessageId(): String {
        return com.example.gymbro.core.util.Constants.MessageId.generate()
    }
}
