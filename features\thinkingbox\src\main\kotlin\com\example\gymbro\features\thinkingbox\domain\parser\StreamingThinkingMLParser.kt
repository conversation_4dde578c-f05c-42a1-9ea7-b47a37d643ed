package com.example.gymbro.features.thinkingbox.domain.parser

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.logging.RawTokenRecorder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * StreamingThinkingMLParser - 纯 XML 语法解析器 v3.0
 *
 * 🎯 核心职责：
 * - XML Token 流解析：将原始 token 转换为结构化的 XML 事件
 * - 状态机管理：维护解析器状态（PRE_THINK → THINKING → POST_FINAL）
 * - 基础事件生成：只生成 TagOpened、TagClosed、TextChunk、StreamFinished 事件
 *
 * 🔥 架构原则：
 * - 纯解析器：不处理业务逻辑，不维护 phase 状态
 * - 无副作用：仅做语法解析，语义映射由 DomainMapper 负责
 * - 状态管理：维护最小必要的解析状态
 * - 错误处理：优雅处理恶意 XML 和解析错误
 *
 * 🔧 重构改进：
 * - 移除所有业务逻辑处理
 * - 简化状态机，仅用于解析上下文
 * - 优化性能，减少不必要的字符串操作
 * - 增强错误处理和调试支持
 */
@Singleton
class StreamingThinkingMLParser @Inject constructor(
    private val xmlScanner: XmlStreamScanner,
) {

    // 🔥 【729方案9优化】移除token数据收集，减少内存开销
    private var tbRawTokenCount = 0L

    /**
     * 解析器状态枚举
     * 用于追踪当前解析上下文，不涉及业务逻辑
     */
    enum class ParserState {
        PRE_THINK, // 解析前：等待 <think> 或 <thinking> 标签
        THINKING, // 思考中：处理 <thinking> 块内容
        IN_FINAL, // 最终答案中：处理 <final> 块内容
        POST_FINAL, // 思考后：处理完成后状态
    }

    /**
     * 解析器上下文
     * 维护解析过程中的最小必要状态
     */
    data class ParserContext(
        var state: ParserState = ParserState.PRE_THINK,
        var tokenCount: Long = 0L, // 处理的 token 计数（用于调试）
    )

    /**
     * 🔥 主要解析接口 - 处理 token 流
     *
     * 这是架构中的核心方法，由 ViewModel 调用
     * 负责将原始 token 流转换为语义事件流
     */
    suspend fun parseTokenStream(
        messageId: String,
        tokens: kotlinx.coroutines.flow.Flow<String>,
        onEvent: suspend (SemanticEvent) -> Unit,
    ) {
        val context = ParserContext()

        try {
            Timber.tag("TB-CORE").i("🚀 [解析启动] 开始解析token流: messageId=$messageId")

            // 🔥 【729方案9优化】使用collectLatest + Dispatchers.Main.immediate，消除线程切换等待
            tokens.flowOn(Dispatchers.Main.immediate).collectLatest { tokenChunk ->
                context.tokenCount++

                // 🔥 【日志优化】只在重要里程碑记录，避免逐token输出
                if (context.tokenCount == 1L) {
                    Timber.tag("TB-CORE").i("🚀 [解析首Token] 开始处理token流: messageId=$messageId")
                } else if (context.tokenCount % 100 == 0L) {
                    Timber.tag("TB-CORE").d("📊 [解析进度] 已处理${context.tokenCount}个token")
                }

                // 🔥 【729方案9优化】仅在DEBUG模式下收集RAW token数据
                collectThinkingBoxRawTokenData(messageId, tokenChunk)

                // 记录原始 token
                if (RawTokenRecorder.isActive()) {
                    RawTokenRecorder.recordToken(tokenChunk, "StreamingThinkingMLParser")
                }

                // 🔥 【日志优化】只记录包含关键标签的chunk，避免普通文本日志
                if (containsKeyTags(tokenChunk)) {
                    Timber.tag("TB-XML").d("🏷️ [关键标签] 检测到XML标签: ${tokenChunk.take(50)}...")
                }

                // 解析 token 块
                processTokenChunk(tokenChunk, context, messageId) { event ->
                    onEvent(event)
                }

                // 🔥 【729方案9优化】每处理10个token调用一次flush，确保流式输出不截断
                if (context.tokenCount % 10 == 0L) {
                    flush()
                }
            }

            // 流结束时发送完成事件
            Timber.tag("TB-CORE").i("✅ [解析完成] Token流结束，总计处理${context.tokenCount}个token")
            onEvent(SemanticEvent.StreamFinished())
        } catch (e: Exception) {
            Timber.tag("TB-ERROR").e(e, "❌ [解析错误] 流解析失败: messageId=$messageId")
            onEvent(
                SemanticEvent.ParseErrorEvent(
                    com.example.gymbro.features.thinkingbox.domain.model.ParseError(
                        type = com.example.gymbro.features.thinkingbox.domain.model.ErrorType.PARSING_ERROR,
                        message = e.message ?: "Unknown parsing error",
                    ),
                ),
            )
        }
    }

    /**
     * 🔥 【729方案9强化】刷新方法，ViewModel每30ms调用一次
     * 确保流式输出不截断，处理缓冲区中的未完成内容
     */
    fun flush() {
        try {
            // 检查XML扫描器是否有未完成的标签
            if (xmlScanner.hasIncompleteTag()) {
                Timber.tag("TB-BUFFER").d("🔄 [Flush] 检测到未完成标签，等待更多数据")
                return
            }

            // 如果缓冲区接近满载，记录警告
            if (xmlScanner.isBufferNearFull()) {
                Timber.tag("TB-WARN").w("⚠️ [Flush] XML扫描器缓冲区接近满载")
            }

            // 处理缓冲区中的剩余内容
            val bufferContent = xmlScanner.getBufferContent()
            if (bufferContent.isNotEmpty()) {
                Timber.tag("TB-BUFFER").d("🔄 [Flush] 处理缓冲区剩余内容: ${bufferContent.length}字符")
            }
        } catch (e: Exception) {
            Timber.tag("TB-ERROR").e(e, "❌ [Flush] 刷新操作失败")
        }
    }

    /**
     * 单 token 块解析方法（向后兼容）
     */
    suspend fun parseTokenChunk(
        tokenChunk: String,
        messageId: String,
        onEvent: suspend (SemanticEvent) -> Unit,
    ) {
        val context = ParserContext()

        try {
            processTokenChunk(tokenChunk, context, messageId, onEvent)
        } catch (e: Exception) {
            Timber.tag("TB-ERROR").e(e, "❌ [解析错误] Token 块解析失败")
            onEvent(
                SemanticEvent.ParseErrorEvent(
                    com.example.gymbro.features.thinkingbox.domain.model.ParseError(
                        type = com.example.gymbro.features.thinkingbox.domain.model.ErrorType.PARSING_ERROR,
                        message = e.message ?: "Unknown parsing error",
                    ),
                ),
            )
        }
    }

    /**
     * 核心解析逻辑 - 处理单个 token 块
     */
    private suspend fun processTokenChunk(
        tokenChunk: String,
        context: ParserContext,
        messageId: String,
        onEvent: suspend (SemanticEvent) -> Unit,
    ) {
        // 使用 XmlStreamScanner 扫描 token
        val xmlTokens = xmlScanner.feed(tokenChunk)

        xmlTokens.forEach { xmlToken ->
            // 记录 XML token 事件
            if (RawTokenRecorder.isActive()) {
                RawTokenRecorder.recordEvent("XMLToken", xmlToken.toString(), "TB-XML-SCANNER")
            }

            // 处理单个 XML token
            val semanticEvents = processXmlToken(xmlToken, context, messageId)

            // 发送所有生成的语义事件
            semanticEvents.forEach { semanticEvent ->
                if (RawTokenRecorder.isActive()) {
                    RawTokenRecorder.recordEvent("SemanticEvent", semanticEvent.toString(), "processXmlToken")
                }
                onEvent(semanticEvent)
            }
        }
    }

    /**
     * 处理单个 XML Token
     * 纯语法解析，不涉及业务逻辑
     */
    private fun processXmlToken(
        token: XmlStreamScanner.Token,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        return when (token) {
            is XmlStreamScanner.TagOpen -> {
                handleTagOpen(token, context, messageId)
            }

            is XmlStreamScanner.TagClose -> {
                handleTagClose(token, context, messageId)
            }

            is XmlStreamScanner.Text -> {
                handleTextContent(token, context, messageId)
            }
        }
    }

    /**
     * 处理标签开启
     * 更新解析状态并生成语义事件
     */
    private fun handleTagOpen(
        token: XmlStreamScanner.TagOpen,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        val tagName = token.name.lowercase()

        Timber.tag("TB-XML").v("🏷️ [标签开启] <$tagName> in state: ${context.state}")

        // 状态转换逻辑（纯解析状态，不涉及业务）
        val events = mutableListOf<SemanticEvent>()

        when (tagName) {
            "thinking" -> {
                if (context.state == ParserState.PRE_THINK) {
                    context.state = ParserState.THINKING
                    Timber.tag("TB-STATE").d("🔄 [状态转换] PRE_THINK → THINKING")
                }
            }
            "final" -> {
                // 🔥 【新增】处理<final>标签开始
                when (context.state) {
                    ParserState.THINKING, ParserState.POST_FINAL -> {
                        context.state = ParserState.IN_FINAL
                        Timber.tag("TB-STATE").d("🔄 [状态转换] ${context.state} → IN_FINAL")
                        // 生成FinalStart事件
                        events.add(SemanticEvent.FinalStart)
                        Timber.tag("TB-FINAL").i("🎯 [最终答案开始] 生成FinalStart事件")
                    }
                    else -> {
                        Timber.tag("TB-WARN").w("⚠️ [状态警告] 在状态${context.state}下遇到<final>标签")
                    }
                }
            }
        }

        // 生成标签开启事件
        events.add(SemanticEvent.TagOpened(token.name, token.attributes))
        return events
    }

    /**
     * 处理标签关闭
     * 更新解析状态并生成语义事件
     */
    private fun handleTagClose(
        token: XmlStreamScanner.TagClose,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        val tagName = token.name.lowercase()
        val events = mutableListOf<SemanticEvent>()

        // 🔥 【日志优化】只在重要状态转换时记录，避免重复输出
        when (tagName) {
            "thinking" -> {
                if (context.state == ParserState.THINKING) {
                    context.state = ParserState.POST_FINAL
                    Timber.tag("TB-STATE").d("🔄 [状态转换] THINKING → POST_FINAL")
                }
            }
            "final" -> {
                // 🔥 【新增】处理</final>标签结束
                if (context.state == ParserState.IN_FINAL) {
                    context.state = ParserState.POST_FINAL
                    Timber.tag("TB-STATE").d("🔄 [状态转换] IN_FINAL → POST_FINAL")
                    // 生成FinalEnd事件
                    events.add(SemanticEvent.FinalEnd)
                    Timber.tag("TB-FINAL").i("🎯 [最终答案结束] 生成FinalEnd事件")
                } else {
                    Timber.tag("TB-WARN").w("⚠️ [状态警告] 在状态${context.state}下遇到</final>标签")
                }
            }
        }

        // 生成标签关闭事件
        events.add(SemanticEvent.TagClosed(token.name))
        return events
    }

    /**
     * 处理文本内容
     * 🔥 【729方案9优化】遇到纯文本立即onTextChunk()，不再等待\n
     */
    private fun handleTextContent(
        token: XmlStreamScanner.Text,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        // 过滤空白内容
        if (token.content.isBlank()) {
            return emptyList()
        }

        // 🔥 【日志优化】移除频繁的文本内容日志，避免逐chunk输出

        // 🔥 【729方案9优化】立即生成文本块事件，不等待换行符
        return listOf(SemanticEvent.TextChunk(token.content))
    }

    /**
     * 检查 token 是否包含关键标签
     * 用于优化日志记录
     */
    private fun containsKeyTags(tokenChunk: String): Boolean {
        val keyTags =
            listOf(
                "<think>",
                "</think>",
                "<thinking>",
                "</thinking>",
                "<phase",
                "</phase>",
                "<final>",
                "</final>",
            )
        return keyTags.any { tokenChunk.contains(it, ignoreCase = true) }
    }

    /**
     * 完成解析并生成流结束事件（向后兼容）
     */
    fun finishParsing(
        messageId: String,
        onEvent: (SemanticEvent) -> Unit,
    ) {
        Timber.tag("TB-CORE").d("✅ [手动结束] 手动触发流结束事件")
        onEvent(SemanticEvent.StreamFinished())
    }

    /**
     * 🔥 【729方案9优化】简化的RAW Token采集（移除以减少性能开销）
     */
    private fun collectThinkingBoxRawTokenData(messageId: String, token: String) {
        // 🔥 【729方案9优化】移除token数据收集，减少性能开销
        tbRawTokenCount++

        // 仅记录关键XML标签用于调试
        if (token.contains("<thinking") || token.contains("</thinking") ||
            token.contains("<phase") || token.contains("<final")
        ) {
            Timber.tag("TB-XML").d("🔍 [关键XML Token] messageId=$messageId, token='$token'")
        }
    }
}
