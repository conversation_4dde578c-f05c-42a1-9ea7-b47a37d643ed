package com.example.gymbro.features.workout.template.edit.internal.components

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import com.example.gymbro.features.workout.shared.components.drag.DragAnimations
import com.example.gymbro.features.workout.shared.components.drag.DragConfig
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import timber.log.Timber

/**
 * Material3拖拽动画集成组件
 *
 * 提供TemplateEdit模块专用的Material3拖拽动画和触觉反馈集成。
 * 基于新的UnifiedDragHandler和DragAnimations系统实现。
 *
 * 功能特性：
 * - Material3标准动画时长和缓动
 * - 智能触觉反馈（拖拽开始、移动、完成）
 * - 与TemplateEditContract.State完美集成
 * - 性能优化的动画实现
 * - 可配置的动画参数
 */
object DragAnimationIntegration {

    /**
     * 拖拽动画修饰符 - TemplateEdit专用版本
     *
     * 将新的DragAnimations系统应用到TemplateEdit的拖拽组件上
     */
    @Composable
    fun Modifier.templateDragAnimated(
        state: TemplateEditContract.State,
        itemId: String,
        config: DragConfig = state.dragConfig
    ): Modifier {
        val dragState = state.createDragState()
        val isCurrentlyDragged = dragState.isDragging(itemId)

        return if (isCurrentlyDragged) {
            this.then(
                DragAnimations.dragAnimated(
                    modifier = Modifier,
                    dragState = dragState,
                    config = config
                )
            )
        } else {
            this
        }
    }

    /**
     * 拖拽触觉反馈处理
     *
     * 根据拖拽状态自动触发相应的触觉反馈
     */
    @Composable
    fun DragHapticFeedbackHandler(
        state: TemplateEditContract.State,
        onTriggerHaptic: (HapticFeedbackType) -> Unit = {}
    ) {
        val haptic = LocalHapticFeedback.current

        // 拖拽开始触觉反馈
        LaunchedEffect(state.isDragInProgress) {
            if (state.isDragInProgress && state.dragConfig.hapticOnStart) {
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                onTriggerHaptic(HapticFeedbackType.LongPress)
                Timber.d("DragAnimation: 触发拖拽开始触觉反馈")
            }
        }

        // 拖拽位置变化触觉反馈
        LaunchedEffect(state.dragTargetIndex) {
            if (state.isDragInProgress &&
                state.shouldTriggerHaptic &&
                state.dragConfig.hapticOnHover &&
                state.dragTargetIndex != state.draggedItemIndex) {
                haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                onTriggerHaptic(HapticFeedbackType.TextHandleMove)
                Timber.d("DragAnimation: 触发拖拽悬停触觉反馈 targetIndex=${state.dragTargetIndex}")
            }
        }

        // 拖拽完成触觉反馈
        LaunchedEffect(state.isDragInProgress) {
            if (!state.isDragInProgress && state.dragConfig.hapticOnDrop) {
                // 只有当拖拽刚刚结束时才触发
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                onTriggerHaptic(HapticFeedbackType.LongPress)
                Timber.d("DragAnimation: 触发拖拽完成触觉反馈")
            }
        }
    }

    /**
     * 拖拽预览动画效果
     *
     * 为拖拽预览提供Material3标准的出现/消失动画
     */
    @Composable
    fun DragPreviewAnimation(
        state: TemplateEditContract.State,
        config: DragConfig = state.dragConfig,
        content: @Composable () -> Unit
    ) {
        if (state.showDropPreview && state.dropPreviewIndex >= 0) {
            // 使用Material3标准的预览动画
            content()
        }
    }

    /**
     * 完整的拖拽动画集成组件
     *
     * 包含所有拖拽相关的动画和交互效果
     */
    @Composable
    fun CompleteDragAnimationIntegration(
        state: TemplateEditContract.State,
        onTriggerHaptic: (HapticFeedbackType) -> Unit = {},
        content: @Composable () -> Unit
    ) {
        // 触觉反馈处理
        DragHapticFeedbackHandler(
            state = state,
            onTriggerHaptic = onTriggerHaptic
        )

        // 内容渲染
        content()

        // 拖拽预览
        DragPreviewAnimation(
            state = state
        ) {
            // 预览内容可以在这里自定义
        }
    }
}

/**
 * TemplateEditContract.State的扩展函数
 * 为拖拽动画提供便利方法
 */
fun TemplateEditContract.State.getDragAnimationInfo(itemId: String): DragAnimationInfo {
    return DragAnimationInfo(
        isDragging = isDragInProgress && draggedExerciseId == itemId,
        isDropTarget = isDragInProgress && dragTargetIndex >= 0,
        shouldAnimate = isDragInProgress,
        dragProgress = if (isDragInProgress) 1f else 0f,
        config = dragConfig
    )
}

/**
 * 拖拽动画信息数据类
 */
data class DragAnimationInfo(
    val isDragging: Boolean,
    val isDropTarget: Boolean,
    val shouldAnimate: Boolean,
    val dragProgress: Float,
    val config: DragConfig
)

/**
 * 使用示例和集成指南
 *
 * 在TemplateEdit的Composable中使用：
 *
 * ```kotlin
 * @Composable
 * fun TemplateExerciseItem(
 *     state: TemplateEditContract.State,
 *     exercise: TemplateExerciseDto,
 *     onIntent: (TemplateEditContract.Intent) -> Unit
 * ) {
 *     with(DragAnimationIntegration) {
 *         CompleteDragAnimationIntegration(
 *             state = state,
 *             onTriggerHaptic = { hapticType ->
 *                 onIntent(TemplateEditContract.Intent.TriggerDragHaptic(hapticType))
 *             }
 *         ) {
 *             Card(
 *                 modifier = Modifier
 *                     .templateDragAnimated(state, exercise.id)
 *                     .fillMaxSize()
 *             ) {
 *                 // 运动项目内容
 *             }
 *         }
 *     }
 * }
 * ```
 */
