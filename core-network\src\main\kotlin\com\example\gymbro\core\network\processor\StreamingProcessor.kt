package com.example.gymbro.core.network.processor

import com.example.gymbro.core.network.detector.ContentType

/**
 * 🔄 流式处理器接口
 *
 * 单一处理器，根据内容类型进行即时处理
 * 设计目标：
 * - 即时处理，零缓冲延迟
 * - 根据内容类型选择最优处理策略
 * - 直接输出给ThinkingBox，无中间转换
 * - 处理时间<2ms per token
 */
interface StreamingProcessor {

    /**
     * 即时处理Token
     *
     * @param token 要处理的token
     * @param contentType 内容类型
     * @param messageId 消息ID（单条AI响应标识）
     * @return 处理后的内容，空字符串表示跳过
     */
    fun processImmediate(
        token: String,
        contentType: ContentType,
        messageId: String,
    ): String
}
