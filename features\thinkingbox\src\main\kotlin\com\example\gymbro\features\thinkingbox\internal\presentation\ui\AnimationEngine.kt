package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.Box
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextIndent
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.sp
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import com.example.gymbro.designSystem.theme.motion.MotionEasings
import com.example.gymbro.designSystem.theme.tokens.ColorTokens
import com.example.gymbro.designSystem.theme.tokens.MapleMono
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

/**
 * AnimationEngine - 统一的动画引擎
 *
 * 基于施工文档的要求，提供统一的60fps动画效果
 * 包含AnimatedSwap组件和所有ThinkingBox动画
 *
 * 设计原则：
 * - 统一的动画时长和缓动函数
 * - 60fps性能保证
 * - 基于designSystem Motion系统
 * - 支持阶段切换、折叠/展开、Final进入动画
 *
 * @sample com.example.gymbro.features.thinkingbox.samples.AnimationEngineSamples.animatedSwapUsage
 * @sample com.example.gymbro.features.thinkingbox.samples.AnimationEngineSamples.pulseAnimationUsage
 */
object AnimationEngine {

    // 🔥 【动画系统统一化】使用 designSystem tokens，移除所有硬编码值
    object Durations {
        // === 核心动画时长（基于 MotionDurations 标准）===
        val PHASE_TRANSITION = MotionDurations.M // 阶段切换动画 (400ms)
        val EXPAND_COLLAPSE = MotionDurations.Coach.SMOOTH_TRANSITION // 折叠/展开动画 (300ms)
        val FINAL_ENTER = MotionDurations.Coach.BUBBLE_ANIMATION // Final答案进入动画 (600ms)
        val FADE_TRANSITION = MotionDurations.Coach.SMOOTH_TRANSITION // 淡入淡出动画 (300ms)
        val PULSE_CYCLE = MotionDurations.Coach.THINKING_BOX_PULSE // 思考框脉动效果 (2000ms)
        val PREVIEW_FADE = MotionDurations.Coach.SMOOTH_TRANSITION // 预览淡入淡出 (300ms)

        // === 🔥 【729方案9优化】打字机效果速度配置 - 允许立即绘制首段 ===
        const val PERTHINK_CHAR_DELAY = 0L // perthink 瞬显，无延迟
        const val FORMAL_PHASE_CHAR_DELAY = 0L // 🔥 【729方案9优化】正式 phase：立即绘制，无延迟
        const val FINAL_RICH_TEXT_CHAR_DELAY = 33L // 最终富文本渲染：30 字符/秒 = 33ms/字符

        // === 向后兼容性常量 ===
        const val TYPEWRITER_CHAR_DELAY = FORMAL_PHASE_CHAR_DELAY // 默认使用正式 phase 速度
        const val PRETHINK_CHAR_DELAY = PERTHINK_CHAR_DELAY // 预思考使用 perthink 速度

        // === 性能优化常量 ===
        const val FRAME_DELAY = 16L // 60fps = 16ms per frame
        const val MAX_TOKEN_PROCESSING_TIME_MS = 1.0 // 1ms per token for 60fps
        const val MIN_ANIMATION_DURATION = 250L // 最短动画持续时间，确保用户感知
    }

    // 🔥 【缓动函数统一化】使用 MotionEasings 标准缓动函数
    object Easings {
        val STANDARD = MotionEasings.STANDARD // 标准缓动 (FastOutSlowInEasing)
        val MICRO_INTERACTION = MotionEasings.MICRO_INTERACTION // 微交互缓动
        val CONTENT_TRANSITION = MotionEasings.CONTENT_TRANSITION // 内容切换缓动
        val DECORATIVE = MotionEasings.DECORATIVE // 装饰性动画缓动
        val EMPHASIZE = MotionEasings.EMPHASIZE // 强调效果缓动
    }

    /**
     * AnimatedSwap - 统一的内容切换组件
     *
     * 为所有阶段切换、折叠/展开、Final进入提供一致的60fps动画效果
     *
     * @param T 内容的类型参数
     * @param targetState 目标状态，当状态改变时触发动画
     * @param modifier 修饰符
     * @param animationType 动画类型，默认为淡入淡出
     * @param label 动画标签，用于测试和调试
     * @param content 要显示的内容
     *
     * @sample Usage:
     * ```kotlin
     * var currentPhase by remember { mutableStateOf(Phase.THINKING) }
     *
     * AnimationEngine.AnimatedSwap(
     *     targetState = currentPhase,
     *     animationType = AnimationEngine.SwapAnimationType.SLIDE
     * ) { phase ->
     *     PhaseContent(phase)
     * }
     * ```
     */
    @Composable
    fun <T> AnimatedSwap(
        targetState: T,
        modifier: Modifier = Modifier,
        animationType: SwapAnimationType = SwapAnimationType.FADE,
        label: String = "animated_swap",
        content: @Composable (T) -> Unit,
    ) {
        AnimatedContent(
            targetState = targetState,
            modifier = modifier,
            transitionSpec = {
                when (animationType) {
                    SwapAnimationType.FADE -> {
                        fadeIn(
                            animationSpec = tween(
                                durationMillis = Durations.FADE_TRANSITION,
                                easing = Easings.STANDARD,
                            ),
                        ) togetherWith fadeOut(
                            animationSpec = tween(
                                durationMillis = Durations.FADE_TRANSITION,
                                easing = Easings.STANDARD,
                            ),
                        )
                    }
                    SwapAnimationType.SLIDE -> {
                        slideInVertically(
                            animationSpec = tween(
                                durationMillis = Durations.PHASE_TRANSITION,
                                easing = Easings.CONTENT_TRANSITION,
                            ),
                            initialOffsetY = { it / 4 },
                        ) + fadeIn(
                            animationSpec = tween(
                                durationMillis = Durations.PHASE_TRANSITION,
                                easing = Easings.CONTENT_TRANSITION,
                            ),
                        ) togetherWith slideOutVertically(
                            animationSpec = tween(
                                durationMillis = Durations.PHASE_TRANSITION,
                                easing = Easings.CONTENT_TRANSITION,
                            ),
                            targetOffsetY = { -it / 4 },
                        ) + fadeOut(
                            animationSpec = tween(
                                durationMillis = Durations.PHASE_TRANSITION,
                                easing = Easings.CONTENT_TRANSITION,
                            ),
                        )
                    }
                    SwapAnimationType.SCALE -> {
                        scaleIn(
                            animationSpec = tween(
                                durationMillis = Durations.FINAL_ENTER,
                                easing = Easings.EMPHASIZE,
                            ),
                            initialScale = 0.8f,
                        ) + fadeIn(
                            animationSpec = tween(
                                durationMillis = Durations.FINAL_ENTER,
                                easing = Easings.EMPHASIZE,
                            ),
                        ) togetherWith scaleOut(
                            animationSpec = tween(
                                durationMillis = Durations.FINAL_ENTER,
                                easing = Easings.EMPHASIZE,
                            ),
                            targetScale = 1.2f,
                        ) + fadeOut(
                            animationSpec = tween(
                                durationMillis = Durations.FINAL_ENTER,
                                easing = Easings.EMPHASIZE,
                            ),
                        )
                    }
                }
            },
            label = label,
        ) { targetState ->
            content(targetState)
        }
    }

    /**
     * 动画类型枚举
     */
    enum class SwapAnimationType {
        FADE, // 淡入淡出
        SLIDE, // 滑动切换
        SCALE, // 缩放切换
    }

    /**
     * 思考框脉冲动画
     * 用于活跃状态指示，60fps优化
     *
     * @param enabled 是否启用动画
     * @return 动画的alpha值状态，范围从0.85f到1f
     *
     * @sample Usage:
     * ```kotlin
     * val pulseAlpha by AnimationEngine.rememberPulseAnimation(enabled = isThinking)
     *
     * Box(
     *     modifier = Modifier
     *         .graphicsLayer { alpha = pulseAlpha }
     * ) {
     *     // Content
     * }
     * ```
     */
    @Composable
    fun rememberPulseAnimation(enabled: Boolean = true): State<Float> {
        val infiniteTransition = rememberInfiniteTransition(label = "thinking_pulse")
        return infiniteTransition.animateFloat(
            initialValue = if (enabled) 0.98f else 1f, // 🔥 【P1修复】改为scale值，不再使用alpha
            targetValue = if (enabled) 1.02f else 1f, // 🔥 【P1修复】轻微的缩放效果
            animationSpec = if (enabled) {
                infiniteRepeatable(
                    animation = tween(
                        durationMillis = Durations.PULSE_CYCLE,
                        easing = Easings.DECORATIVE,
                    ),
                    repeatMode = RepeatMode.Reverse,
                )
            } else {
                infiniteRepeatable(
                    animation = tween(1), // 最小持续时间，避免除零错误
                    repeatMode = RepeatMode.Reverse,
                )
            },
            label = "pulse_alpha",
        )
    }

    /**
     * 展开/折叠动画修饰符
     * 统一的折叠动画，支持高度和透明度变化
     *
     * @param expanded 是否展开状态
     * @return 应用了动画效果的Modifier
     *
     * @sample Usage:
     * ```kotlin
     * var isExpanded by remember { mutableStateOf(false) }
     *
     * Card(
     *     modifier = Modifier
     *         .expandCollapseAnimation(expanded = isExpanded)
     *         .clickable { isExpanded = !isExpanded }
     * ) {
     *     // Expandable content
     * }
     * ```
     */
    fun Modifier.expandCollapseAnimation(
        expanded: Boolean,
    ): Modifier = composed {
        val scale by animateFloatAsState(
            targetValue = if (expanded) 1f else 0.95f,
            animationSpec = tween(
                durationMillis = Durations.EXPAND_COLLAPSE,
                easing = Easings.CONTENT_TRANSITION,
            ),
            label = "expand_scale",
        )

        val alpha by animateFloatAsState(
            targetValue = if (expanded) 1f else 0.7f,
            animationSpec = tween(
                durationMillis = Durations.EXPAND_COLLAPSE,
                easing = Easings.CONTENT_TRANSITION,
            ),
            label = "expand_alpha",
        )

        graphicsLayer {
            scaleX = scale
            scaleY = scale
            this.alpha = alpha
        }
    }

    /**
     * 脉冲动画修饰符
     * 应用脉冲效果到任何组件
     *
     * @param enabled 是否启用脉冲动画
     * @return 应用了脉冲效果的Modifier
     *
     * @sample Usage:
     * ```kotlin
     * Icon(
     *     imageVector = Icons.Default.Favorite,
     *     contentDescription = null,
     *     modifier = Modifier.pulseAnimation(enabled = isActive)
     * )
     * ```
     */
    fun Modifier.pulseAnimation(
        enabled: Boolean = true,
    ): Modifier = composed {
        if (!enabled) return@composed this

        val pulseScale by rememberPulseAnimation(enabled)

        graphicsLayer {
            // 🔥 【P1修复】不再修改 alpha，改为 scale 避免与 fade alpha 叠加
            scaleX = pulseScale
            scaleY = pulseScale
        }
    }

    /**
     * Final答案进入动画修饰符
     * 平滑的进入动画，用于最终答案显示
     */
    fun Modifier.finalEnterAnimation(
        visible: Boolean,
    ): Modifier = composed {
        val scale by animateFloatAsState(
            targetValue = if (visible) 1f else 0.8f,
            animationSpec = tween(
                durationMillis = Durations.FINAL_ENTER,
                easing = Easings.EMPHASIZE,
            ),
            label = "final_scale",
        )

        val alpha by animateFloatAsState(
            targetValue = if (visible) 1f else 0f,
            animationSpec = tween(
                durationMillis = Durations.FINAL_ENTER,
                easing = Easings.EMPHASIZE,
            ),
            label = "final_alpha",
        )

        graphicsLayer {
            scaleX = scale
            scaleY = scale
            this.alpha = alpha
        }
    }

    /**
     * 阶段切换动画修饰符
     * 用于思考阶段之间的平滑切换
     */
    fun Modifier.phaseTransitionAnimation(
        isActive: Boolean,
    ): Modifier = composed {
        val elevation by animateFloatAsState(
            targetValue = if (isActive) Tokens.Elevation.Medium.value else Tokens.Elevation.Small.value,
            animationSpec = tween(
                durationMillis = Durations.PHASE_TRANSITION,
                easing = Easings.CONTENT_TRANSITION,
            ),
            label = "phase_elevation",
        )

        val scale by animateFloatAsState(
            targetValue = if (isActive) 1.02f else 1f,
            animationSpec = tween(
                durationMillis = Durations.PHASE_TRANSITION,
                easing = Easings.CONTENT_TRANSITION,
            ),
            label = "phase_scale",
        )

        graphicsLayer {
            scaleX = scale
            scaleY = scale
            shadowElevation = elevation
        }
    }

    /**
     * 预览模式淡出动画修饰符
     * 用于ChatGPT风格的预览文本淡出效果
     */
    fun Modifier.previewFadeAnimation(
        visible: Boolean,
    ): Modifier = composed {
        val alpha by animateFloatAsState(
            targetValue = if (visible) 0.7f else 0f, // ChatGPT规范：70%透明度
            animationSpec = tween(
                durationMillis = Durations.PREVIEW_FADE,
                easing = Easings.STANDARD,
            ),
            label = "preview_alpha",
        )

        graphicsLayer {
            this.alpha = alpha
        }
    }

    /**
     * 🔥 【Spring 动画规格统一化】ChatGPT风格的展开/折叠动画规格
     * 基于 Material Design 3 标准，提供流畅的弹性效果
     */
    object SpringSpecs {
        // 标准弹性动画 - 用于展开/折叠
        val EXPAND_COLLAPSE = spring<IntSize>(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow,
        )

        // 微交互弹性动画 - 用于按钮反馈
        val MICRO_INTERACTION = spring<Float>(
            dampingRatio = Spring.DampingRatioNoBouncy,
            stiffness = Spring.StiffnessMedium,
        )

        // 内容过渡弹性动画 - 用于页面切换
        val CONTENT_TRANSITION = spring<Float>(
            dampingRatio = Spring.DampingRatioLowBouncy,
            stiffness = Spring.StiffnessLow,
        )
    }

    // 🔥 【向后兼容】保留旧的 API
    @Deprecated("使用 SpringSpecs.EXPAND_COLLAPSE 替代", ReplaceWith("SpringSpecs.EXPAND_COLLAPSE"))
    val chatGptExpandCollapseSpec = SpringSpecs.EXPAND_COLLAPSE

    /**
     * 🔥 【Phase 切换动画规格】专用的 Phase 切换动画系统
     *
     * 实现温和的渐隐渐显过渡效果：
     * - fadeOut: 200ms (当前 Phase 渐隐)
     * - fadeIn: 300ms (新 Phase 渐显)
     * - 总时长: 500ms 内完成
     *
     * @sample 使用示例：
     * ```kotlin
     * // 在 AIThinkingCard 中使用
     * AnimationEngine.AnimatedPhaseTransition(
     *     currentPhase = phaseToRender,
     *     isTransitioning = isPhaseTransitioning
     * ) { phase ->
     *     ThinkingStageCard(
     *         phase = phase,
     *         onAnimationFinished = { phaseId ->
     *             // Phase 完成，触发切换到下一个 Phase
     *         }
     *     )
     * }
     * ```
     */
    object PhaseTransitionSpecs {
        // Phase 渐隐动画规格 (200ms)
        val FADE_OUT = tween<Float>(
            durationMillis = MotionDurations.Coach.NORMAL_INTERACTION, // 200ms
            easing = Easings.STANDARD,
        )

        // Phase 渐显动画规格 (300ms)
        val FADE_IN = tween<Float>(
            durationMillis = Durations.FADE_TRANSITION, // 300ms
            easing = Easings.STANDARD,
        )

        // Phase 切换延迟 (fadeOut 完成后的间隔)
        const val TRANSITION_DELAY = 50L // 50ms 间隔，确保切换流畅

        // 总切换时长 (用于外部计时)
        val TOTAL_DURATION = MotionDurations.Coach.NORMAL_INTERACTION +
            TRANSITION_DELAY +
            Durations.FADE_TRANSITION // 200 + 50 + 300 = 550ms
    }

    /**
     * 打字机效果动画
     * 用于流式文本显示，优化为16ms/帧性能
     *
     * @param text 要显示的完整文本
     * @param enabled 是否启用打字机效果
     * @param typingSpeed 打字速度（毫秒/字符）
     * @return 当前应该显示的文本状态
     *
     * @sample Usage:
     * ```kotlin
     * val message = "Hello, this is a typing animation!"
     * val displayedText by AnimationEngine.rememberTypingAnimation(
     *     text = message,
     *     enabled = true,
     *     typingSpeed = 50L
     * )
     *
     * Text(text = displayedText)
     * ```
     */
    @Composable
    fun rememberTypingAnimation(
        text: String,
        enabled: Boolean = true,
        typingSpeed: Long = Durations.FRAME_DELAY, // 🔥 P1修复：使用统一的帧延迟常量
    ): State<String> {
        var displayedText by remember(text) { mutableStateOf("") }

        LaunchedEffect(text, enabled) {
            if (!enabled) {
                displayedText = text
                return@LaunchedEffect
            }

            // 性能优化：批量处理字符，减少重组频率
            val batchSize = when {
                text.length > 1000 -> 10 // 长文本使用大批量
                text.length > 200 -> 5 // 中等文本使用中批量
                else -> 1 // 短文本逐字符
            }

            if (text.startsWith(displayedText)) {
                // 增量追加新内容
                val newContent = text.substring(displayedText.length)
                for (i in newContent.indices step batchSize) {
                    val endIndex = minOf(i + batchSize, newContent.length)
                    val batch = newContent.substring(i, endIndex)
                    displayedText += batch
                    kotlinx.coroutines.delay(typingSpeed)
                }
            } else {
                // 重新开始打字
                displayedText = ""
                for (i in text.indices step batchSize) {
                    val endIndex = minOf(i + batchSize, text.length)
                    displayedText = text.substring(0, endIndex)
                    kotlinx.coroutines.delay(typingSpeed)
                }
            }
        }

        return remember { derivedStateOf { displayedText } }
    }

    /**
     * 性能验收测试工具
     * 验证动画性能是否达到60fps标准
     */
    object PerformanceValidator {

        /**
         * 验证Token消费性能：16ms/帧内
         *
         * @param tokenCount 处理的token数量
         * @param processingTimeMs 处理时间（毫秒）
         * @return 是否满足性能要求
         */
        fun validateTokenProcessingPerformance(
            tokenCount: Int,
            processingTimeMs: Long,
        ): Boolean {
            val avgTimePerToken = if (tokenCount > 0) processingTimeMs.toDouble() / tokenCount else 0.0
            return avgTimePerToken <= Durations.MAX_TOKEN_PROCESSING_TIME_MS // 🔥 P1修复：使用统一的性能常量
        }

        /**
         * 验证思考框高度：≤1/3屏幕
         *
         * @param thinkingBoxHeight 思考框高度（像素）
         * @param screenHeight 屏幕高度（像素）
         * @return 是否满足高度限制
         */
        fun validateThinkingBoxHeight(
            thinkingBoxHeight: Float,
            screenHeight: Float,
        ): Boolean {
            return thinkingBoxHeight <= screenHeight * 0.33f
        }

        /**
         * 验证阶段切换节流：≤1s
         */
        fun validatePhaseTransitionThrottling(
            lastTransitionTime: Long,
            currentTime: Long,
        ): Boolean {
            return (currentTime - lastTransitionTime) >= 1000L // 1 second throttling
        }

        /**
         * 验证最终答案渲染：400ms内
         */
        fun validateFinalAnswerRendering(
            renderingTimeMs: Long,
        ): Boolean {
            return renderingTimeMs <= 400L
        }

        /**
         * 综合性能验收
         */
        fun performComprehensiveValidation(): ValidationResult {
            return ValidationResult(
                tokenProcessingPassed = true, // 需要实际测量
                heightConstraintPassed = true, // 需要实际测量
                throttlingPassed = true, // 需要实际测量
                renderingPassed = true, // 需要实际测量
                overallPassed = true,
            )
        }
    }

    /**
     * 验收结果数据类
     *
     * @property tokenProcessingPassed Token处理性能是否通过
     * @property heightConstraintPassed 高度限制是否通过
     * @property throttlingPassed 节流控制是否通过
     * @property renderingPassed 渲染性能是否通过
     * @property overallPassed 整体是否通过
     */
    data class ValidationResult(
        val tokenProcessingPassed: Boolean,
        val heightConstraintPassed: Boolean,
        val throttlingPassed: Boolean,
        val renderingPassed: Boolean,
        val overallPassed: Boolean,
    )

    /**
     * TypewriterText - 简化版打字机文本组件
     *
     * 🔥 【打字机优化】简化实现，消除顿挫感：
     * - 移除复杂的增量追加逻辑，统一渲染状态
     * - perthink 瞬显，正式 phase 逐字显示
     * - 丝滑的文本渲染，无状态不一致问题
     * - 移除所有不必要的动画效果
     *
     * @param fullText 要显示的完整文本
     * @param phaseId 阶段ID（用于日志）
     * @param charDelay 字符延迟（毫秒），0表示瞬显
     * @param modifier 修饰符
     * @param style 文本样式
     * @param onDone 完成回调
     */
    @Composable
    fun TypewriterText(
        fullText: String,
        phaseId: String,
        charDelay: Long = Durations.FORMAL_PHASE_CHAR_DELAY,
        modifier: Modifier = Modifier,
        style: TextStyle = TextStyle(
            fontSize = Tokens.Typography.Small,
            fontWeight = androidx.compose.ui.text.font.FontWeight.Normal,
        ),
        onDone: () -> Unit = {},
    ) {
        // 🔥 【P0修复】增量逻辑，key 去掉 fullText，只保留 phaseId
        var displayedText by remember(phaseId) { mutableStateOf("") }
        var isComplete by remember(phaseId) { mutableStateOf(false) }

        // 🔥 【P0修复】增量追加逻辑，避免前缀重播
        LaunchedEffect(fullText, phaseId, charDelay) {
            if (fullText.isEmpty()) {
                displayedText = ""
                isComplete = true
                onDone()
                return@LaunchedEffect
            }

            if (charDelay == 0L) {
                // 🔥 【瞬显优化】perthink 瞬显
                displayedText = fullText
                isComplete = true
                onDone()
                return@LaunchedEffect
            }

            // 🔥 【P0修复】增量追加：只有前缀失配时才重播
            if (fullText.startsWith(displayedText)) {
                // 增量追加新尾部
                val startIndex = displayedText.length
                val characters = fullText.toCharArray()

                for (i in startIndex until characters.size) {
                    // 检查协程是否被取消
                    if (!isActive) return@LaunchedEffect

                    displayedText = String(characters, 0, i + 1)

                    // 最后一个字符不需要延迟
                    if (i < characters.size - 1) {
                        delay(charDelay)
                    }
                }
            } else {
                // 只有真正出现"前缀失配"才整段重播
                displayedText = ""
                isComplete = false

                val characters = fullText.toCharArray()
                for (i in characters.indices) {
                    // 检查协程是否被取消
                    if (!isActive) return@LaunchedEffect

                    displayedText = String(characters, 0, i + 1)

                    // 最后一个字符不需要延迟
                    if (i < characters.size - 1) {
                        delay(charDelay)
                    }
                }
            }

            isComplete = true
            onDone()
        }

        Text(
            text = displayedText,
            style = style.copy(
                lineHeight = style.lineHeight * 0.9f, // 🔥 【空白修复】进一步减少行高
                textIndent = TextIndent.None, // 🔥 【空白修复】确保无缩进
            ),
            modifier = modifier,
            maxLines = Int.MAX_VALUE, // 🎯 【自适应文本】允许自然换行
            overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis, // 🎯 【文本溢出】显示省略号
        )
    }

    /**
     * PreThinkTypewriterText - perthink 专用打字机文本
     *
     * 🔥 【打字机修复】perthink 使用灰色斜体样式，瞬显，统一底层实现
     */
    @Composable
    fun PreThinkTypewriterText(
        fullText: String,
        phaseId: String,
        modifier: Modifier = Modifier,
        onDone: () -> Unit = {},
    ) {
        TypewriterText(
            fullText = fullText,
            phaseId = phaseId,
            charDelay = Durations.PERTHINK_CHAR_DELAY, // 0L 瞬显
            modifier = modifier,
            style = TextStyle(
                fontFamily = MapleMono, // 🔥 【字体系统修复】使用项目标准字体族
                fontSize = Tokens.Typography.BodyMedium,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Normal,
                fontStyle = FontStyle.Normal, // 🔥 【用户需求修复】不使用斜体样式
                color = ColorTokens.Dark.OnSurfaceVariant,
                lineHeight = Tokens.Typography.BodyMedium * 1.4f * 0.85f, // 🔥 【修复缩进】减少行高，移除多余空白，使用Tokens计算
                textIndent = TextIndent(
                    firstLine = Tokens.Spacing.None.value.sp, // 🔥 【使用Tokens替代硬编码】
                    restLine = Tokens.Spacing.None.value.sp, // 🔥 【使用Tokens替代硬编码】
                ), // 🔥 【修复缩进】移除文本缩进
            ),
            onDone = onDone,
        )
    }

    /**
     * FormalPhaseTypewriterText - 统一打字机文本组件
     *
     * 🔥 【打字机修复】perthink和正式phase都使用相同的打字机效果，只在样式上区分
     * 使用 33ms/字符，30cps 丝滑效果，确保一致的用户体验，统一底层实现
     */
    @Composable
    fun FormalPhaseTypewriterText(
        fullText: String,
        phaseId: String,
        modifier: Modifier = Modifier,
        onDone: () -> Unit = {},
    ) {
        TypewriterText(
            fullText = fullText,
            phaseId = phaseId,
            charDelay = Durations.FORMAL_PHASE_CHAR_DELAY, // 16ms/字符 - 更丝滑的打字机速度
            modifier = modifier,
            style = TextStyle(
                fontFamily = MapleMono, // 🔥 【字体系统修复】使用项目标准字体族
                fontSize = Tokens.Typography.BodyMedium,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Normal,
                color = ColorTokens.Dark.OnSurface,
                lineHeight = Tokens.Typography.BodyMedium * 1.4f * 0.85f, // 🔥 【修复缩进】减少行高，移除多余空白，使用Tokens计算
                textIndent = TextIndent(
                    firstLine = Tokens.Spacing.None.value.sp, // 🔥 【使用Tokens替代硬编码】
                    restLine = Tokens.Spacing.None.value.sp, // 🔥 【使用Tokens替代硬编码】
                ), // 🔥 【修复缩进】移除文本缩进
            ),
            onDone = onDone,
        )
    }

    /**
     * 🔥 【Phase 切换动画组件】AnimatedPhaseTransition
     *
     * 实现 Phase 之间的温和渐隐渐显过渡效果
     *
     * @param currentPhase 当前要显示的 Phase
     * @param isTransitioning 是否正在切换中
     * @param modifier 修饰符
     * @param content Phase 内容渲染函数
     */
    @Composable
    fun <T> AnimatedPhaseTransition(
        currentPhase: T,
        isTransitioning: Boolean = false,
        modifier: Modifier = Modifier,
        content: @Composable (T) -> Unit,
    ) {
        // 🔥 【Phase 切换状态管理】
        var displayPhase by remember { mutableStateOf(currentPhase) }
        var isVisible by remember { mutableStateOf(true) }

        // 🔥 【P1修复】使用稳定的ID比较，避免对象引用变化触发不必要的动画
        val currentPhaseId = when (val phase = currentPhase) {
            is PhaseUi -> phase.id
            else -> phase.hashCode().toString()
        }
        val displayPhaseId = when (val phase = displayPhase) {
            is PhaseUi -> phase.id
            else -> phase.hashCode().toString()
        }

        // 🔥 【切换逻辑】只在 ID 真正变化时触发切换动画
        LaunchedEffect(currentPhaseId) {
            if (displayPhaseId != currentPhaseId) {
                // 开始切换：先渐隐当前 Phase
                isVisible = false
                delay(PhaseTransitionSpecs.FADE_OUT.durationMillis.toLong())

                // 切换到新 Phase
                displayPhase = currentPhase
                delay(PhaseTransitionSpecs.TRANSITION_DELAY)

                // 渐显新 Phase
                isVisible = true
            }
        }

        // 🔥 【渐隐渐显动画】
        val alpha by animateFloatAsState(
            targetValue = if (isVisible) 1f else 0f,
            animationSpec = if (isVisible) {
                PhaseTransitionSpecs.FADE_IN
            } else {
                PhaseTransitionSpecs.FADE_OUT
            },
            label = "phase_transition_alpha",
        )

        Box(
            modifier = modifier.graphicsLayer { this.alpha = alpha },
        ) {
            content(displayPhase)
        }
    }

    /**
     * 🔥 【Phase 切换修饰符】phaseTransitionModifier
     *
     * 为任何组件添加 Phase 切换时的渐隐渐显效果
     *
     * @param isVisible 是否可见
     * @param animationType 动画类型（渐隐或渐显）
     */
    fun Modifier.phaseTransitionModifier(
        isVisible: Boolean,
        animationType: PhaseAnimationType = PhaseAnimationType.FADE_IN,
    ): Modifier = composed {
        val alpha by animateFloatAsState(
            targetValue = if (isVisible) 1f else 0f,
            animationSpec = when (animationType) {
                PhaseAnimationType.FADE_IN -> PhaseTransitionSpecs.FADE_IN
                PhaseAnimationType.FADE_OUT -> PhaseTransitionSpecs.FADE_OUT
            },
            label = "phase_transition_modifier",
        )

        graphicsLayer { this.alpha = alpha }
    }

    /**
     * Phase 动画类型枚举
     */
    enum class PhaseAnimationType {
        FADE_IN, // 渐显动画
        FADE_OUT, // 渐隐动画
    }
}
