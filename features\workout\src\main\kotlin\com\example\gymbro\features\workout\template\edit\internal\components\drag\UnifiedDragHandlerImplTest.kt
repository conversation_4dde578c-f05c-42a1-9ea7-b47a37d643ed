package com.example.gymbro.features.workout.template.edit.internal.components.drag

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class UnifiedDragHandlerImplTest {

    private lateinit var dragHandler: UnifiedDragHandlerImpl<String>

    @BeforeEach
    fun setUp() {
        dragHandler = UnifiedDragHandlerImpl()
        dragHandler.setItems(listOf("A", "B", "C", "D"))
    }

    @Test
    fun `test drag start and state update`() = runTest {
        dragHandler.startDrag("B", 1)
        val state = dragHandler.dragState.value

        assertTrue(state.isDragging)
        assertEquals("B", state.draggedItem)
        assertEquals(1, state.originalPosition)
        assertEquals(1, state.currentPosition)
    }

    @Test
    fun `test drag update position`() = runTest {
        dragHandler.startDrag("B", 1)
        dragHandler.updateDragPosition(3)
        val state = dragHandler.dragState.value

        assertTrue(state.isDragging)
        assertEquals(3, state.currentPosition)
    }

    @Test
    fun `test commit drag successfully`() = runTest {
        dragHandler.startDrag("B", 1)
        dragHandler.updateDragPosition(3)
        val result = dragHandler.commitDrag()

        assertTrue(result.isSuccess)
        assertFalse(dragHandler.isDragging())
        assertEquals(listOf("A", "C", "D", "B"), dragHandler.getItems())
    }

    @Test
    fun `test cancel drag`() = runTest {
        dragHandler.startDrag("B", 1)
        dragHandler.cancelDrag()

        assertFalse(dragHandler.isDragging())
        assertEquals(listOf("A", "B", "C", "D"), dragHandler.getItems())
    }
}
