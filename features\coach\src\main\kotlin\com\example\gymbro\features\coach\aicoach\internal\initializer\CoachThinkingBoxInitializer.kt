package com.example.gymbro.features.coach.aicoach.internal.initializer

import com.example.gymbro.features.coach.aicoach.internal.effect.AiCoachEffectHandler
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxDisplay
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Coach-ThinkingBox集成初始化器
 *
 * 负责在应用启动时将ThinkingBoxDisplay注入到AiCoachEffectHandler中。
 * 解决了跨模块依赖注入的问题。
 *
 * @since Coach-ThinkingBox重构
 */
@Singleton
class CoachThinkingBoxInitializer @Inject constructor(
    private val thinkingBoxDisplay: ThinkingBoxDisplay,
) {

    private var isInitialized = false

    /**
     * 初始化Coach-ThinkingBox集成
     *
     * @param aiCoachEffectHandler AiCoachEffectHandler实例
     */
    internal fun initialize(aiCoachEffectHandler: AiCoachEffectHandler) {
        if (isInitialized) {
            Timber.d("🔄 [CoachThinkingBoxInitializer] 已经初始化，跳过")
            return
        }

        try {
            Timber.d("🚀 [CoachThinkingBoxInitializer] 开始初始化Coach-ThinkingBox集成")

            // 将ThinkingBoxDisplay注入到AiCoachEffectHandler
            aiCoachEffectHandler.setThinkingBoxDisplay(thinkingBoxDisplay)

            isInitialized = true

            Timber.d("✅ [CoachThinkingBoxInitializer] Coach-ThinkingBox集成初始化完成")
        } catch (e: Exception) {
            Timber.e(e, "❌ [CoachThinkingBoxInitializer] 初始化失败")
            throw e
        }
    }

    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized
}
