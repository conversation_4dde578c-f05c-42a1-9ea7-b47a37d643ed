package com.example.gymbro.features.thinkingbox.internal.adapter

import com.example.gymbro.core.network.adapter.ThinkingBoxAdapter
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * ThinkingBoxStreamAdapter 单元测试
 *
 * 🎯 测试目标：
 * - 验证Token流处理的核心功能
 * - 测试暂停/恢复/取消机制
 * - 验证错误处理和资源清理
 * - 确保生命周期管理正确性
 */
class ThinkingBoxStreamAdapterTest {

    private lateinit var adapter: ThinkingBoxStreamAdapter
    private lateinit var mockThinkingBoxAdapter: ThinkingBoxAdapter
    private lateinit var mockStreamingParser: StreamingThinkingMLParser
    private lateinit var mockDomainMapper: DomainMapper

    @Before
    fun setup() {
        mockThinkingBoxAdapter = mockk()
        mockStreamingParser = mockk()
        mockDomainMapper = mockk()

        adapter = ThinkingBoxStreamAdapter(
            thinkingBoxAdapter = mockThinkingBoxAdapter,
            streamingParser = mockStreamingParser,
            domainMapper = mockDomainMapper,
        )
    }

    @Test
    fun `startTokenStreamProcessing should create active streaming job`() = runTest {
        // Given
        val messageId = "test-message-123"
        val tokenFlow = flowOf("test-token")
        val receivedEvents = mutableListOf<ThinkingEvent>()
        var streamCompleted = false
        var errorOccurred: Throwable? = null

        coEvery { mockThinkingBoxAdapter.processWithNewArchitecture(any(), any()) } returns emptyFlow()

        // When
        val streamingJob = adapter.startTokenStreamProcessing(
            tokenFlow = tokenFlow,
            messageId = messageId,
            onTokenReceived = { event -> receivedEvents.add(event) },
            onStreamComplete = { streamCompleted = true },
            onError = { error -> errorOccurred = error },
        )

        // Then
        assertNotNull(streamingJob)
        assertEquals(messageId, streamingJob.messageId)
        assertFalse(streamingJob.isPaused)
        assertFalse(streamingJob.isCompleted)
    }

    @Test
    fun `pauseStreamProcessing should pause active job`() = runTest {
        // Given
        val messageId = "test-message-456"
        coEvery { mockThinkingBoxAdapter.processWithNewArchitecture(any(), any()) } returns emptyFlow()

        val streamingJob = adapter.startTokenStreamProcessing(
            tokenFlow = flowOf("test"),
            messageId = messageId,
            onTokenReceived = { },
            onStreamComplete = { },
            onError = { },
        )

        // When
        val result = adapter.pauseStreamProcessing(messageId)

        // Then
        assertTrue(result)
        assertTrue(streamingJob.isPaused)
    }

    @Test
    fun `resumeStreamProcessing should resume paused job`() = runTest {
        // Given
        val messageId = "test-message-789"
        coEvery { mockThinkingBoxAdapter.processWithNewArchitecture(any(), any()) } returns emptyFlow()

        val streamingJob = adapter.startTokenStreamProcessing(
            tokenFlow = flowOf("test"),
            messageId = messageId,
            onTokenReceived = { },
            onStreamComplete = { },
            onError = { },
        )

        adapter.pauseStreamProcessing(messageId)
        assertTrue(streamingJob.isPaused)

        // When
        val result = adapter.resumeStreamProcessing(messageId)

        // Then
        assertTrue(result)
        assertFalse(streamingJob.isPaused)
    }

    @Test
    fun `cancelStreamProcessing should cancel and remove job`() = runTest {
        // Given
        val messageId = "test-message-cancel"
        coEvery { mockThinkingBoxAdapter.processWithNewArchitecture(any(), any()) } returns emptyFlow()
        // No longer need to mock unsubscribe as it's handled internally

        adapter.startTokenStreamProcessing(
            tokenFlow = flowOf("test"),
            messageId = messageId,
            onTokenReceived = { },
            onStreamComplete = { },
            onError = { },
        )

        // When
        val result = adapter.cancelStreamProcessing(messageId)

        // Then
        assertTrue(result)
        // Verification no longer needed as unsubscribe is handled internally

        // Verify job is removed
        val status = adapter.getStreamProcessingStatus(messageId)
        assertEquals(null, status)
    }

    @Test
    fun `getStreamProcessingStatus should return correct status`() = runTest {
        // Given
        val messageId = "test-message-status"
        coEvery { mockThinkingBoxAdapter.processWithNewArchitecture(any(), any()) } returns emptyFlow()

        adapter.startTokenStreamProcessing(
            tokenFlow = flowOf("test"),
            messageId = messageId,
            onTokenReceived = { },
            onStreamComplete = { },
            onError = { },
        )

        // When
        val status = adapter.getStreamProcessingStatus(messageId)

        // Then
        assertNotNull(status)
        assertEquals(messageId, status.messageId)
        assertTrue(status.isActive)
        assertFalse(status.isPaused)
        assertFalse(status.isCompleted)
    }

    @Test
    fun `cleanupAll should cancel all active jobs`() = runTest {
        // Given
        val messageId1 = "test-message-1"
        val messageId2 = "test-message-2"

        coEvery { mockThinkingBoxAdapter.processWithNewArchitecture(any(), any()) } returns emptyFlow()

        adapter.startTokenStreamProcessing(flowOf("test1"), messageId1, { }, { }, { })
        adapter.startTokenStreamProcessing(flowOf("test2"), messageId2, { }, { }, { })

        assertEquals(2, adapter.getAllActiveStreamProcessingStatus().size)

        // When
        adapter.cleanupAll()

        // Then
        // Cleanup is handled internally, no need to verify specific calls
        assertEquals(0, adapter.getAllActiveStreamProcessingStatus().size)
    }

    @Test
    fun `pauseStreamProcessing should return false for non-existent job`() = runTest {
        // When
        val result = adapter.pauseStreamProcessing("non-existent-message")

        // Then
        assertFalse(result)
    }

    @Test
    fun `resumeStreamProcessing should return false for non-existent job`() = runTest {
        // When
        val result = adapter.resumeStreamProcessing("non-existent-message")

        // Then
        assertFalse(result)
    }

    @Test
    fun `cancelStreamProcessing should return false for non-existent job`() = runTest {
        // When
        val result = adapter.cancelStreamProcessing("non-existent-message")

        // Then
        assertFalse(result)
    }
}