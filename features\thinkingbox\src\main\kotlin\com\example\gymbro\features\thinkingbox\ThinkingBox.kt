package com.example.gymbro.features.thinkingbox

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.history.HistoryActor
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.AIThinkingCard
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.FinalActionsRow
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.SimpleSummaryText
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.StreamingFinalRenderer
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.shouldShowAIThinkingCard
import com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel.ThinkingBoxViewModel
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.mapNotNull
import timber.log.Timber

/**
 * ThinkingBox - 主要的公共API组件（729方案3.md重构版）
 *
 * 🎯 核心职责：
 * - 提供统一的ThinkingBox公共接口
 * - 基于Segment队列架构的完整实现
 * - 集成HistoryActor进行History写入
 * - 保持与现有代码的兼容性
 *
 * 🔥 架构特点：
 * - 使用新的SegmentQueueViewModel
 * - 支持Segment队列渲染
 * - 实现三断点规则和双时序架构
 * - 集成HistoryActor监听Effect流
 */
@Composable
fun ThinkingBoxInternal(
    messageId: String,
    modifier: Modifier = Modifier,
    tokenFlow: Flow<String>? = null,
    viewModel: ThinkingBoxViewModel = hiltViewModel(),
    historyActor: HistoryActor? = null,
) {
    // 🔥 【初始化ViewModel】
    val state by viewModel.state.collectAsStateWithLifecycle()
    val lifecycleOwner = LocalLifecycleOwner.current

    // 🔥 【修复】正确获取HistoryActor - 使用EntryPoint而不是hiltViewModel
    val context = LocalContext.current
    val actualHistoryActor = historyActor ?: EntryPointAccessors.fromApplication(
        context.applicationContext,
        ThinkingBoxEntryPoint::class.java,
    ).historyActor()

    // 🔥 【Coach-ThinkingBox重构】获取ViewModelProvider用于注册ViewModel
    val viewModelProvider = EntryPointAccessors.fromApplication(
        context.applicationContext,
        ThinkingBoxEntryPoint::class.java,
    ).viewModelProvider()

    // 🔥 【MVI规范】初始化ThinkingBox
    LaunchedEffect(messageId, tokenFlow) {
        Timber.d("TB-VM:  🚀 初始化ThinkingBox: $messageId, hasTokenFlow=${tokenFlow != null}")

        // 🔥 【Coach-ThinkingBox重构】注册ViewModel到Provider
        viewModelProvider.registerViewModel(messageId, viewModel)

        // 🔥 【架构简化】ViewModel现在统一使用TokenBus订阅，不需要外部token流
        // 无论是否有tokenFlow，都使用相同的初始化方式
        viewModel.initialize(messageId)
    }

    // 🔥 【Coach-ThinkingBox重构】组件销毁时注销ViewModel
    DisposableEffect(messageId) {
        onDispose {
            Timber.d("TB-VM:  🧹 清理ThinkingBox: $messageId")
            viewModelProvider.unregisterViewModel(messageId)
        }
    }

    // 🔥 【History集成】初始化HistoryActor并连接Effect流
    LaunchedEffect(messageId, actualHistoryActor) {
        Timber.d("TB-VM:  🔗 连接HistoryActor到Effect流: $messageId")

        // 获取ViewModel的Effect流并连接到HistoryActor
        actualHistoryActor.initialize(
            effectFlow = viewModel.effect.mapNotNull { it }, // 过滤null值
            scope = lifecycleOwner.lifecycleScope,
        )
    }

    // 🔥 【完整UI组件编排】按照thinkingbox大纲.md第7节要求整合所有组件
    ThinkingBoxFullUI(
        state = state,
        messageId = messageId,
        modifier = modifier,
        onSegmentRendered = { segmentId ->
            Timber.d("TB-VM:  📤 段渲染完成回调: $segmentId")
            viewModel.onSegmentRendered(segmentId)
        },
    )
}

/**
 * 🔥 【修复】ThinkingBox EntryPoint - 用于获取非ViewModel依赖
 */
@EntryPoint
@InstallIn(SingletonComponent::class)
interface ThinkingBoxEntryPoint {
    fun historyActor(): HistoryActor

    // 🔥 【Coach-ThinkingBox重构】添加ViewModelProvider
    fun viewModelProvider(): com.example.gymbro.features.thinkingbox.internal.provider.ThinkingBoxViewModelProvider
}

/**
 * ThinkingBoxFullUI - 完整的UI组件编排实现
 *
 * 🎯 按照thinkingbox大纲.md第7节要求整合所有UI组件：
 * - UserMessage
 * - SimpleSummaryText
 * - FinalRichText (StreamingFinalRenderer)
 * - FinalActionsRow
 * - ScrollToBottomBtn
 *
 * 🔥 遵循Box+LazyColumn+Surface设计规范和MVI架构
 */
@Composable
private fun ThinkingBoxFullUI(
    state: ThinkingBoxContract.State,
    messageId: String,
    modifier: Modifier = Modifier,
    onSegmentRendered: ((String) -> Unit)? = null,
) {
    // 🔥 【Box+Column架构】修复嵌套滚动问题

    // 🔥 【状态计算】基于state计算UI显示逻辑
    val shouldShowThinking = shouldShowAIThinkingCard(state)
    val shouldShowFinal = state.finalReady // 🔥 【修复】使用正确的严格条件，等待思考段队列清空
    val finalContent = state.finalContent
    // 滚动按钮功能由外层LazyColumn处理，此处不再需要

    Box(modifier = modifier.fillMaxWidth()) {
        // 🔥 【修复嵌套滚动】使用Column替代LazyColumn，避免与外层LazyColumn冲突
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = Tokens.Spacing.Medium,
                    vertical = Tokens.Spacing.Small,
                ),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 1. 思考过程 - AIThinkingCard
            if (shouldShowThinking) {
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(Tokens.Radius.Medium),
                    tonalElevation = Tokens.Elevation.Small,
                ) {
                    AIThinkingCard(
                        state = state,
                        messageId = messageId,
                        modifier = Modifier.padding(Tokens.Spacing.Small),
                        onSegmentRendered = onSegmentRendered,
                    )
                }
            }

            // 2. 简单摘要文本 - SimpleSummaryText (如果需要显示摘要)
            if (shouldShowFinal && finalContent.length > 200) {
                val summaryText = finalContent.take(100) + "..."
                SimpleSummaryText(
                    content = summaryText,
                    modifier = Modifier.fillMaxWidth(),
                )
            }

            // 3. 最终富文本 - StreamingFinalRenderer
            if (shouldShowFinal) {
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(Tokens.Radius.Medium),
                    tonalElevation = Tokens.Elevation.Small,
                ) {
                    StreamingFinalRenderer(
                        finalTokens = if (finalContent.isNotEmpty()) finalContent.chunked(1) else emptyList(),
                        isFinalStreaming = !state.thinkingClosed,
                        modifier = Modifier.padding(Tokens.Spacing.Medium),
                        onRenderingComplete = {
                            // 渲染完成回调（不再需要滚动逻辑，因为外层LazyColumn会处理）
                        },
                    )
                }
            }

            // 4. 最终操作行 - FinalActionsRow
            if (shouldShowFinal && state.thinkingClosed) {
                FinalActionsRow(
                    finalContent = finalContent,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }

        // 注意：滚动按钮功能由外层LazyColumn处理，此处不再需要
    }
}

// ===============================================================================
// PREVIEW FUNCTIONS - @GymBroPreview 参数组
// ===============================================================================

@GymBroPreview
@Composable
private fun ThinkingBoxInternalPreview() {
    GymBroTheme {
        ThinkingBoxInternal(
            messageId = "preview-message-001",
            modifier = Modifier.fillMaxSize(),
        )
    }
}

@GymBroPreview
@Composable
private fun ThinkingBoxFullUIPreview() {
    GymBroTheme {
        // 模拟思考中状态
        val mockState = ThinkingBoxContract.State(
            messageId = "preview-message-002",
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = "segment-1",
                    kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.PERTHINK,
                    title = null,
                    content = "用户想要制定一个全面的力量训练计划...",
                    isComplete = true,
                ),
                ThinkingBoxContract.SegmentUi(
                    id = "segment-2",
                    kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.PHASE,
                    title = "分析用户需求",
                    content = "首先，我需要了解用户的当前健身水平和目标。基于这些信息，我可以设计一个个性化的训练方案。",
                    isComplete = true,
                ),
            ),
            finalContent = "基于您的需求，我为您设计了一个循序渐进的力量训练计划...",
            thinkingClosed = true,
            finalReady = true,
        )

        ThinkingBoxFullUI(
            state = mockState,
            messageId = "preview-message-002",
            modifier = Modifier.fillMaxSize(),
        )
    }
}

@GymBroPreview
@Composable
private fun ThinkingBoxThinkingInProgressPreview() {
    GymBroTheme {
        // 模拟思考进行中状态
        val mockState = ThinkingBoxContract.State(
            messageId = "preview-message-003",
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = "segment-1",
                    kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.PERTHINK,
                    title = null,
                    content = "正在分析用户的训练需求和目标...",
                    isComplete = false,
                ),
            ),
            finalContent = "",
            thinkingClosed = false,
            finalReady = false,
        )

        ThinkingBoxFullUI(
            state = mockState,
            messageId = "preview-message-003",
            modifier = Modifier.fillMaxSize(),
        )
    }
}

@GymBroPreview
@Composable
private fun ThinkingBoxEmptyStatePreview() {
    GymBroTheme {
        // 模拟空状态
        val mockState = ThinkingBoxContract.State(
            messageId = "preview-message-004",
            segmentsQueue = emptyList(),
            finalContent = "",
            thinkingClosed = false,
            finalReady = false,
        )

        ThinkingBoxFullUI(
            state = mockState,
            messageId = "preview-message-004",
            modifier = Modifier.fillMaxSize(),
        )
    }
}
