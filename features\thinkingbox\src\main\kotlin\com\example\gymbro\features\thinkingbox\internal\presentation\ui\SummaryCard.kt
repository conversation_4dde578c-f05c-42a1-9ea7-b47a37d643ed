package com.example.gymbro.features.thinkingbox.internal.presentation.ui

// 优化SummaryCard为流畅的底部弹出组件
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.em
import com.example.gymbro.designSystem.theme.tokens.ColorTokens
import com.example.gymbro.designSystem.theme.tokens.MapleMono
import com.example.gymbro.designSystem.theme.tokens.Tokens
import kotlinx.coroutines.launch
import timber.log.Timber
import com.example.gymbro.features.thinkingbox.domain.interfaces.UiState as StandardUiState

// 使用domain层的Summary类型，移除重复定义
// 使用 com.example.gymbro.features.thinkingbox.domain.model.events.Summary

/**
 * SummaryCard - 优化的思考内容展示卡片
 *
 * ✨ 优化特性：
 * - 单一Card组件设计，避免双层结构
 * - 流畅的半屏/全屏切换动画
 * - 优化的拖拽交互体验
 * - 性能优化的LazyColumn渲染
 *
 * 🎯 交互逻辑：
 * - 默认半屏展示，点击拖拽条或向上拖拽展开全屏
 * - 向下拖拽关闭或收起
 * - 全屏状态下支持内容滚动
 *
 * 🔥 【修复13】恢复完整的Sheet功能：
 * - 点击先显示半屏逻辑
 * - 下滑消失功能
 * - 滑块显示和交互
 */
@Composable
fun SummaryCard(
    uiState: StandardUiState,
    isExpanded: Boolean,
    onToggle: () -> Unit,
    onSourceClick: (String) -> Unit = {},
    modifier: Modifier = Modifier,
) {
    // 获取思考内容
    val thinkingContent =
        remember(uiState.phases) {
            buildThinkingContent(uiState.phases)
        }

    // 🔥 【719施工方案 2.4】统一可见性条件：isThinkingComplete && !finalRichTextReady
    val shouldShowSummary = uiState.isThinkingComplete && !uiState.finalRichTextReady && isExpanded

    // 🔥 【修复方案1】使用状态管理半屏/全屏切换，初始状态为半屏
    var isFullScreen by remember { mutableStateOf(false) }

    // 🔥 【修复方案2】重置全屏状态当面板关闭时
    LaunchedEffect(isExpanded) {
        if (!isExpanded) {
            isFullScreen = false
        }
    }

    // 🔥 【修复方案4】外部点击关闭功能 - 使用 Box 包装并添加背景点击检测
    if (shouldShowSummary) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.3f)) // 半透明背景
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() },
                ) {
                    // 点击外部区域关闭面板
                    onToggle()
                    Timber.tag("TB-SUMMARY-CARD").d("🔥 [外部点击] 点击外部区域，关闭SummaryCard")
                },
        ) {
            // 🔥 【修复方案3】SummaryCard 内容区域，阻止点击穿透
            OptimizedThinkingBottomSheet(
                content = thinkingContent,
                elapsed = remember(uiState.thinkingDuration) {
                    kotlin.time.Duration.parse("${uiState.thinkingDuration}ms")
                },
                isFullScreen = isFullScreen,
                onToggleFullScreen = {
                    isFullScreen = !isFullScreen
                    Timber.tag("TB-SUMMARY-CARD").d("🔥 [全屏切换] 切换到: ${if (isFullScreen) "全屏" else "半屏"}")
                },
                onDismiss = {
                    onToggle()
                    Timber.tag("TB-SUMMARY-CARD").d("🔥 [关闭按钮] 通过关闭按钮关闭SummaryCard")
                },
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() },
                    ) {
                        // 阻止点击事件传播到外部背景
                    },
            )
        }
    }
}

/**
 * 优化的思考内容底部弹出卡片
 * 🔥 【修复13】恢复完整的Sheet功能
 */
@Composable
private fun OptimizedThinkingBottomSheet(
    content: String,
    elapsed: kotlin.time.Duration,
    isFullScreen: Boolean,
    onToggleFullScreen: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val coroutineScope = rememberCoroutineScope()

    // 屏幕高度配置
    val halfScreenHeight = (configuration.screenHeightDp * 0.5f).dp
    val fullScreenHeight = configuration.screenHeightDp.dp

    // 当前高度状态 - 使用Animatable实现更流畅的动画
    val currentHeight = remember { Animatable(halfScreenHeight.value, Float.VectorConverter) }

    // 拖拽状态
    var dragOffset by remember { mutableFloatStateOf(0f) }
    var isDragging by remember { mutableStateOf(false) }

    // 🔥 【丝滑动画优化】优化半屏到全屏的过渡动画
    LaunchedEffect(isFullScreen) {
        val targetHeight = if (isFullScreen) fullScreenHeight.value else halfScreenHeight.value

        // 使用更丝滑的动画效果
        currentHeight.animateTo(
            targetValue = targetHeight,
            animationSpec = if (isFullScreen) {
                // 展开动画：更快速、轻微弹性
                spring(
                    dampingRatio = Spring.DampingRatioLowBouncy,
                    stiffness = Spring.StiffnessMediumLow,
                    visibilityThreshold = 0.1f,
                )
            } else {
                // 收起动画：更平滑、无弹性
                tween(
                    durationMillis = 350,
                    easing = FastOutSlowInEasing,
                )
            },
        )
    }

    // 🔥 【丝滑动画优化】背景遮罩透明度动画
    val backgroundAlpha by animateFloatAsState(
        targetValue = if (isFullScreen) 0.7f else 0.5f,
        animationSpec = tween(
            durationMillis = 300,
            easing = FastOutSlowInEasing,
        ),
        label = "backgroundAlpha",
    )

    // 背景遮罩
    Box(
        modifier =
        Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = backgroundAlpha))
            .clickable(
                indication = null,
                interactionSource = remember { MutableInteractionSource() },
            ) { onDismiss() },
    ) {
        // 主卡片
        Surface(
            modifier = modifier
                .fillMaxWidth()
                .height((currentHeight.value + dragOffset).dp)
                .align(Alignment.BottomCenter)
                .clip(
                    RoundedCornerShape(
                        topStart = Tokens.Radius.Large,
                        topEnd = Tokens.Radius.Large,
                        bottomStart = 0.dp,
                        bottomEnd = 0.dp,
                    ),
                )
                .pointerInput(Unit) {
                    detectDragGestures(
                        onDragStart = { isDragging = true },
                        onDragEnd = {
                            isDragging = false
                            val threshold = with(density) { 100.dp.toPx() }

                            coroutineScope.launch {
                                Timber.tag(
                                    "SummaryCard",
                                ).d(
                                    "🔥 [拖拽结束] dragOffset=$dragOffset, threshold=$threshold, isFullScreen=$isFullScreen",
                                )

                                when {
                                    // 🔥 【滑动块修复】向下拖拽 → dragOffset < 0 → 收起或关闭
                                    dragOffset < -threshold -> {
                                        if (isFullScreen) {
                                            Timber.tag("TB-SUMMARY-CARD").d("🔥 [拖拽操作] 全屏 → 半屏")
                                            onToggleFullScreen() // 全屏 → 半屏
                                        } else {
                                            Timber.tag("TB-SUMMARY-CARD").d("🔥 [拖拽操作] 半屏 → 关闭")
                                            onDismiss() // 半屏 → 关闭
                                        }
                                    }
                                    // 🔥 【滑动块修复】向上拖拽 → dragOffset > 0 → 展开
                                    dragOffset > threshold && !isFullScreen -> {
                                        Timber.tag("TB-SUMMARY-CARD").d("🔥 [拖拽操作] 半屏 → 全屏")
                                        onToggleFullScreen() // 半屏 → 全屏
                                    }
                                    else -> {
                                        Timber.tag(
                                            "SummaryCard",
                                        ).d("🔥 [拖拽操作] 无操作，dragOffset=$dragOffset 未达到阈值")
                                    }
                                }
                                // 重置拖拽偏移
                                dragOffset = 0f
                            }
                        },
                    ) { _, dragAmount ->
                        // 🔥 【Coach反向布局最终修复】反转拖拽逻辑以符合用户直觉
                        // 向下拖拽(dragAmount.y > 0) → 卡片高度减少(收起) → dragOffset为负
                        // 向上拖拽(dragAmount.y < 0) → 卡片高度增加(展开) → dragOffset为正
                        val newOffset = dragOffset - dragAmount.y / density.density // 🔥 关键：使用减法反转
                        val minOffset = -200f // 最大向下拖拽（收起/关闭）
                        val maxOffset = if (isFullScreen) 200f else 400f // 最大向上拖拽（展开）

                        dragOffset = newOffset.coerceIn(minOffset, maxOffset)

                        // 🔥 调试：记录拖拽方向和高度变化
                        if (kotlin.math.abs(dragAmount.y) > 5) { // 只记录明显的拖拽
                            val currentHeightWithOffset = currentHeight.value + dragOffset
                            timber.log.Timber
                                .tag("SummaryCard")
                                .d(
                                    "🔥 拖拽: dragAmount.y=${dragAmount.y}, dragOffset=$dragOffset, 当前高度=$currentHeightWithOffset, 用户手势=${if (dragAmount.y > 0) "向下拖拽(收起)" else "向上拖拽(展开)"}",
                                )
                        }
                    }
                }
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() },
                ) { /* 阻止点击穿透 */ },
            color = ColorTokens.Dark.Surface,
            shadowElevation = Tokens.Elevation.Large,
        ) {
            ThinkingCardContent(
                content = content,
                elapsed = elapsed,
                isFullScreen = isFullScreen,
                onToggleFullScreen = onToggleFullScreen,
                onDismiss = onDismiss,
            )
        }
    }
}

/**
 * 思考内容卡片主体内容
 */
@Composable
private fun ThinkingCardContent(
    content: String,
    elapsed: kotlin.time.Duration,
    isFullScreen: Boolean,
    onToggleFullScreen: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxSize(),
    ) {
        // 顶部拖动条和标题区域
        ThinkingCardHeader(
            elapsed = elapsed,
            isFullScreen = isFullScreen,
            onToggleFullScreen = onToggleFullScreen,
            onDismiss = onDismiss,
            modifier = Modifier.fillMaxWidth(),
        )

        // 分隔线
        HorizontalDivider(
            color = ColorTokens.Dark.OutlineVariant.copy(alpha = 0.12f),
            thickness = Tokens.Size.IndicatorSmall,
        )

        // 内容区域 - 使用LazyColumn优化性能
        OptimizedThinkingContent(
            content = content,
            modifier =
            Modifier
                .weight(1f)
                .fillMaxWidth(),
        )
    }
}

/**
 * 优化的思考内容头部
 */
@Composable
private fun ThinkingCardHeader(
    elapsed: kotlin.time.Duration,
    isFullScreen: Boolean,
    onToggleFullScreen: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier =
        modifier.padding(
            top = Tokens.Spacing.Medium,
            start = Tokens.Spacing.Medium,
            end = Tokens.Spacing.Medium,
            bottom = Tokens.Spacing.Small,
        ),
    ) {
        // 顶部拖动条
        Box(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(bottom = Tokens.Spacing.Medium),
            contentAlignment = Alignment.Center,
        ) {
            // 🔥 【丝滑动画优化】拖动指示条 - 添加视觉反馈动画
            val dragBarAlpha by animateFloatAsState(
                targetValue = if (isFullScreen) 0.6f else 0.4f,
                animationSpec = tween(
                    durationMillis = 200,
                    easing = FastOutSlowInEasing,
                ),
                label = "dragBarAlpha",
            )

            val dragBarWidth by animateDpAsState(
                targetValue = if (isFullScreen) Tokens.Icon.TouchTargetSmall + Tokens.Spacing.Small else Tokens.Icon.TouchTargetSmall,
                animationSpec = tween(
                    durationMillis = 250,
                    easing = FastOutSlowInEasing,
                ),
                label = "dragBarWidth",
            )

            Box(
                modifier =
                Modifier
                    .width(dragBarWidth)
                    .height(Tokens.Size.IndicatorLarge)
                    .background(
                        color = ColorTokens.Dark.OnSurfaceVariant.copy(alpha = dragBarAlpha),
                        shape = RoundedCornerShape(Tokens.Radius.Tiny),
                    )
                    // 🔥 【滑动块修复】扩大点击区域，确保用户容易点击拖拽条
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() },
                    ) {
                        onToggleFullScreen()
                        Timber.tag("TB-SUMMARY-CARD").d("🔥 [拖拽条点击] 切换全屏状态: $isFullScreen → ${!isFullScreen}")
                    }
                    .padding(vertical = Tokens.Spacing.Small), // 扩大垂直点击区域
            )

            // 右上角关闭按钮
            IconButton(
                onClick = onDismiss,
                modifier =
                Modifier
                    .align(Alignment.CenterEnd)
                    .size(Tokens.Icon.Large),
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "关闭",
                    tint = ColorTokens.Dark.OnSurfaceVariant,
                    modifier = Modifier.size(Tokens.Icon.Medium),
                )
            }
        }

        // 标题：已思考 X时间
        Text(
            text = "已思考 ${formatElapsedTime(elapsed)}",
            style = androidx.compose.ui.text.TextStyle(
                fontFamily = MapleMono, // 🔥 【字体系统修复】使用项目标准字体族
                fontSize = Tokens.Typography.CardTitle,
                fontWeight = FontWeight.Medium,
                color = ColorTokens.Dark.OnSurface,
            ),
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

/**
 * 优化的思考内容显示组件
 */
@Composable
private fun OptimizedThinkingContent(
    content: String,
    modifier: Modifier = Modifier,
) {
    // 处理思考内容为可渲染的块
    val contentBlocks =
        remember(content) {
            processMarkdownContent(content)
        }

    // 使用LazyColumn优化性能，避免大量内容时的卡顿
    LazyColumn(
        modifier = modifier,
        contentPadding =
        PaddingValues(
            start = Tokens.Spacing.Medium,
            end = Tokens.Spacing.Medium,
            top = Tokens.Spacing.Small,
            bottom = Tokens.Spacing.Large,
        ),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
    ) {
        items(
            items = contentBlocks,
            key = { block -> "${block.type}_${block.content.hashCode()}" },
        ) { block ->
            ThinkingContentBlock(
                block = block,
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}

/**
 * 🔥 【步骤4修复】构建思考内容的markdown字符串
 * 先拼"预思考"段，再拼各phase；跳过perthink
 */
private fun buildThinkingContent(
    phases: List<com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi>,
): String {
    if (phases.isEmpty()) return ""

    return buildString {
        // 🔥 【步骤4修复】先处理perthink阶段作为"预思考"段
        val perthinkPhase = phases.find { it.id == "perthink" }
        if (perthinkPhase != null && perthinkPhase.content.isNotBlank()) {
            appendLine("## 预思考")
            appendLine()
            appendLine(perthinkPhase.content)
            appendLine()
        }

        // 🔥 【步骤4修复】再处理其他正式phase，跳过perthink
        phases.filter { it.id != "perthink" }.forEach { phase ->
            if (!phase.title.isNullOrBlank()) {
                appendLine("## ${phase.title}")
                appendLine()
            }
            if (phase.content.isNotBlank()) {
                appendLine(phase.content)
                appendLine()
            }
        }
    }.trim()
}

/**
 * 思考内容块组件 - 渲染单个内容块
 */
@Composable
private fun ThinkingContentBlock(
    block: ThinkingBlock,
    modifier: Modifier = Modifier,
) {
    when (block.type) {
        ThinkingBlockType.HEADING -> {
            Text(
                text = block.content,
                style = androidx.compose.ui.text.TextStyle(
                    fontFamily = MapleMono, // 🔥 【字体系统修复】使用项目标准字体族
                    fontSize = when (block.level) {
                        1 -> Tokens.Typography.DisplayLarge
                        2 -> Tokens.Typography.Display
                        3 -> Tokens.Typography.HeadlineLarge
                        else -> Tokens.Typography.Headline
                    },
                    fontWeight = FontWeight.Bold,
                    color = ColorTokens.Dark.OnSurface,
                ),
                modifier = modifier.padding(vertical = Tokens.Spacing.Small),
            )
        }
        ThinkingBlockType.PARAGRAPH -> {
            Text(
                text = block.content,
                style = androidx.compose.ui.text.TextStyle(
                    fontFamily = MapleMono, // 🔥 【字体系统修复】使用项目标准字体族
                    fontSize = Tokens.Typography.BodyLarge,
                    lineHeight = 1.6.em,
                    color = ColorTokens.Dark.OnSurface,
                ),
                modifier = modifier,
            )
        }
        ThinkingBlockType.LIST_ITEM -> {
            Row(
                modifier = modifier,
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            ) {
                Text(
                    text = "•",
                    style = androidx.compose.ui.text.TextStyle(
                        fontFamily = MapleMono, // 🔥 【字体系统修复】使用项目标准字体族
                        fontSize = Tokens.Typography.BodyLarge,
                        color = ColorTokens.Dark.Primary,
                    ),
                    modifier = Modifier.padding(top = Tokens.Spacing.Tiny),
                )
                Text(
                    text = block.content,
                    style = androidx.compose.ui.text.TextStyle(
                        fontFamily = MapleMono, // 🔥 【字体系统修复】使用项目标准字体族
                        fontSize = Tokens.Typography.BodyLarge,
                        lineHeight = 1.6.em,
                        color = ColorTokens.Dark.OnSurface,
                    ),
                    modifier = Modifier.weight(1f),
                )
            }
        }
        ThinkingBlockType.CODE_BLOCK -> {
            Surface(
                modifier = modifier,
                shape = RoundedCornerShape(Tokens.Radius.Small),
                color = ColorTokens.Dark.SurfaceVariant,
            ) {
                Text(
                    text = block.content,
                    style = androidx.compose.ui.text.TextStyle(
                        fontFamily = MapleMono, // 🔥 【字体系统修复】使用项目标准字体族
                        fontSize = Tokens.Typography.Body,
                        color = ColorTokens.Dark.OnSurfaceVariant,
                    ),
                    modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(Tokens.Spacing.Medium),
                )
            }
        }
        else -> {
            Text(
                text = block.content,
                style = androidx.compose.ui.text.TextStyle(
                    fontFamily = MapleMono, // 🔥 【字体系统修复】使用项目标准字体族
                    fontSize = Tokens.Typography.BodyLarge,
                    lineHeight = 1.6.em,
                    color = ColorTokens.Dark.OnSurface,
                ),
                modifier = modifier,
            )
        }
    }
}

/**
 * 思考内容块类型
 */
private enum class ThinkingBlockType {
    HEADING,
    PARAGRAPH,
    LIST_ITEM,
    CODE_BLOCK,
    TEXT,
}

/**
 * 思考内容块数据
 */
private data class ThinkingBlock(
    val type: ThinkingBlockType,
    val content: String,
    val level: Int = 0, // 用于标题级别
)

/**
 * 处理markdown内容，转换为可渲染的块
 */
private fun processMarkdownContent(markdown: String): List<ThinkingBlock> {
    if (markdown.isBlank()) return emptyList()

    val blocks = mutableListOf<ThinkingBlock>()
    val lines = markdown.split("\n")

    var i = 0
    while (i < lines.size) {
        val line = lines[i].trim()

        when {
            // 标题
            line.startsWith("#") -> {
                val level = line.takeWhile { it == '#' }.length
                val content = line.drop(level).trim()
                blocks.add(ThinkingBlock(ThinkingBlockType.HEADING, content, level))
            }
            // 列表项
            line.startsWith("- ") || line.startsWith("* ") -> {
                val content = line.drop(2).trim()
                blocks.add(ThinkingBlock(ThinkingBlockType.LIST_ITEM, content))
            }
            // 代码块
            line.startsWith("```") -> {
                val codeLines = mutableListOf<String>()
                i++ // 跳过开始的```
                while (i < lines.size && !lines[i].trim().startsWith("```")) {
                    codeLines.add(lines[i])
                    i++
                }
                blocks.add(ThinkingBlock(ThinkingBlockType.CODE_BLOCK, codeLines.joinToString("\n")))
            }
            // 普通段落
            line.isNotEmpty() -> {
                blocks.add(ThinkingBlock(ThinkingBlockType.PARAGRAPH, line))
            }
        }
        i++
    }

    return blocks
}

/**
 * 格式化已用时间
 */
private fun formatElapsedTime(duration: kotlin.time.Duration): String {
    val totalSeconds = duration.inWholeSeconds
    val minutes = totalSeconds / 60
    val seconds = totalSeconds % 60

    return when {
        minutes > 0 -> "${minutes}分${seconds}秒"
        else -> "${seconds}秒"
    }
}

// 🔥 【V2系统清理】兼容性重载已移除，V2系统使用统一的StandardUiState

// ==================== Preview Functions ====================

@Composable
private fun createMockStandardUiState(
    isThinkingComplete: Boolean = true,
    finalRichTextReady: Boolean = false,
    phases: List<com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi> = emptyList(),
): StandardUiState {
    return com.example.gymbro.features.thinkingbox.domain.interfaces.UiState(
        isStreaming = false,
        preThinking = null,
        phases = phases,
        activePhaseId = null,
        isThinkingComplete = isThinkingComplete,
        finalContentArrived = false,
        finalRichTextReady = finalRichTextReady,
        finalTokens = emptyList(),
        finalMarkdown = null,
        isFinalStreaming = false,
        isConversationComplete = isThinkingComplete && finalRichTextReady,
        thinkingDuration = 15000L,
    )
}

@Composable
private fun createMockPhaseUi(
    id: String,
    title: String,
    content: String,
    isComplete: Boolean = true,
): com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi {
    return com.example.gymbro.features.thinkingbox.domain.interfaces.PhaseUi(
        id = id,
        title = title,
        content = content,
        isComplete = isComplete,
    )
}

/**
 * SummaryCard Preview - 半屏模式
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun SummaryCardPreview_HalfScreen() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        SummaryCard(
            uiState = createMockStandardUiState(
                phases = listOf(
                    createMockPhaseUi(
                        id = "analyze",
                        title = "分析问题",
                        content = "我需要仔细分析你提出的健身计划问题，考虑你的目标、经验水平和可用时间。",
                    ),
                    createMockPhaseUi(
                        id = "plan",
                        title = "制定方案",
                        content = "基于分析结果，我为你制定了个性化的训练计划，包括力量训练和有氧运动的合理搭配。",
                    ),
                ),
            ),
            isExpanded = true,
            onToggle = { },
            modifier = Modifier.fillMaxSize(),
        )
    }
}

/**
 * SummaryCard Preview - 复杂内容
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun SummaryCardPreview_Complex() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        SummaryCard(
            uiState = createMockStandardUiState(
                phases = listOf(
                    createMockPhaseUi(
                        id = "perthink",
                        title = "预思考",
                        content = "让我仔细思考一下你的健身需求...",
                    ),
                    createMockPhaseUi(
                        id = "analyze",
                        title = "分析需求",
                        content = "## 目标分析\n\n- 增肌\n- 减脂\n- 提高体能\n\n## 时间安排\n\n每周3-4次训练，每次60-90分钟。",
                    ),
                    createMockPhaseUi(
                        id = "plan",
                        title = "训练计划",
                        content = "```workout\n周一：胸+三头\n周三：背+二头\n周五：腿+肩\n```\n\n每个部位进行4-5个动作，每个动作3-4组。",
                    ),
                ),
            ),
            isExpanded = true,
            onToggle = { },
            modifier = Modifier.fillMaxSize(),
        )
    }
}

/**
 * SummaryCard Preview - 简单内容
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun SummaryCardPreview_Simple() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        SummaryCard(
            uiState = createMockStandardUiState(
                phases = listOf(
                    createMockPhaseUi(
                        id = "quick",
                        title = "快速回答",
                        content = "好的，我明白了你的需求。",
                    ),
                ),
            ),
            isExpanded = true,
            onToggle = { },
            modifier = Modifier.fillMaxSize(),
        )
    }
}
