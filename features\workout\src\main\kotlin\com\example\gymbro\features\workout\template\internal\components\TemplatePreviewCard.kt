package com.example.gymbro.features.workout.template.internal.components

// 🔥 修复：清理拖动逻辑，专注于展示功能
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.DragHandle
import androidx.compose.material.icons.filled.VerticalAlignTop
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.domain.workout.model.DraftSource
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.model.summary
import com.example.gymbro.features.workout.shared.components.SwipeToDeleteWrapper
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import com.example.gymbro.shared.models.workout.summary
import kotlinx.datetime.Clock
import timber.log.Timber

/**
 * 统一的模板预览卡片组件 - 支持拖拽排序和滑动删除
 *
 * 🎯 功能特性：
 * - 支持 WorkoutTemplateDto（已发布模板）和 TemplateDraft（草稿）两种数据类型
 * - 显示完整统计信息：总动作数、总重量、总组数
 * - 区分模板状态（已发布 vs 草稿）
 * - 统一的操作按钮（编辑/转正）
 * - 🔥 新增：滑动删除功能
 * - 🔥 新增：拖拽排序功能
 * - 🔥 新增：一键置顶功能
 *
 * 🏗️ 架构原则：
 * - 遵循 MVI 2.0 架构模式
 * - 使用 MaterialTheme.workoutColors.* 和 Tokens.* 系统
 * - 符合 Box+LazyColumn+Surface UI 设计规范
 * - 支持 @GymBroPreview 预览
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemplatePreviewCard(
    template: WorkoutTemplateDto? = null,
    draft: TemplateDraft? = null,
    onItemClick: (String) -> Unit,
    onEditClick: (String) -> Unit,
    onDeleteClick: ((String) -> Unit)? = null, // 🔥 删除回调
    onMoveToTopClick: ((String) -> Unit)? = null, // 🔥 一键置顶功能
    // 🔥 修复：简化参数，移除拖动逻辑
    currentIndex: Int = -1, // 当前索引，用于置顶功能
    isDragging: Boolean = false, // 是否正在拖拽状态（由 TemplateScreen 管理，仅用于视觉反馈）
    modifier: Modifier = Modifier,
) {
    // 🔥 修复：数据提取逻辑，确保状态标识和统计信息准确关联数据库
    val (id, name, exerciseCount, statsText, isDraft) = when {
        template != null -> {
            val id = template.id
            val name = template.name.takeIf { it.isNotBlank() } ?: "未命名模板"
            val exerciseCount = template.exercises.size

            // 🔥 调试：记录模板数据加载情况
            Timber.d("🔍 [PREVIEW-CARD] 模板数据: id=$id, name=$name, exercises=${template.exercises.size}")
            template.exercises.forEachIndexed { index, exercise ->
                Timber.d(
                    "🔍 [PREVIEW-CARD] 动作${index + 1}: ${exercise.exerciseName}, customSets=${exercise.customSets.size}, 默认sets=${exercise.sets}",
                )
            }

            // 🔥 修复：统计信息基于真实的customSets数据计算
            val statsText = try {
                if (template.exercises.isNotEmpty()) {
                    // 计算基于 customSets 的真实统计信息
                    val totalSets = template.exercises.sumOf { exercise ->
                        val customSetsCount = exercise.customSets.size.takeIf { it > 0 } ?: exercise.sets
                        Timber.d(
                            "🔍 [PREVIEW-CARD] 动作${exercise.exerciseName}: customSets=${exercise.customSets.size}, 使用组数=$customSetsCount",
                        )
                        customSetsCount
                    }
                    val totalVolume = template.exercises.sumOf { exercise ->
                        if (exercise.customSets.isNotEmpty()) {
                            exercise.customSets.sumOf { set ->
                                (set.targetWeight * set.targetReps).toDouble()
                            }
                        } else {
                            ((exercise.targetWeight ?: 0f) * exercise.reps * exercise.sets).toDouble()
                        }
                    }
                    val result = "${totalSets}组 · ${totalVolume.toInt()}kg"
                    Timber.d("🔍 [PREVIEW-CARD] 统计结果: $result")
                    result
                } else {
                    "暂无动作"
                }
            } catch (e: Exception) {
                // 如果计算失败，显示基本信息
                Timber.e(e, "🔍 [PREVIEW-CARD] 统计计算失败")
                "${template.exercises.size} 个动作"
            }

            // 🔥 修复：根据版本控制逻辑，正确判断模板状态
            // 只有明确标记为已发布的模板才显示为"模板"，否则显示为"草稿"
            val isDraft = when {
                template.isPublished == true && template.isDraft == false -> false // 已发布的模板
                template.isDraft == false && template.isPublished != true -> false // 兼容旧数据：没发布标记但不是草稿
                else -> true // 其他情况都认为是草稿
            }
            
            // 🔥 调试：记录状态判断过程
            Timber.d("🔍 [PREVIEW-CARD] 模板状态判断: id=$id, name=$name")
            Timber.d("🔍 [PREVIEW-CARD] 原始状态: isDraft=${template.isDraft}, isPublished=${template.isPublished}")
            Timber.d("🔍 [PREVIEW-CARD] 判断结果: isDraft=$isDraft")
            listOf(id, name, exerciseCount, statsText, isDraft)
        }
        draft != null -> {
            val id = draft.id
            val name = draft.name.takeIf { it.isNotBlank() } ?: "未命名草稿"
            val exerciseCount = draft.exercises.size
            // 🔥 修复：统计信息关联真实数据
            val statsText = try {
                if (draft.exercises.isNotEmpty()) {
                    // 使用 TemplateDraft.summary 扩展属性，基于 totalVolume 和 totalSets 计算
                    draft.summary
                } else {
                    "暂无动作"
                }
            } catch (e: Exception) {
                // 如果计算失败，显示基本信息
                "${draft.exercises.size} 个动作"
            }
            val isDraft = true // 草稿始终为 true，符合业务逻辑
            listOf(id, name, exerciseCount, statsText, isDraft)
        }
        else -> {
            error("Either template or draft must be provided")
        }
    }

    // 🔥 计算状态文本 - 修复null pointer exception
    val statusText = if (isDraft == true) "草稿" else "模板"

    // 🔥 修复：简化组件，专注于展示和基本交互
    // 使用 Material 3 官方滑动删除功能
    SwipeToDeleteWrapper(
        onDelete = {
            onDeleteClick?.invoke(id as String)
            true // 确认删除
        },
        deleteIcon = Icons.Default.Delete,
        modifier = modifier,
    ) {
        TemplatePreviewCardContent(
            id = id as String,
            name = name as String,
            exerciseCount = exerciseCount as Int,
            statsText = statsText as String,
            isDraft = isDraft == true, // 安全的Boolean转换
            statusText = statusText,
            currentIndex = currentIndex,
            isDragging = isDragging,
            onItemClick = onItemClick,
            onEditClick = onEditClick,
            onMoveToTopClick = onMoveToTopClick,
        )
    }
}

/**
 * 模板预览卡片内容组件
 */
@Composable
private fun TemplatePreviewCardContent(
    id: String,
    name: String,
    exerciseCount: Int,
    statsText: String,
    isDraft: Boolean,
    statusText: String,
    currentIndex: Int,
    isDragging: Boolean,
    onItemClick: (String) -> Unit,
    onEditClick: (String) -> Unit,
    onMoveToTopClick: ((String) -> Unit)?,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small)
            // 🔥 修复：拖动动画层级问题 - 提升到最顶层
            .zIndex(if (isDragging) 10f else 0f) // 拖拽时提升到最高层级
            .graphicsLayer {
                // 🔥 修复：拖拽时的视觉效果 - 只保留必要的视觉反馈
                scaleX = if (isDragging) 1.05f else 1f // 增加缩放效果
                scaleY = if (isDragging) 1.05f else 1f
                alpha = if (isDragging) 0.95f else 1f // 减少透明度变化
                shadowElevation = if (isDragging) Tokens.Elevation.Medium.toPx() else 0f // 添加阴影效果
            }
            // 🔥 修复：移除拖动逻辑，使用简单点击
            .clickable { onItemClick(id) },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.workoutColors.cardBackground, // 🔥 修复：使用 GymBro 自定义颜色系统
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = Tokens.Elevation.Card, // 🔥 修复：使用统一的Token路径
        ),
        onClick = { onItemClick(id) }, // 🔥 修复：使用Card的onClick替代clickable修饰符，避免手势冲突
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.CardPadding), // 🔥 修复：使用统一的Token路径
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Column(
                modifier = Modifier.weight(1f),
            ) {
                // 标题行：模板名称 + 状态标识
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        text = name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.workoutColors.textPrimary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f),
                    )

                    // 🔥 修复：始终显示状态标识，区分草稿和模板
                    Spacer(modifier = Modifier.width(Tokens.Spacing.Small))

                    // 状态标识：草稿 or 模板
                    Surface(
                        color = if (isDraft) {
                            MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.1f)
                        } else {
                            MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.1f)
                        },
                        shape = RoundedCornerShape(Tokens.Radius.XSmall),
                    ) {
                        Text(
                            text = statusText,
                            style = MaterialTheme.typography.labelSmall,
                            color = if (isDraft) {
                                MaterialTheme.workoutColors.accentPrimary
                            } else {
                                MaterialTheme.workoutColors.accentSecondary
                            },
                            modifier = Modifier.padding(
                                horizontal = Tokens.Spacing.Small,
                                vertical = Tokens.Spacing.Tiny,
                            ),
                        )
                    }
                }

                Spacer(modifier = Modifier.height(Tokens.Spacing.Tiny))

                // 统计信息行：动作数量 + 详细统计
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        text = "$exerciseCount 个动作",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.workoutColors.textSecondary,
                    )

                    Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

                    Text(
                        text = statsText,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.workoutColors.textSecondary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            }

            // 🔥 修复：操作按钮区域 - 置顶功能 + 拖拽手柄
            Row(
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // 🔥 一键置顶功能按钮
                if (currentIndex > 0 && onMoveToTopClick != null) {
                    IconButton(
                        onClick = { onMoveToTopClick(id) },
                    ) {
                        Icon(
                            imageVector = Icons.Default.VerticalAlignTop,
                            contentDescription = "置顶",
                            tint = MaterialTheme.workoutColors.accentPrimary,
                            modifier = Modifier.size(Tokens.Icon.Medium),
                        )
                    }
                }

                // 🔥 修复：拖拽手柄作为视觉指示器
                // 拖动功能由 TemplateScreen 的 LazyColumn 层级处理
                Icon(
                    imageVector = Icons.Default.DragHandle,
                    contentDescription = "拖拽排序",
                    tint = MaterialTheme.workoutColors.accentSecondary,
                    modifier = Modifier.size(Tokens.Icon.Medium),
                )
            }
        }
    }
}

// === Preview 组件 ===

@GymBroPreview
@Composable
private fun TemplatePreviewCardTemplatePreview() {
    GymBroTheme {
        Surface(
            color = MaterialTheme.workoutColors.cardBackground,
        ) {
            Column(
                modifier = Modifier.padding(Tokens.Spacing.Medium),
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
            ) {
                // 模板预览
                TemplatePreviewCard(
                    template = WorkoutTemplateDto(
                        id = "template_1",
                        name = "胸部训练模板",
                        description = "专注胸部肌群的训练模板",
                        exercises = listOf(
                            // 模拟数据，实际使用时需要完整的 TemplateExerciseDto
                        ),
                    ),
                    onItemClick = { },
                    onEditClick = { },
                )

                // 草稿预览
                TemplatePreviewCard(
                    draft = TemplateDraft(
                        id = "draft_1",
                        name = "AI生成的训练草稿",
                        description = "基于用户需求生成的训练草稿",
                        exercises = emptyList(),
                        source = DraftSource.AI_GENERATED,
                        createdAt = Clock.System.now(),
                        updatedAt = Clock.System.now(),
                        userId = "user_1",
                    ),
                    onItemClick = { },
                    onEditClick = { },
                    // 🔥 修复：移除草稿转正按钮参数
                )
            }
        }
    }
}
