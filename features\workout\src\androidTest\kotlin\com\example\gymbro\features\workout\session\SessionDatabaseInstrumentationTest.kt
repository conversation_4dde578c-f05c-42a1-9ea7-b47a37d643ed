package com.example.gymbro.features.workout.session

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.exercise.model.ExerciseSet
import com.example.gymbro.domain.workout.model.session.SessionExercise
import com.example.gymbro.domain.workout.model.session.WorkoutSession
import com.example.gymbro.domain.workout.repository.SessionRepository
import com.example.gymbro.domain.workout.usecase.session.SaveWorkoutSessionUseCase
import com.example.gymbro.features.workout.session.internal.effect.SessionEffectHandler
import com.example.gymbro.features.workout.session.internal.reducer.SessionReducer
import com.example.gymbro.shared.models.exercise.MuscleGroup
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.todayIn
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import javax.inject.Inject
import kotlin.test.*

/**
 * Session数据库持久化仪器化测试
 *
 * 验证在真实Android数据库环境中的功能：
 * 1. 会话数据的完整保存和读取
 * 2. 动作数据的关联性保存
 * 3. 训练组数据的持久化
 * 4. 数据一致性和完整性验证
 * 5. 并发访问和事务处理
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class SessionDatabaseInstrumentationTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    @Inject
    lateinit var sessionRepository: SessionRepository

    @Inject
    lateinit var saveWorkoutSessionUseCase: SaveWorkoutSessionUseCase

    @Inject
    lateinit var sessionEffectHandler: SessionEffectHandler

    @Inject
    lateinit var sessionReducer: SessionReducer

    private val context = InstrumentationRegistry.getInstrumentation().targetContext

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun saveWorkoutSession_shouldPersistCompleteSessionData() = runTest {
        // Given - 创建完整的训练会话数据
        val sessionId = "db_test_session_${System.currentTimeMillis()}"
        val workoutSession = createCompleteWorkoutSession(sessionId)

        // When - 保存到真实数据库
        val saveResult = saveWorkoutSessionUseCase(workoutSession)

        // Then - 验证保存成功
        assertTrue(saveResult.isSuccess, "会话保存应该成功")

        // 验证数据完整性 - 从数据库读取验证
        val retrievedSessionResult = sessionRepository.getSessionById(sessionId)
        assertTrue(retrievedSessionResult.isSuccess, "应该能从数据库读取会话")

        val retrievedSession = retrievedSessionResult.getOrNull()
        assertNotNull(retrievedSession, "读取的会话不应该为null")

        // 验证基本信息
        assertEquals(sessionId, retrievedSession.id, "会话ID应该匹配")
        assertEquals(workoutSession.name, retrievedSession.name, "会话名称应该匹配")
        assertEquals(workoutSession.startTime, retrievedSession.startTime, "开始时间应该匹配")
        assertEquals(workoutSession.endTime, retrievedSession.endTime, "结束时间应该匹配")
        assertEquals(workoutSession.isCompleted, retrievedSession.isCompleted, "完成状态应该匹配")

        // 验证动作数据完整性
        assertEquals(workoutSession.exercises.size, retrievedSession.exercises.size, "动作数量应该匹配")

        workoutSession.exercises.forEachIndexed { index, originalExercise ->
            val retrievedExercise = retrievedSession.exercises[index]
            assertEquals(originalExercise.exerciseId, retrievedExercise.exerciseId, "动作ID应该匹配")
            assertEquals(originalExercise.name, retrievedExercise.name, "动作名称应该匹配")
            assertEquals(originalExercise.order, retrievedExercise.order, "动作顺序应该匹配")
            assertEquals(originalExercise.notes, retrievedExercise.notes, "动作备注应该匹配")

            // 验证训练组数据
            assertEquals(originalExercise.sets.size, retrievedExercise.sets.size, "训练组数量应该匹配")

            originalExercise.sets.forEachIndexed { setIndex, originalSet ->
                val retrievedSet = retrievedExercise.sets[setIndex]
                assertEquals(originalSet.weight, retrievedSet.weight, "重量应该匹配")
                assertEquals(originalSet.reps, retrievedSet.reps, "次数应该匹配")
                assertEquals(originalSet.isCompleted, retrievedSet.isCompleted, "完成状态应该匹配")
            }
        }
    }

    @Test
    fun sessionEffectHandler_saveSessionEffect_shouldWorkWithRealDatabase() = runTest {
        // Given - 创建会话状态
        val sessionId = "effect_test_session_${System.currentTimeMillis()}"
        val session = createCompleteWorkoutSession(sessionId)

        val saveSessionEffect = SessionContract.Effect.SaveSession(session)

        // When - 通过EffectHandler处理保存
        // 注意: 在真实测试中，我们需要通过ViewModel或直接调用EffectHandler
        // 这里模拟Effect处理流程
        val saveResult = saveWorkoutSessionUseCase(session)

        // Then - 验证保存结果
        assertTrue(saveResult.isSuccess, "通过Effect保存应该成功")

        // 验证数据确实被保存到数据库
        val verificationResult = sessionRepository.getSessionById(sessionId)
        assertTrue(verificationResult.isSuccess, "应该能读取保存的数据")

        val savedSession = verificationResult.getOrNull()
        assertNotNull(savedSession, "保存的会话不应该为null")
        assertEquals(sessionId, savedSession.id, "会话ID应该正确")
    }

    @Test
    fun addExercisesToSession_shouldPersistWithCorrectRelationships() = runTest {
        // Given - 初始空会话状态
        val sessionId = "exercise_add_test_${System.currentTimeMillis()}"
        var currentState = SessionContract.State(
            sessionId = sessionId,
            exercises = persistentListOf(),
        )

        // 创建要添加的动作
        val exercisesToAdd = listOf(
            createTestExercise("db_bench", "数据库卧推测试"),
            createTestExercise("db_squat", "数据库深蹲测试"),
        )

        // When - 通过Reducer添加动作
        val addIntent = SessionContract.Intent.AddExercises(exercisesToAdd)
        val addResult = sessionReducer.reduce(addIntent, currentState)
        currentState = addResult.newState

        // 创建完整的会话对象进行保存
        val completeSession = WorkoutSession(
            id = sessionId,
            date = Clock.System.todayIn(TimeZone.currentSystemDefault()),
            templateId = null,
            name = "数据库动作添加测试",
            description = "测试动作添加后的数据库保存",
            startTime = System.currentTimeMillis(),
            endTime = null,
            isCompleted = false,
            plannedDate = null,
            plannedTemplateId = null,
            status = WorkoutSession.Status.IN_PROGRESS,
            completionTimestamp = null,
            exercises = currentState.exercises.map { it.sessionExercise }.toImmutableList(),
            userId = "test_user",
        )

        // 保存到数据库
        val saveResult = saveWorkoutSessionUseCase(completeSession)
        assertTrue(saveResult.isSuccess, "添加动作后的会话保存应该成功")

        // Then - 验证动作关联关系正确保存
        val verificationResult = sessionRepository.getSessionById(sessionId)
        assertTrue(verificationResult.isSuccess, "应该能读取包含动作的会话")

        val savedSession = verificationResult.getOrNull()
        assertNotNull(savedSession, "保存的会话不应该为null")
        assertEquals(2, savedSession.exercises.size, "应该保存了2个动作")

        // 验证动作的完整数据
        val savedExercises = savedSession.exercises
        assertEquals("db_bench", savedExercises[0].exerciseId, "第一个动作ID应该正确")
        assertEquals("db_squat", savedExercises[1].exerciseId, "第二个动作ID应该正确")

        // 验证每个动作都有默认的训练组
        savedExercises.forEach { exercise ->
            assertTrue(exercise.sets.isNotEmpty(), "每个动作都应该有训练组")
            assertEquals(sessionId, exercise.sessionId, "动作应该关联到正确的会话")
        }
    }

    @Test
    fun concurrentSessionOperations_shouldMaintainDataIntegrity() = runTest {
        // Given - 准备多个会话进行并发测试
        val sessionIds = (1..5).map { "concurrent_session_${System.currentTimeMillis()}_$it" }
        val sessions = sessionIds.map { sessionId ->
            createCompleteWorkoutSession(sessionId)
        }

        // When - 并发保存多个会话
        val saveResults = sessions.map { session ->
            saveWorkoutSessionUseCase(session)
        }

        // Then - 验证所有会话都成功保存
        saveResults.forEach { result ->
            assertTrue(result.isSuccess, "并发保存的会话都应该成功")
        }

        // 验证数据完整性 - 每个会话都能正确读取
        sessionIds.forEach { sessionId ->
            val retrieveResult = sessionRepository.getSessionById(sessionId)
            assertTrue(retrieveResult.isSuccess, "并发保存的会话 $sessionId 应该能正确读取")

            val session = retrieveResult.getOrNull()
            assertNotNull(session, "会话 $sessionId 不应该为null")
            assertEquals(sessionId, session.id, "会话ID应该正确")
            assertTrue(session.exercises.isNotEmpty(), "会话应该包含动作数据")
        }
    }

    @Test
    fun updateSessionProgress_shouldPersistIncrementalChanges() = runTest {
        // Given - 已保存的会话
        val sessionId = "progress_update_test_${System.currentTimeMillis()}"
        val initialSession = createCompleteWorkoutSession(sessionId)

        // 保存初始会话
        val initialSaveResult = saveWorkoutSessionUseCase(initialSession)
        assertTrue(initialSaveResult.isSuccess, "初始会话保存应该成功")

        // When - 模拟训练进度更新
        val updatedExercises = initialSession.exercises.map { exercise ->
            exercise.copy(
                sets = exercise.sets.map { set ->
                    set.copy(isCompleted = true) // 标记所有组为完成
                },
            )
        }.toImmutableList()

        val updatedSession = initialSession.copy(
            exercises = updatedExercises,
            isCompleted = true,
            endTime = System.currentTimeMillis(),
            status = WorkoutSession.Status.COMPLETED,
        )

        // 保存更新后的会话
        val updateSaveResult = saveWorkoutSessionUseCase(updatedSession)
        assertTrue(updateSaveResult.isSuccess, "更新后的会话保存应该成功")

        // Then - 验证更新被正确持久化
        val verificationResult = sessionRepository.getSessionById(sessionId)
        assertTrue(verificationResult.isSuccess, "应该能读取更新后的会话")

        val savedSession = verificationResult.getOrNull()
        assertNotNull(savedSession, "更新后的会话不应该为null")
        assertTrue(savedSession.isCompleted, "会话应该标记为完成")
        assertNotNull(savedSession.endTime, "应该有结束时间")
        assertEquals(WorkoutSession.Status.COMPLETED, savedSession.status, "状态应该为已完成")

        // 验证所有训练组都标记为完成
        savedSession.exercises.forEach { exercise ->
            exercise.sets.forEach { set ->
                assertTrue(set.isCompleted, "所有训练组都应该标记为完成")
            }
        }
    }

    // === 辅助方法 ===

    private fun createCompleteWorkoutSession(sessionId: String): WorkoutSession {
        val exercises = listOf(
            createCompleteSessionExercise("bench_press", "卧推", sessionId, 0),
            createCompleteSessionExercise("squat", "深蹲", sessionId, 1),
        ).toImmutableList()

        return WorkoutSession(
            id = sessionId,
            date = Clock.System.todayIn(TimeZone.currentSystemDefault()),
            templateId = "test_template",
            name = "数据库测试训练",
            description = "用于数据库功能验证的测试训练",
            startTime = System.currentTimeMillis() - 3600000, // 1小时前开始
            endTime = System.currentTimeMillis(), // 现在结束
            isCompleted = true,
            plannedDate = null,
            plannedTemplateId = null,
            status = WorkoutSession.Status.COMPLETED,
            completionTimestamp = System.currentTimeMillis(),
            exercises = exercises,
            userId = "test_user_instrumentation",
        )
    }

    private fun createCompleteSessionExercise(
        exerciseId: String,
        name: String,
        sessionId: String,
        order: Int,
    ): SessionExercise {
        val sets = (1..3).map { setIndex ->
            ExerciseSet(
                id = "set_${exerciseId}_$setIndex",
                exerciseId = exerciseId,
                sessionId = sessionId,
                order = setIndex - 1,
                weight = 60f + (setIndex * 5f), // 递增重量
                reps = 10 - setIndex, // 递减次数
                isCompleted = true,
                createdAt = System.currentTimeMillis() - (setIndex * 600000), // 间隔10分钟
                updatedAt = System.currentTimeMillis(),
            )
        }

        return SessionExercise(
            id = "session_ex_$exerciseId",
            sessionId = sessionId,
            exerciseId = exerciseId,
            order = order,
            sets = sets,
            notes = "数据库测试备注：$name",
            name = name,
            targetSets = 3,
            completedSets = 3,
            status = "COMPLETED",
            isCompleted = true,
            restSeconds = 90,
            createdAt = System.currentTimeMillis() - 3600000,
            updatedAt = System.currentTimeMillis(),
        )
    }

    private fun createTestExercise(id: String, name: String): Exercise {
        return Exercise(
            id = id,
            name = UiText.DynamicString(name),
            muscleGroup = MuscleGroup.CHEST,
            equipment = emptyList(),
            description = UiText.DynamicString("数据库测试动作：$name"),
            defaultSets = 3,
            defaultReps = 10,
            defaultWeight = 60f,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
        )
    }
}