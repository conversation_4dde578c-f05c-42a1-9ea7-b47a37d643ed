package com.example.gymbro.features.workout.template.edit.internal.components

import androidx.compose.animation.*
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.FitnessCenter
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.GymBroToast
import com.example.gymbro.designSystem.components.ToastSeverity
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroComponentPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import kotlinx.coroutines.delay
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 通用UI组件集合 - TemplateEdit模块专用
 *
 * 🎯 职责：
 * - 可复用的UI组件
 * - 集成designSystem标准组件
 * - 提供一致的UI体验
 * - 支持主题和动画
 *
 * 📋 遵循标准：
 * - 使用designSystem tokens
 * - 支持MaterialTheme
 * - 遵循Compose最佳实践
 * - 无状态组件设计
 */

// === 可编辑输入组件 ===

/**
 * 可编辑顶部栏字段组件 - 高性能优化版本
 * 按照Box+LazyColumn+Surface.md文档优化，解决重组风暴问题
 */
@Stable
@Composable
fun EditableTopBarField(
    value: String,
    onValueChange: (String) -> Unit,
    placeholder: String,
    isTitle: Boolean = false,
    modifier: Modifier = Modifier,
) {
    // 🔥 性能优化：使用本地状态管理编辑过程，减少上层重组
    var isEditing by remember { mutableStateOf(false) }
    var localText by remember(value) { mutableStateOf(value) }

    // 🔥 修复：确保 localText 与 value 保持同步
    LaunchedEffect(value) {
        if (!isEditing) {
            localText = value
            println("🔧 [DEBUG] 同步 localText = '$value' (isEditing: $isEditing)")
        }
    }

    // 🔥 额外修复：当 value 变化时，如果不在编辑状态，强制更新 localText
    LaunchedEffect(value, isEditing) {
        if (!isEditing && localText != value) {
            println("🔧 [DEBUG] 强制同步 localText: '$localText' -> '$value'")
            localText = value
        }
    }

    // 🔥 修复：添加焦点请求器，确保编辑时能正确获得焦点
    val focusRequester = remember { FocusRequester() }

    // 🔥 性能优化：缓存稳定的回调函数
    val stableOnEditComplete =
        remember(onValueChange) {
            {
                    finalText: String ->
                println(
                    "🔧 [DEBUG] EditableTopBarField.stableOnEditComplete: finalText='$finalText', isTitle=$isTitle",
                )
                // 🔧 修复：始终调用onValueChange，让ViewModel处理是否需要更新
                onValueChange(finalText)
                isEditing = false
                println("🔧 [DEBUG] EditableTopBarField.stableOnEditComplete: onValueChange 调用完成")
            }
        }

    // 🔥 性能优化：缓存文本样式，避免重复创建
    val textStyle =
        if (isTitle) {
            MaterialTheme.typography.titleLarge
        } else {
            MaterialTheme.typography.bodyMedium
        }

    // 🔥 修复：进入编辑模式时自动请求焦点 - 添加延迟确保组件已渲染
    LaunchedEffect(isEditing) {
        println("🔧 [DEBUG] LaunchedEffect triggered, isEditing: $isEditing")
        if (isEditing) {
            println("🔧 [DEBUG] 延迟请求焦点...")
            // 🔥 关键修复：添加短暂延迟，确保BasicTextField已完全渲染
            delay(50)
            try {
                focusRequester.requestFocus()
                println("🔧 [DEBUG] 焦点请求完成")
            } catch (e: Exception) {
                println("🔧 [DEBUG] 焦点请求失败: ${e.message}")
            }
        }
    }

    // 🔥 修复：保持 BasicTextField 用于文本编辑，数字输入使用 KeypadInputField
    if (isEditing) {
        // 编辑模式：对于文本内容，继续使用 BasicTextField
        // 数字输入已在 WorkoutExerciseComponent 中使用 KeypadInputField
        BasicTextField(
            value = localText,
            onValueChange = { localText = it },
            textStyle =
            textStyle.copy(
                color = MaterialTheme.workoutColors.accentSecondary,
            ),
            modifier =
            modifier
                .focusRequester(focusRequester) // 🔥 添加焦点请求器
                .background(
                    color = Color.White, // 🔥 统一使用纯白背景
                    shape = RoundedCornerShape(Tokens.Radius.XSmall),
                ).padding(
                    horizontal = Tokens.Spacing.Tiny,
                    vertical = Tokens.Spacing.Tiny, // 🔥 Phase 0: 使用 Tokens 替代硬编码
                ).onFocusChanged { focusState ->
                    if (!focusState.isFocused) {
                        stableOnEditComplete(localText)
                    }
                },
            keyboardOptions =
            KeyboardOptions(
                imeAction = ImeAction.Done,
            ),
            keyboardActions =
            KeyboardActions(
                onDone = { stableOnEditComplete(localText) },
            ),
            singleLine = isTitle,
            maxLines = if (isTitle) 1 else 3,
        )
    } else {
        // 显示模式：优化的文本显示
        val displayText = value.ifBlank { placeholder }

        val displayColor =
            if (value.isBlank()) {
                MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.6f)
            } else {
                MaterialTheme.workoutColors.accentSecondary
            }

        Text(
            text = displayText,
            style =
            if (isTitle) {
                textStyle.copy(fontWeight = FontWeight.Medium)
            } else {
                textStyle
            },
            color = displayColor,
            modifier =
            modifier
                .fillMaxWidth() // 🔥 确保点击区域覆盖整个宽度
                .padding(
                    horizontal = Tokens.Spacing.Tiny,
                    vertical = Tokens.Spacing.Small, // 🔥 紧凑垂直间距
                ).clickable(
                    // 🔥 修复：禁用涟漪效果，避免与LazyColumn滚动冲突
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() },
                ) {
                    println("🔧 [DEBUG] 内联编辑被点击: $displayText (isTitle: $isTitle)")
                    println(
                        "🔧 [DEBUG] 当前 isEditing: $isEditing, value: '$value', localText: '$localText'",
                    )
                    localText = value // 🔥 确保本地文本与当前值同步
                    isEditing = true // 🔥 进入编辑模式
                    println("🔧 [DEBUG] 设置 isEditing = true, localText = '$localText'")
                }.background(
                    color = Color.Transparent, // 🔥 添加透明背景确保点击区域可见
                    shape = RoundedCornerShape(Tokens.Radius.XSmall),
                ),
            maxLines = if (isTitle) 1 else 3,
            overflow = TextOverflow.Ellipsis,
        )
    }
}

// === 状态显示组件 ===

/**
 * 状态标签组件
 * 基于designSystem tokens，提供一致的状态显示
 */
@Composable
fun StatusChip(
    text: String,
    color: Color,
    backgroundColor: Color,
    modifier: Modifier = Modifier,
) {
    Text(
        text = text,
        style = MaterialTheme.typography.bodySmall,
        color = color,
        modifier =
        modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(Tokens.Radius.XSmall),
            ).padding(horizontal = Tokens.Spacing.Small, vertical = Tokens.Spacing.Tiny),
    )
}

// === 加载状态组件 ===

/**
 * 加载内容组件
 * 使用designSystem的进度指示器和排版标准
 */
@Composable
fun LoadingContent(
    modifier: Modifier = Modifier,
    message: String = "加载模板中...",
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            CircularProgressIndicator(
                color = MaterialTheme.workoutColors.accentPrimary,
                modifier = Modifier.size(Tokens.Icon.Large),
            )
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.workoutColors.accentSecondary,
            )
        }
    }
}

// === 空状态组件 ===

/**
 * 空动作列表卡片组件
 * 提供友好的空状态引导，集成designSystem卡片规范
 */
@Composable
fun EmptyExercisesCard(
    onAddExercise: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors =
        CardDefaults.cardColors(
            containerColor = MaterialTheme.workoutColors.cardBackground,
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.Small),
    ) {
        Column(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.XLarge),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            Icon(
                imageVector = Icons.Default.FitnessCenter,
                contentDescription = null,
                modifier = Modifier.size(Tokens.Icon.XLarge),
                tint = MaterialTheme.workoutColors.accentSecondary,
            )

            Text(
                text = "还没有添加训练动作",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.workoutColors.accentSecondary,
                textAlign = TextAlign.Center,
            )

            Text(
                text = "点击下方的 + 按钮开始添加动作",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.7f),
                textAlign = TextAlign.Center,
            )

            // 🔥 性能优化：直接使用回调，避免错误的remember使用
            Button(
                onClick = onAddExercise,
                colors =
                ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.workoutColors.accentPrimary,
                ),
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null,
                    modifier = Modifier.size(Tokens.Icon.Small), // 🔥 Phase 0: 使用 Tokens 替代硬编码
                )
                Spacer(modifier = Modifier.width(Tokens.Spacing.Small)) // 🔥 Phase 0: 使用 Tokens 替代硬编码
                Text("添加动作")
            }
        }
    }
}

// === 错误处理组件 ===

// 🔥 移除重复的 ErrorSnackbar 定义，使用下方更完善的版本

// === 新的顶部信息组件 ===

/**
 * P4: 模板信息头部组件 - 增强Summary UI显示
 *
 * P4 新增功能：
 * 1. 完整的模板基本信息显示：名称、动作数量、预计时长、创建/更新时间
 * 2. 模板状态显示：草稿/已发布状态、版本信息
 * 3. 使用设计系统Token，支持深色/浅色模式
 * 4. 数据一致性保障
 */
@Composable
fun TemplateInfoHeader(
    templateName: String,
    templateDescription: String,
    totalWeight: Float,
    workoutSummary: String,
    onEditName: () -> Unit,
    onEditDescription: () -> Unit,
    modifier: Modifier = Modifier,
    // P4: 新增Summary UI参数
    exerciseCount: Int = 0,
    estimatedDuration: String = "",
    createdAt: Long = 0L,
    updatedAt: Long = 0L,
    isDraft: Boolean = true,
    isPublished: Boolean = false,
    currentVersion: Int = 1,
    templateId: String = "",
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = MaterialTheme.workoutColors.aiCoachBackground,
        shape = RoundedCornerShape(Tokens.Radius.Medium),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // P4: 第一行：模板名称（左）+ 状态标签（右）
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // 左侧：可点击的模板名称
                Text(
                    text = templateName.ifEmpty { "点击编辑模板名称" },
                    style = MaterialTheme.typography.titleLarge,
                    color = if (templateName.isEmpty()) {
                        MaterialTheme.workoutColors.textSecondary
                    } else {
                        MaterialTheme.workoutColors.textPrimary
                    },
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .weight(1f)
                        .clickable {
                            Timber.d("🔧 [DEBUG-CLICK] TemplateInfoHeader 模板名称被点击")
                            onEditName()
                        }
                        .padding(vertical = Tokens.Spacing.Small),
                )

                // P4: 右侧：状态标签
                TemplateStatusChip(
                    isDraft = isDraft,
                    isPublished = isPublished,
                    currentVersion = currentVersion,
                )

                // 右侧：总重量显示
                Text(
                    text = "${"%.1f".format(totalWeight)} kg",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.workoutColors.accentPrimary,
                    fontWeight = FontWeight.Bold,
                )
            }

            // P4: 第二行：可点击的模板描述
            Text(
                text = templateDescription.ifEmpty { "点击编辑模板描述" },
                style = MaterialTheme.typography.bodyLarge,
                color = if (templateDescription.isEmpty()) {
                    MaterialTheme.workoutColors.textSecondary
                } else {
                    MaterialTheme.workoutColors.textPrimary
                },
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable {
                        Timber.d("🔧 [DEBUG-CLICK] TemplateInfoHeader 模板描述被点击")
                        onEditDescription()
                    }
                    .padding(vertical = Tokens.Spacing.Small),
            )

            // P4: 第三行：模板统计信息
            TemplateSummaryRow(
                exerciseCount = exerciseCount,
                estimatedDuration = estimatedDuration,
                totalWeight = totalWeight,
                workoutSummary = workoutSummary,
            )

            // P4: 第四行：时间信息
            TemplateTimeInfo(
                createdAt = createdAt,
                updatedAt = updatedAt,
                templateId = templateId,
            )
        }
    }
}

// === P4: 新增Summary UI组件 ===

/**
 * P4: 状态信息数据类
 */
data class StatusInfo(
    val text: String,
    val color: Color,
    val backgroundColor: Color,
)

/**
 * P4: 模板状态标签组件
 * 显示草稿/已发布状态和版本信息
 */
@Composable
fun TemplateStatusChip(
    isDraft: Boolean,
    isPublished: Boolean,
    currentVersion: Int,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 状态标签
        val statusInfo = when {
            isPublished -> StatusInfo(
                text = "已发布",
                color = MaterialTheme.workoutColors.textPrimary,
                backgroundColor = MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.1f),
            )
            isDraft -> StatusInfo(
                text = "草稿",
                color = MaterialTheme.workoutColors.textSecondary,
                backgroundColor = MaterialTheme.workoutColors.cardBackground,
            )
            else -> StatusInfo(
                text = "未保存",
                color = MaterialTheme.workoutColors.textSecondary,
                backgroundColor = MaterialTheme.workoutColors.cardBackground.copy(alpha = 0.5f),
            )
        }

        StatusChip(
            text = statusInfo.text,
            color = statusInfo.color,
            backgroundColor = statusInfo.backgroundColor,
        )

        // 版本信息
        if (currentVersion > 0) {
            StatusChip(
                text = "v$currentVersion",
                color = MaterialTheme.workoutColors.textSecondary,
                backgroundColor = MaterialTheme.workoutColors.cardBackground,
            )
        }
    }
}

/**
 * P4: 模板统计信息行组件
 * 显示动作数量、预计时长、总重量等统计信息
 */
@Composable
fun TemplateSummaryRow(
    exerciseCount: Int,
    estimatedDuration: String,
    totalWeight: Float,
    workoutSummary: String,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 左侧：统计信息
        Row(
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 动作数量
            if (exerciseCount > 0) {
                TemplateSummaryItem(
                    label = "动作",
                    value = "$exerciseCount",
                    icon = "🏋️",
                )
            }

            // 预计时长
            if (estimatedDuration.isNotEmpty()) {
                TemplateSummaryItem(
                    label = "时长",
                    value = estimatedDuration,
                    icon = "⏱️",
                )
            }

            // 总重量
            if (totalWeight > 0) {
                TemplateSummaryItem(
                    label = "总重量",
                    value = "${"%.1f".format(totalWeight)}kg",
                    icon = "⚖️",
                )
            }
        }

        // 右侧：训练内容摘要
        if (workoutSummary.isNotEmpty()) {
            Text(
                text = workoutSummary,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.workoutColors.textSecondary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f, fill = false),
            )
        }
    }
}

/**
 * P4: 模板统计项组件
 */
@Composable
fun TemplateSummaryItem(
    label: String,
    value: String,
    icon: String,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Tiny),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = icon,
            style = MaterialTheme.typography.bodySmall,
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.workoutColors.textPrimary,
            fontWeight = FontWeight.Medium,
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.workoutColors.textSecondary,
        )
    }
}

/**
 * P4: 模板时间信息组件
 * 显示创建时间、更新时间等
 */
@Composable
fun TemplateTimeInfo(
    createdAt: Long,
    updatedAt: Long,
    templateId: String,
    modifier: Modifier = Modifier,
) {
    if (createdAt > 0 || updatedAt > 0 || templateId.isNotEmpty()) {
        Row(
            modifier = modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 左侧：时间信息
            Column(
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Tiny),
            ) {
                if (createdAt > 0) {
                    Text(
                        text = "创建：${formatTimestamp(createdAt)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.workoutColors.textSecondary,
                    )
                }
                if (updatedAt > 0 && updatedAt != createdAt) {
                    Text(
                        text = "更新：${formatTimestamp(updatedAt)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.workoutColors.textSecondary,
                    )
                }
            }

            // 右侧：模板状态显示（简化生产信息）
            if (templateId.isNotEmpty() && !templateId.startsWith("temp_")) {
                Text(
                    text = "已保存",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.8f),
                )
            }
        }
    }
}

/**
 * P4: 时间戳格式化工具函数
 */
private fun formatTimestamp(timestamp: Long): String {
    return try {
        val date = Date(timestamp)
        val format = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
        format.format(date)
    } catch (e: Exception) {
        "未知时间"
    }
}

// === Preview 函数 ===

/**
 * TemplateInfoHeader 预览 - 草稿状态
 */
@GymBroPreview
@Composable
private fun TemplateInfoHeaderDraftPreview() {
    GymBroTheme {
        TemplateInfoHeader(
            templateName = "胸部训练模板",
            templateDescription = "专注于胸大肌和三角肌前束的综合训练",
            totalWeight = 1250.5f,
            workoutSummary = "4个动作 · 12组",
            exerciseCount = 4,
            estimatedDuration = "45分钟",
            createdAt = System.currentTimeMillis() - 86400000, // 1天前
            updatedAt = System.currentTimeMillis() - 3600000, // 1小时前
            isDraft = true,
            isPublished = false,
            currentVersion = 1,
            templateId = "template_123",
            onEditName = { },
            onEditDescription = { },
        )
    }
}

/**
 * TemplateInfoHeader 预览 - 已发布状态
 */
@GymBroPreview
@Composable
private fun TemplateInfoHeaderPublishedPreview() {
    GymBroTheme {
        TemplateInfoHeader(
            templateName = "背部力量训练",
            templateDescription = "全面的背部肌群训练，包含引体向上、划船等经典动作",
            totalWeight = 2100.0f,
            workoutSummary = "6个动作 · 18组",
            exerciseCount = 6,
            estimatedDuration = "1小时15分钟",
            createdAt = System.currentTimeMillis() - 604800000, // 7天前
            updatedAt = System.currentTimeMillis() - 86400000, // 1天前
            isDraft = false,
            isPublished = true,
            currentVersion = 3,
            templateId = "template_456",
            onEditName = { },
            onEditDescription = { },
        )
    }
}

/**
 * TemplateSummaryRow 预览
 */
@GymBroComponentPreview
@Composable
private fun TemplateSummaryRowPreview() {
    GymBroTheme {
        Surface {
            TemplateSummaryRow(
                exerciseCount = 5,
                estimatedDuration = "50分钟",
                totalWeight = 1800.5f,
                workoutSummary = "5个动作 · 15组",
            )
        }
    }
}

/**
 * TemplateTimeInfo 预览
 */
@GymBroComponentPreview
@Composable
private fun TemplateTimeInfoPreview() {
    GymBroTheme {
        Surface {
            TemplateTimeInfo(
                createdAt = System.currentTimeMillis() - 259200000, // 3天前
                updatedAt = System.currentTimeMillis() - 7200000, // 2小时前
                templateId = "template_789",
            )
        }
    }
}

/**
 * EmptyExercisesCard 预览
 */
@GymBroComponentPreview
@Composable
private fun EmptyExercisesCardPreview() {
    GymBroTheme {
        EmptyExercisesCard(
            onAddExercise = { },
        )
    }
}

// === 编辑内容组件 ===

/**
 * 编辑内容主体组件 - 使用 DraggableExerciseCard 集成 WorkoutExerciseComponent
 */
@Composable
fun EditContent(
    uiState: TemplateEditContract.State,
    onIntent: (TemplateEditContract.Intent) -> Unit,
    modifier: Modifier = Modifier,
) {
    // 🔥 添加渲染监控：确保组件能正常显示
    LaunchedEffect(uiState.exercises.size) {
        Timber.d("🎯 [RENDER-DEBUG] EditContent 开始渲染: 动作数量=${uiState.exercises.size}")
        uiState.exercises.forEach { exercise ->
            Timber.d(
                "🎯 [RENDER-DEBUG] EditContent 动作列表: ${exercise.exerciseName}, customSets=${exercise.customSets.size}",
            )
        }
    }

    Box(modifier = modifier.fillMaxSize()) {
        // 🔥 修复：在模板编辑器中显示 WorkoutExerciseComponent 时，默认展开模式
        when {
            uiState.isLoading -> {
                LoadingContent(
                    message = "加载模板中...",
                )
            }
            uiState.exercises.isEmpty() -> {
                // 空状态显示
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(Tokens.Spacing.Large),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                ) {
                    EmptyExercisesCard(
                        onAddExercise = {
                            Timber.d("🎯 [RENDER-DEBUG] EmptyExercisesCard 添加动作按钮被点击")
                            onIntent(TemplateEditContract.Intent.ShowExerciseSelector)
                        },
                    )
                }
            }
            else -> {
                // 🔥 修复：直接渲染动作列表，移除不必要的嵌套
                TemplateEditor(
                    uiState = uiState,
                    onIntent = onIntent,
                    modifier = Modifier.fillMaxSize(),
                )
            }
        }
    }
}

/**
 * 模板编辑器 - 集成统一拖拽系统
 * 使用新的DragModifiers和DragAnimations，提供Material3标准的拖拽体验
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun TemplateEditor(
    uiState: TemplateEditContract.State,
    onIntent: (TemplateEditContract.Intent) -> Unit,
    modifier: Modifier = Modifier,
) {
    // 🔥 简化：直接渲染动作列表，移除不必要的拖拽状态管理
    val stableOnShowExerciseSelector = remember(onIntent) {
        {
            onIntent(TemplateEditContract.Intent.ShowExerciseSelector)
        }
    }

    Surface(
        modifier = modifier.fillMaxSize(),
        color = MaterialTheme.workoutColors.cardBackground,
    ) {
        LazyColumn(
            contentPadding = PaddingValues(
                horizontal = Tokens.Spacing.Medium,
                vertical = Tokens.Spacing.Small,
            ),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            // 动作列表标题
            item(key = "exercises_header") {
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = MaterialTheme.workoutColors.aiCoachBackground,
                    shape = RoundedCornerShape(Tokens.Radius.Medium),
                ) {
                    Row(
                        modifier = Modifier.padding(Tokens.Spacing.Medium),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = "训练动作",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.workoutColors.textPrimary,
                        )
                        // 显示动作数量和拖拽状态
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small)
                        ) {
                            // 拖拽状态指示器
                            if (uiState.isDragInProgress) {
                                Icon(
                                    imageVector = Icons.Default.FitnessCenter,
                                    contentDescription = "拖拽中",
                                    tint = MaterialTheme.workoutColors.accentPrimary,
                                    modifier = Modifier.size(Tokens.Icon.Small)
                                )
                                Text(
                                    text = "拖拽排序中",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.workoutColors.accentPrimary,
                                )
                            } else {
                                val exerciseCountText = "${uiState.exercises.size}/${TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE} 个动作"
                                val isNearLimit = uiState.exercises.size >= TemplateEditContract.Constants.MAX_EXERCISES_PER_TEMPLATE * 0.8f

                                Text(
                                    text = exerciseCountText,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = if (isNearLimit) {
                                        MaterialTheme.workoutColors.accentPrimary
                                    } else {
                                        MaterialTheme.workoutColors.textSecondary
                                    },
                                )
                            }
                        }
                    }
                }
            }

            // 动作列表或空状态
            if (uiState.exercises.isEmpty()) {
                item(key = "empty_exercises") {
                    Timber.d("🎯 [RENDER-DEBUG] TemplateEditor 渲染空状态卡片")
                    EmptyExercisesCard(
                        onAddExercise = stableOnShowExerciseSelector,
                        modifier = Modifier.padding(Tokens.Spacing.Medium),
                    )
                }
            } else {
                // 🔥 增强拖拽动作列表 - 使用新的拖拽修饰符
                itemsIndexed(
                    items = uiState.exercises,
                    key = { _, exercise -> "exercise_${exercise.id}" },
                ) { index, exercise ->
                    // 单个动作渲染监控
                    LaunchedEffect(exercise.id) {
                        Timber.d(
                            "🎯 [RENDER-DEBUG] TemplateEditor 渲染动作[$index]: ${exercise.exerciseName}, id=${exercise.id}",
                        )
                    }

                    // 🔥 简化的动作卡片渲染 - 拖拽功能由WorkoutExerciseComponent内部处理
                    TemplateExerciseCard(
                        exercise = exercise,
                        onExerciseUpdate = { updatedExercise ->
                            Timber.d(
                                "🎯 [RENDER-DEBUG] TemplateEditor 收到动作更新: ${updatedExercise.exerciseName}",
                            )
                            onIntent(TemplateEditContract.Intent.UpdateExercise(updatedExercise))
                        },
                        onDeleteExercise = { exerciseId ->
                            Timber.d("🎯 [RENDER-DEBUG] TemplateEditor 删除动作: $exerciseId")
                            onIntent(TemplateEditContract.Intent.RemoveExercise(exerciseId))
                        },
                        modifier = Modifier
                            .animateItem() // LazyColumn的内置动画
                            .fillMaxWidth()
                    )
                }
            }
        }
    }
}

// === 已移除的自动保存指示器组件 ===
// 🔥 移除：AutoSave相关组件已移至左下角小窗口显示，使用GymBroToast替代

/**
 * 错误提示组件
 */
@Composable
fun ErrorSnackbar(
    message: String,
    visible: Boolean,
    modifier: Modifier = Modifier,
    onDismiss: () -> Unit = {},
) {
    if (visible) {
        GymBroToast(
            message = UiText.DynamicString(message),
            severity = ToastSeverity.ERROR,
            onDismiss = onDismiss,
            modifier = modifier,
            autoDismissDelay = 3000L,
        )
    }
}
