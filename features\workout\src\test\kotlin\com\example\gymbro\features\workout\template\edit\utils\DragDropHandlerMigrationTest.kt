package com.example.gymbro.features.workout.template.edit.utils

import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.features.workout.template.edit.utils.DragDropHandler
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import org.junit.Test
import org.junit.Assert.*

/**
 * DragDropHandler迁移兼容性测试
 *
 * 验证迁移后的DragDropHandler与原有接口的兼容性
 */
class DragDropHandlerMigrationTest {

    @Test
    fun `canStartDrag should work with existing signature`() {
        val state = TemplateEditContract.State(
            exercises = listOf(
                TemplateExerciseDto(
                    id = "exercise1",
                    name = "Test Exercise",
                    sets = 3,
                    reps = 12,
                    targetWeight = 50f,
                    restTimeSeconds = 60,
                    customSets = emptyList()
                )
            ),
            reorderingEnabled = true,
            isDragInProgress = false
        )

        val canStart = DragDropHandler.canStartDrag(state, "exercise1")
        assertTrue("Should be able to start drag with valid exercise", canStart)

        val cannotStart = DragDropHandler.canStartDrag(state, "invalid_id")
        assertFalse("Should not be able to start drag with invalid exercise", cannotStart)
    }

    @Test
    fun `handleDragStart should return updated state`() {
        val state = TemplateEditContract.State(
            exercises = listOf(
                TemplateExerciseDto(
                    id = "exercise1",
                    name = "Test Exercise",
                    sets = 3,
                    reps = 12,
                    targetWeight = 50f,
                    restTimeSeconds = 60,
                    customSets = emptyList()
                )
            ),
            reorderingEnabled = true,
            isDragInProgress = false
        )

        val newState = DragDropHandler.handleDragStart(state, "exercise1", 0)

        assertTrue("Should be in drag progress", newState.isDragInProgress)
        assertEquals("Should have correct dragged exercise ID", "exercise1", newState.draggedExerciseId)
        assertEquals("Should have correct drag start index", 0, newState.draggedItemIndex)
        assertTrue("Should mark as having unsaved changes", newState.hasUnsavedChanges)
    }

    @Test
    fun `handleDragUpdate should update target index`() {
        val state = TemplateEditContract.State(
            exercises = listOf(
                TemplateExerciseDto(id = "ex1", name = "Ex1", sets = 3, reps = 12, targetWeight = 50f, restTimeSeconds = 60, customSets = emptyList()),
                TemplateExerciseDto(id = "ex2", name = "Ex2", sets = 3, reps = 12, targetWeight = 50f, restTimeSeconds = 60, customSets = emptyList())
            ),
            isDragInProgress = true,
            draggedExerciseId = "ex1",
            draggedItemIndex = 0,
            dragTargetIndex = 0
        )

        val newState = DragDropHandler.handleDragUpdate(state, 1, 100f)

        assertEquals("Should update target index", 1, newState.dragTargetIndex)
        assertEquals("Should update drag offset", 100f, newState.dragOffset, 0.1f)
    }

    @Test
    fun `handleDragComplete should reorder exercises`() {
        val exercises = listOf(
            TemplateExerciseDto(id = "ex1", name = "Ex1", sets = 3, reps = 12, targetWeight = 50f, restTimeSeconds = 60, customSets = emptyList()),
            TemplateExerciseDto(id = "ex2", name = "Ex2", sets = 3, reps = 12, targetWeight = 50f, restTimeSeconds = 60, customSets = emptyList())
        )

        val state = TemplateEditContract.State(
            exercises = exercises,
            isDragInProgress = true,
            draggedExerciseId = "ex1",
            draggedItemIndex = 0,
            dragTargetIndex = 1
        )

        val newState = DragDropHandler.handleDragComplete(state, 0, 1)

        assertFalse("Should end drag progress", newState.isDragInProgress)
        assertEquals("Should reorder exercises", "ex2", newState.exercises[0].id)
        assertEquals("Should reorder exercises", "ex1", newState.exercises[1].id)
        assertTrue("Should mark as having unsaved changes", newState.hasUnsavedChanges)
    }

    @Test
    fun `handleDragCancel should reset drag state`() {
        val state = TemplateEditContract.State(
            isDragInProgress = true,
            draggedExerciseId = "ex1",
            draggedItemIndex = 0,
            dragTargetIndex = 1,
            dragOffset = 100f
        )

        val newState = DragDropHandler.handleDragCancel(state)

        assertFalse("Should end drag progress", newState.isDragInProgress)
        assertNull("Should clear dragged exercise ID", newState.draggedExerciseId)
        assertEquals("Should reset drag index", -1, newState.draggedItemIndex)
        assertEquals("Should reset target index", -1, newState.dragTargetIndex)
        assertEquals("Should reset drag offset", 0f, newState.dragOffset, 0.1f)
    }

    @Test
    fun `getDragStateSummary should return meaningful info`() {
        val dragState = TemplateEditContract.State(
            isDragInProgress = true,
            draggedExerciseId = "ex1",
            draggedItemIndex = 0,
            dragTargetIndex = 1
        )

        val summary = DragDropHandler.getDragStateSummary(dragState)
        assertTrue("Should contain drag info", summary.contains("ex1"))
        assertTrue("Should contain indices", summary.contains("0") && summary.contains("1"))

        val noDragState = TemplateEditContract.State(isDragInProgress = false)
        val noSummary = DragDropHandler.getDragStateSummary(noDragState)
        assertTrue("Should indicate no drag", noSummary.contains("未拖拽") || noSummary.contains("not"))
    }

    @Test
    fun `getEnhancedDragInfo should return detailed information`() {
        val state = TemplateEditContract.State(
            isDragInProgress = true,
            draggedExerciseId = "ex1",
            draggedItemIndex = 0,
            dragTargetIndex = 1,
            reorderingEnabled = true
        )

        val info = DragDropHandler.getEnhancedDragInfo(state)

        assertTrue("Should contain isDragInProgress", info["isDragInProgress"] as Boolean)
        assertEquals("Should contain draggedItemId", "ex1", info["draggedItemId"])
        assertEquals("Should contain indices", 0, info["draggedItemIndex"])
        assertEquals("Should contain target index", 1, info["dragTargetIndex"])
        assertTrue("Should be drag enabled", info["isDragEnabled"] as Boolean)
    }
}
