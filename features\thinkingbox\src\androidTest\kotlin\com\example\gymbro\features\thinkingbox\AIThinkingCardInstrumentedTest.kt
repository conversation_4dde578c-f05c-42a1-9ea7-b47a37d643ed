package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.test.assertHeightIsAtMost
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * AIThinkingCard仪器化集成测试
 *
 * 🎯 测试目标：验证四条铁律的技术实现
 * 📊 测试要求：仪器化测试，真实UI渲染验证
 * 🔧 测试框架：Compose UI Test + JUnit 4 + kotlin.test
 * 🔥 重点：四条铁律验证 + LazyColumn架构验证 + 自动滚动验证
 */
class AIThinkingCardInstrumentedTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    companion object {
        // 测试标签
        private const val AI_THINKING_CARD_TAG = "ai_thinking_card"
        private const val LAZY_COLUMN_TAG = "thinking_lazy_column"
        private const val THINKING_HEADER_TAG = "thinking_header"

        // 测试数据
        private fun createTestSegmentUi(
            id: String,
            kind: SegmentKind = SegmentKind.PERTHINK,
            title: String? = "测试标题",
            content: String = "测试内容",
            isComplete: Boolean = true,
            isRendered: Boolean = false,
        ) = ThinkingBoxContract.SegmentUi(
            id = id,
            kind = kind,
            title = title,
            content = content,
            isComplete = isComplete,
            isRendered = isRendered,
        )

        private fun createEmptyState() = ThinkingBoxContract.State()

        private fun createStateWithSegments(segments: List<ThinkingBoxContract.SegmentUi>) =
            ThinkingBoxContract.State(
                messageId = "test-message",
                segmentsQueue = segments,
                thinkingClosed = false,
            )
    }

    /**
     * 🔥 【四条铁律第1条】UI绝对不重组刷新 - LazyColumn单一画布架构验证
     */
    @Test
    fun testIronLaw1_NoUIRecomposition_LazyColumnArchitecture() {
        // Given
        var state by mutableStateOf(createEmptyState())
        var recompositionCount = 0

        composeTestRule.setContent {
            GymBroTheme {
                Column(modifier = Modifier.fillMaxSize()) {
                    recompositionCount++

                    if (shouldShowAIThinkingCard(state)) {
                        AIThinkingCard(
                            state = state,
                            messageId = "test-message",
                            modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                            onSegmentRendered = { },
                        )
                    }
                }
            }
        }

        val initialRecompositionCount = recompositionCount

        // When - 添加多个segments，模拟实际使用场景
        composeTestRule.runOnIdle {
            state = createStateWithSegments(
                listOf(
                    createTestSegmentUi("segment1", content = "第一段内容"),
                ),
            )
        }

        composeTestRule.waitForIdle()

        composeTestRule.runOnIdle {
            state = createStateWithSegments(
                listOf(
                    createTestSegmentUi("segment1", content = "第一段内容"),
                    createTestSegmentUi("segment2", content = "第二段内容"),
                ),
            )
        }

        composeTestRule.waitForIdle()

        // Then - 验证LazyColumn架构：增量绘制，不触发全局重组
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsDisplayed()

        // 🔥 【核心验证】重组次数应该保持最小（LazyColumn增量绘制特性）
        val finalRecompositionCount = recompositionCount
        assertTrue(
            finalRecompositionCount - initialRecompositionCount <= 3,
            "重组次数应该最小化，实际: ${finalRecompositionCount - initialRecompositionCount}",
        )
    }

    /**
     * 🔥 【四条铁律第3条】思考框硬上限1/3屏高验证
     */
    @Test
    fun testIronLaw3_HeightLimit_OneThirdScreenHeight() {
        // Given
        val testSegments = listOf(
            createTestSegmentUi("segment1", content = "很长的内容".repeat(100)),
            createTestSegmentUi("segment2", content = "很长的内容".repeat(100)),
            createTestSegmentUi("segment3", content = "很长的内容".repeat(100)),
        )
        val state = createStateWithSegments(testSegments)

        composeTestRule.setContent {
            GymBroTheme {
                AIThinkingCard(
                    state = state,
                    messageId = "test-message",
                    modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                )
            }
        }

        composeTestRule.waitForIdle()

        // When & Then - 验证高度硬上限
        // 注意：这里使用一个合理的最大高度来验证限制是否生效
        // 实际的1/3屏高会根据设备而变化，这里使用一个上限来验证
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG)
            .assertIsDisplayed()
            .assertHeightIsAtMost(400.dp) // 验证有高度限制
    }

    /**
     * 🔥 【四条铁律】shouldShowAIThinkingCard逻辑验证
     */
    @Test
    fun testShouldShowAIThinkingCard_Logic() {
        // Test Case 1: 空状态，不流式 -> 不显示
        val emptyNonStreamingState = ThinkingBoxContract.State(
            segmentsQueue = emptyList(),
            thinkingClosed = true,
        )
        assertFalse(shouldShowAIThinkingCard(emptyNonStreamingState))

        // Test Case 2: 有segments -> 显示
        val stateWithSegments = ThinkingBoxContract.State(
            segmentsQueue = listOf(createTestSegmentUi("segment1")),
            thinkingClosed = true,
        )
        assertTrue(shouldShowAIThinkingCard(stateWithSegments))

        // Test Case 3: 空segments但在流式传输 -> 显示
        val emptyStreamingState = ThinkingBoxContract.State(
            segmentsQueue = emptyList(),
            thinkingClosed = false,
        )
        assertTrue(shouldShowAIThinkingCard(emptyStreamingState))
    }

    /**
     * 🔥 【LazyColumn架构】UI渲染状态验证
     */
    @Test
    fun testLazyColumnArchitecture_UIRendering() {
        // Given
        val segments = listOf(
            createTestSegmentUi("segment1", title = "第一阶段", content = "思考内容1"),
            createTestSegmentUi("segment2", title = "第二阶段", content = "思考内容2"),
        )
        val state = createStateWithSegments(segments)

        composeTestRule.setContent {
            GymBroTheme {
                AIThinkingCard(
                    state = state,
                    messageId = "test-message",
                    modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                )
            }
        }

        composeTestRule.waitForIdle()

        // When & Then - 验证LazyColumn正确渲染segments
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsDisplayed()
        composeTestRule.onNodeWithText("第一阶段").assertIsDisplayed()
        composeTestRule.onNodeWithText("第二阶段").assertIsDisplayed()
        composeTestRule.onNodeWithText("思考内容1").assertIsDisplayed()
        composeTestRule.onNodeWithText("思考内容2").assertIsDisplayed()
    }

    /**
     * 🔥 【等待状态】ThinkingHeader显示逻辑验证
     */
    @Test
    fun testWaitingState_ThinkingHeaderDisplay() {
        // Given - 无segments但正在流式传输的状态
        val waitingState = ThinkingBoxContract.State(
            segmentsQueue = emptyList(),
            thinkingClosed = false,
        )

        composeTestRule.setContent {
            GymBroTheme {
                Column(modifier = Modifier.fillMaxSize()) {
                    if (shouldShowAIThinkingCard(waitingState)) {
                        // 模拟AIThinkingCard的等待状态逻辑
                        when {
                            waitingState.segmentsQueue.isNotEmpty() -> {
                                // LazyColumn分支 - 不会执行
                            }
                            waitingState.isStreaming() && !waitingState.thinkingClosed -> {
                                ThinkingHeader(
                                    title = "Bro is thinking...",
                                    isStreaming = true,
                                    hasContent = false,
                                    modifier = Modifier.testTag(THINKING_HEADER_TAG),
                                )
                            }
                        }
                    }
                }
            }
        }

        composeTestRule.waitForIdle()

        // When & Then - 验证等待状态显示
        composeTestRule.onNodeWithTag(THINKING_HEADER_TAG).assertIsDisplayed()
        composeTestRule.onNodeWithText("Bro is thinking...").assertIsDisplayed()
    }

    /**
     * 🔥 【隐藏状态】无显示内容时的正确隐藏
     */
    @Test
    fun testHiddenState_NoDisplay() {
        // Given - 既无segments也非流式传输的状态
        val hiddenState = ThinkingBoxContract.State(
            segmentsQueue = emptyList(),
            thinkingClosed = true,
        )

        composeTestRule.setContent {
            GymBroTheme {
                Column(modifier = Modifier.fillMaxSize()) {
                    if (shouldShowAIThinkingCard(hiddenState)) {
                        AIThinkingCard(
                            state = hiddenState,
                            messageId = "test-message",
                            modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                        )
                    }
                }
            }
        }

        composeTestRule.waitForIdle()

        // When & Then - 验证隐藏状态
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsNotDisplayed()
    }

    /**
     * 🔥 【Segment渲染完成回调】onSegmentRendered回调机制验证
     */
    @Test
    fun testSegmentRenderedCallback_Mechanism() = runTest {
        // Given
        val segments = listOf(
            createTestSegmentUi("segment1", title = "测试", content = "内容"),
        )
        val state = createStateWithSegments(segments)
        var callbackSegmentId: String? = null

        composeTestRule.setContent {
            GymBroTheme {
                AIThinkingCard(
                    state = state,
                    messageId = "test-message",
                    onSegmentRendered = { segmentId ->
                        callbackSegmentId = segmentId
                    },
                )
            }
        }

        // When - 等待渲染完成
        composeTestRule.waitForIdle()

        // 模拟等待异步渲染回调
        delay(100)

        // Then - 验证回调被触发
        // 注意：实际的回调触发依赖于ThinkingStageCard的内部实现
        // 这里主要验证回调机制能正常工作，不会抛出异常
        composeTestRule.onNodeWithText("内容").assertIsDisplayed()
    }

    /**
     * 🔥 【状态变化响应】LaunchedEffect响应机制验证
     */
    @Test
    fun testStateChangeResponse_LaunchedEffectMechanism() {
        // Given
        var state by mutableStateOf(createEmptyState())

        composeTestRule.setContent {
            GymBroTheme {
                AIThinkingCard(
                    state = state,
                    messageId = "test-message",
                    modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                )
            }
        }

        // Initially hidden
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsNotDisplayed()

        // When - 状态变化：添加segments
        composeTestRule.runOnIdle {
            state = createStateWithSegments(
                listOf(
                    createTestSegmentUi("segment1", content = "新内容"),
                ),
            )
        }

        composeTestRule.waitForIdle()

        // Then - UI应该响应状态变化
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsDisplayed()
        composeTestRule.onNodeWithText("新内容").assertIsDisplayed()
    }

    /**
     * 🔥 【边界条件】极端情况处理验证
     */
    @Test
    fun testEdgeCases_ExtremeScenarios() {
        // Test Case 1: 大量segments
        val manySegments = (1..10).map { i ->
            createTestSegmentUi("segment$i", content = "内容$i")
        }
        val stateWithManySegments = createStateWithSegments(manySegments)

        composeTestRule.setContent {
            GymBroTheme {
                AIThinkingCard(
                    state = stateWithManySegments,
                    messageId = "test-message",
                    modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                )
            }
        }

        composeTestRule.waitForIdle()

        // 验证能正常处理大量segments
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsDisplayed()
        composeTestRule.onNodeWithText("内容1").assertIsDisplayed()

        // Test Case 2: 空内容segments
        val emptyContentSegments = listOf(
            createTestSegmentUi("empty1", content = ""),
            createTestSegmentUi("empty2", content = "   "), // 空白内容
        )
        val stateWithEmptyContent = createStateWithSegments(emptyContentSegments)

        composeTestRule.setContent {
            GymBroTheme {
                AIThinkingCard(
                    state = stateWithEmptyContent,
                    messageId = "test-message",
                    modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                )
            }
        }

        composeTestRule.waitForIdle()

        // 验证能正常处理空内容
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsDisplayed()
    }

    /**
     * 🔥 【性能验证】渲染性能基准测试
     */
    @Test
    fun testPerformance_RenderingBenchmark() {
        // Given
        val segments = (1..5).map { i ->
            createTestSegmentUi("segment$i", content = "内容".repeat(50))
        }
        val state = createStateWithSegments(segments)

        // When - 测量渲染时间
        val startTime = System.currentTimeMillis()

        composeTestRule.setContent {
            GymBroTheme {
                AIThinkingCard(
                    state = state,
                    messageId = "test-message",
                    modifier = Modifier.testTag(AI_THINKING_CARD_TAG),
                )
            }
        }

        composeTestRule.waitForIdle()

        val renderTime = System.currentTimeMillis() - startTime

        // Then - 验证渲染性能
        assertTrue(renderTime < 1000, "渲染时间应该小于1秒，实际: ${renderTime}ms")
        composeTestRule.onNodeWithTag(AI_THINKING_CARD_TAG).assertIsDisplayed()
    }
}