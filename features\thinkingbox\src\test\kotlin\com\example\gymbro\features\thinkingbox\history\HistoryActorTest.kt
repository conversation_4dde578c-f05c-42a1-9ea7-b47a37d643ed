package com.example.gymbro.features.thinkingbox.history

import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import io.mockk.*
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

/**
 * HistoryActor 单元测试
 *
 * 🎯 测试覆盖：
 * - Effect流监听和处理
 * - NotifyHistoryThinking处理
 * - NotifyHistoryFinal处理
 * - debounce机制验证
 * - 错误处理和重试逻辑
 */
@DisplayName("HistoryActor 单元测试")
class HistoryActorTest {

    private val mockRepository = mockk<HistoryRepository>(relaxed = true)
    private lateinit var historyActor: HistoryActor
    private val testScope = TestScope()

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        historyActor = HistoryActor(mockRepository)
    }

    @Nested
    @DisplayName("Effect处理测试")
    inner class EffectHandlingTests {

        @Test
        @DisplayName("NotifyHistoryThinking应该正确保存思考过程")
        fun `should handle NotifyHistoryThinking correctly`() = runTest {
            // Given
            val messageId = "test-message-123"
            val thinkingMarkdown = "# 思考过程\n这是思考内容"
            val effect = ThinkingBoxContract.Effect.NotifyHistoryThinking(
                messageId = messageId,
                thinkingMarkdown = thinkingMarkdown,
            )

            coEvery {
                mockRepository.saveThinkingProcess(messageId, thinkingMarkdown)
            } returns Result.success(Unit)

            val effectFlow = flowOf(effect)

            // When
            historyActor.initialize(effectFlow, testScope)
            testScheduler.advanceUntilIdle()

            // Then
            coVerify(exactly = 1) {
                mockRepository.saveThinkingProcess(messageId, thinkingMarkdown)
            }
        }

        @Test
        @DisplayName("NotifyHistoryFinal应该正确保存最终内容")
        fun `should handle NotifyHistoryFinal correctly`() = runTest {
            // Given
            val messageId = "test-message-456"
            val finalMarkdown = "# 最终答案\n这是最终内容"
            val effect = ThinkingBoxContract.Effect.NotifyHistoryFinal(
                messageId = messageId,
                finalMarkdown = finalMarkdown,
            )

            coEvery {
                mockRepository.saveFinalContent(messageId, finalMarkdown)
            } returns Result.success(Unit)

            coEvery {
                mockRepository.markConversationComplete(messageId)
            } returns Result.success(Unit)

            val effectFlow = flowOf(effect)

            // When
            historyActor.initialize(effectFlow, testScope)
            testScheduler.advanceUntilIdle()

            // Then
            coVerify(exactly = 1) {
                mockRepository.saveFinalContent(messageId, finalMarkdown)
            }
            coVerify(exactly = 1) {
                mockRepository.markConversationComplete(messageId)
            }
        }

        @Test
        @DisplayName("非History相关Effect应该被忽略")
        fun `should ignore non-history effects`() = runTest {
            // Given
            val scrollEffect = ThinkingBoxContract.Effect.ScrollToBottom
            val closeEffect = ThinkingBoxContract.Effect.CloseThinkingBox
            val effectFlow = flowOf(scrollEffect, closeEffect)

            // When
            historyActor.initialize(effectFlow, testScope)
            testScheduler.advanceUntilIdle()

            // Then
            coVerify(exactly = 0) { mockRepository.saveThinkingProcess(any(), any()) }
            coVerify(exactly = 0) { mockRepository.saveFinalContent(any(), any()) }
        }
    }

    @Nested
    @DisplayName("Debounce机制测试")
    inner class DebounceTests {

        @Test
        @DisplayName("debounce机制应该合并重复的思考写入请求")
        fun `should debounce duplicate thinking write requests`() = runTest {
            // Given
            val messageId = "debounce-test"
            val effect1 = ThinkingBoxContract.Effect.NotifyHistoryThinking(
                messageId = messageId,
                thinkingMarkdown = "第一次内容",
                debounceMs = 100L,
            )
            val effect2 = ThinkingBoxContract.Effect.NotifyHistoryThinking(
                messageId = messageId,
                thinkingMarkdown = "第二次内容",
                debounceMs = 100L,
            )

            coEvery {
                mockRepository.saveThinkingProcess(messageId, "第二次内容")
            } returns Result.success(Unit)

            val effectFlow = flowOf(effect1, effect2)

            // When
            historyActor.initialize(effectFlow, testScope)
            testScheduler.advanceTimeBy(150L) // 超过debounce时间

            // Then - 只应该保存最后一次内容
            coVerify(exactly = 1) {
                mockRepository.saveThinkingProcess(messageId, "第二次内容")
            }
            coVerify(exactly = 0) {
                mockRepository.saveThinkingProcess(messageId, "第一次内容")
            }
        }
    }

    @Nested
    @DisplayName("错误处理测试")
    inner class ErrorHandlingTests {

        @Test
        @DisplayName("Repository错误应该被记录但不崩溃")
        fun `should handle repository errors gracefully`() = runTest {
            // Given
            val messageId = "error-test"
            val thinkingMarkdown = "错误测试内容"
            val effect = ThinkingBoxContract.Effect.NotifyHistoryThinking(
                messageId = messageId,
                thinkingMarkdown = thinkingMarkdown,
            )

            coEvery {
                mockRepository.saveThinkingProcess(messageId, thinkingMarkdown)
            } returns Result.failure(RuntimeException("数据库错误"))

            val effectFlow = flowOf(effect)

            // When & Then - 不应该抛出异常
            historyActor.initialize(effectFlow, testScope)
            testScheduler.advanceUntilIdle()

            coVerify(exactly = 1) {
                mockRepository.saveThinkingProcess(messageId, thinkingMarkdown)
            }
        }

        @Test
        @DisplayName("异常应该被捕获并记录")
        fun `should catch and log exceptions`() = runTest {
            // Given
            val messageId = "exception-test"
            val effect = ThinkingBoxContract.Effect.NotifyHistoryThinking(
                messageId = messageId,
                thinkingMarkdown = "异常测试",
            )

            coEvery {
                mockRepository.saveThinkingProcess(any(), any())
            } throws RuntimeException("意外异常")

            val effectFlow = flowOf(effect)

            // When & Then - 不应该崩溃
            historyActor.initialize(effectFlow, testScope)
            testScheduler.advanceUntilIdle()
        }
    }

    @Nested
    @DisplayName("并发处理测试")
    inner class ConcurrencyTests {

        @Test
        @DisplayName("多个messageId的Effect应该并发处理")
        fun `should handle multiple messageIds concurrently`() = runTest {
            // Given
            val effect1 = ThinkingBoxContract.Effect.NotifyHistoryThinking(
                messageId = "msg-1",
                thinkingMarkdown = "内容1",
            )
            val effect2 = ThinkingBoxContract.Effect.NotifyHistoryThinking(
                messageId = "msg-2",
                thinkingMarkdown = "内容2",
            )

            coEvery { mockRepository.saveThinkingProcess(any(), any()) } returns Result.success(Unit)
            val effectFlow = flowOf(effect1, effect2)

            // When
            historyActor.initialize(effectFlow, testScope)
            testScheduler.advanceUntilIdle()

            // Then
            coVerify(exactly = 1) { mockRepository.saveThinkingProcess("msg-1", "内容1") }
            coVerify(exactly = 1) { mockRepository.saveThinkingProcess("msg-2", "内容2") }
        }
    }
}