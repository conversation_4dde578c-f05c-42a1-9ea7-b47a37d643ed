package com.example.gymbro.features.thinkingbox.api

/**
 * ConversationTurn - 对话轮次数据类
 *
 * 🎯 职责分离架构设计：
 * - 表示对话中的单个轮次（用户消息+AI响应的组合）
 * - 支持对话历史的结构化存储和检索
 * - 遵循Clean Architecture原则，作为领域数据载体
 * - 提供完整的对话上下文信息，支持连续对话
 *
 * 🔥 核心功能：
 * - 消息配对：用户输入与AI响应的关联
 * - 时间追踪：记录对话的时间信息
 * - 元数据支持：扩展信息的存储和检索
 * - 内容分类：支持不同类型的消息内容
 *
 * 架构原则：
 * - 数据完整性：确保对话信息的完整记录
 * - 类型安全：使用强类型避免数据错误
 * - 可扩展性：支持未来新增消息类型和属性
 * - 序列化友好：支持持久化存储和网络传输
 *
 * @since Coach-ThinkingBox重构v2.0
 */
data class ConversationTurn(
    /**
     * 轮次唯一标识符
     *
     * 用于唯一标识对话中的每个轮次，支持轮次级别的追踪和管理。
     * 格式建议：UUID或时间戳+序号的组合
     */
    val turnId: String,

    /**
     * 用户消息
     *
     * 用户在本轮次中的输入内容。
     * 包含用户的原始表达，保持完整性和准确性。
     */
    val userMessage: String,

    /**
     * AI响应消息
     *
     * AI在本轮次中的响应内容。
     * 可能包含思考过程、最终答案或错误信息。
     */
    val aiResponse: String? = null,

    /**
     * AI思考过程
     *
     * AI生成响应时的思考过程记录。
     * 用于展示AI的推理逻辑和决策过程。
     */
    val aiThinkingProcess: String? = null,

    /**
     * 用户消息时间戳
     *
     * 用户发送消息的时间，用于时间排序和分析。
     * 格式：Unix时间戳（毫秒）
     */
    val userMessageTimestamp: Long,

    /**
     * AI响应时间戳
     *
     * AI完成响应的时间，用于响应时间分析。
     * 格式：Unix时间戳（毫秒）
     */
    val aiResponseTimestamp: Long? = null,

    /**
     * 消息类型
     *
     * 描述本轮次消息的类型和性质。
     * 支持的类型："normal"、"question"、"command"、"feedback"等
     */
    val messageType: String = "normal",

    /**
     * 响应状态
     *
     * 描述AI响应的状态。
     * 支持的状态："completed"、"failed"、"partial"、"cancelled"
     */
    val responseStatus: String = "completed",

    /**
     * 处理时长（毫秒）
     *
     * AI处理本轮次消息所花费的时间。
     * 用于性能分析和优化。
     */
    val processingDuration: Long? = null,

    /**
     * 轮次元数据
     *
     * 存储额外的轮次相关信息，支持扩展功能。
     * 可能包含：用户情绪、消息复杂度、处理模式等
     */
    val metadata: Map<String, Any> = emptyMap(),
) {
    /**
     * 检查轮次是否完整
     *
     * 验证轮次是否包含完整的用户消息和AI响应。
     *
     * @return 如果轮次完整返回true，否则返回false
     */
    fun isComplete(): Boolean {
        return userMessage.isNotBlank() &&
            !aiResponse.isNullOrBlank() &&
            aiResponseTimestamp != null
    }

    /**
     * 检查轮次是否有效
     *
     * 验证轮次数据的基本有效性。
     *
     * @return 如果轮次数据有效返回true，否则返回false
     */
    fun isValid(): Boolean {
        return turnId.isNotBlank() &&
            userMessage.isNotBlank() &&
            userMessageTimestamp > 0
    }

    /**
     * 检查是否有思考过程
     *
     * @return 如果包含AI思考过程返回true
     */
    fun hasThinkingProcess(): Boolean {
        return !aiThinkingProcess.isNullOrBlank()
    }

    /**
     * 计算响应时间
     *
     * 计算从用户发送消息到AI完成响应的时间间隔。
     *
     * @return 响应时间（毫秒），如果AI尚未响应则返回null
     */
    fun getResponseTime(): Long? {
        return if (aiResponseTimestamp != null) {
            aiResponseTimestamp - userMessageTimestamp
        } else {
            null
        }
    }

    /**
     * 获取轮次摘要
     *
     * 用于日志记录和调试，提供轮次的关键信息概览。
     *
     * @return 包含关键信息的摘要字符串
     */
    fun getSummary(): String {
        val responseTime = getResponseTime()
        return "ConversationTurn(" +
            "turnId=$turnId, " +
            "userMessageLength=${userMessage.length}, " +
            "hasAiResponse=${!aiResponse.isNullOrBlank()}, " +
            "hasThinkingProcess=${hasThinkingProcess()}, " +
            "responseTime=${responseTime}ms, " +
            "status=$responseStatus)"
    }

    /**
     * 获取用户消息预览
     *
     * 获取用户消息的简短预览，用于UI显示。
     *
     * @param maxLength 最大长度，默认50字符
     * @return 用户消息预览
     */
    fun getUserMessagePreview(maxLength: Int = 50): String {
        return if (userMessage.length <= maxLength) {
            userMessage
        } else {
            userMessage.take(maxLength - 3) + "..."
        }
    }

    /**
     * 获取AI响应预览
     *
     * 获取AI响应的简短预览，用于UI显示。
     *
     * @param maxLength 最大长度，默认100字符
     * @return AI响应预览
     */
    fun getAiResponsePreview(maxLength: Int = 100): String {
        val response = aiResponse ?: return "无响应"
        return if (response.length <= maxLength) {
            response
        } else {
            response.take(maxLength - 3) + "..."
        }
    }

    /**
     * 创建轮次的完成副本
     *
     * 基于当前轮次创建包含AI响应的完成副本。
     *
     * @param aiResponse AI响应内容
     * @param aiThinkingProcess AI思考过程（可选）
     * @param responseTimestamp 响应时间戳，默认为当前时间
     * @return 完成的ConversationTurn副本
     */
    fun completeWithResponse(
        aiResponse: String,
        aiThinkingProcess: String? = null,
        responseTimestamp: Long = System.currentTimeMillis(),
    ): ConversationTurn {
        val processingTime = responseTimestamp - userMessageTimestamp
        return copy(
            aiResponse = aiResponse,
            aiThinkingProcess = aiThinkingProcess,
            aiResponseTimestamp = responseTimestamp,
            processingDuration = processingTime,
            responseStatus = "completed",
        )
    }

    /**
     * 创建失败状态的轮次副本
     *
     * 基于当前轮次创建表示处理失败的副本。
     *
     * @param errorMessage 错误信息
     * @param failureTimestamp 失败时间戳，默认为当前时间
     * @return 失败状态的ConversationTurn副本
     */
    fun markAsFailed(
        errorMessage: String,
        failureTimestamp: Long = System.currentTimeMillis(),
    ): ConversationTurn {
        val processingTime = failureTimestamp - userMessageTimestamp
        return copy(
            aiResponse = "处理失败：$errorMessage",
            aiResponseTimestamp = failureTimestamp,
            processingDuration = processingTime,
            responseStatus = "failed",
        )
    }

    companion object {
        /**
         * 创建新的对话轮次
         *
         * 用于创建只包含用户消息的新轮次，等待AI响应。
         *
         * @param turnId 轮次ID
         * @param userMessage 用户消息
         * @param messageType 消息类型，默认为"normal"
         * @param timestamp 时间戳，默认为当前时间
         * @return 新的ConversationTurn实例
         */
        fun createNew(
            turnId: String,
            userMessage: String,
            messageType: String = "normal",
            timestamp: Long = System.currentTimeMillis(),
        ): ConversationTurn {
            return ConversationTurn(
                turnId = turnId,
                userMessage = userMessage,
                userMessageTimestamp = timestamp,
                messageType = messageType,
                responseStatus = "pending",
            )
        }

        /**
         * 创建完整的对话轮次
         *
         * 用于创建包含用户消息和AI响应的完整轮次。
         *
         * @param turnId 轮次ID
         * @param userMessage 用户消息
         * @param aiResponse AI响应
         * @param userTimestamp 用户消息时间戳
         * @param aiTimestamp AI响应时间戳
         * @param aiThinkingProcess AI思考过程（可选）
         * @return 完整的ConversationTurn实例
         */
        fun createComplete(
            turnId: String,
            userMessage: String,
            aiResponse: String,
            userTimestamp: Long,
            aiTimestamp: Long,
            aiThinkingProcess: String? = null,
        ): ConversationTurn {
            return ConversationTurn(
                turnId = turnId,
                userMessage = userMessage,
                aiResponse = aiResponse,
                aiThinkingProcess = aiThinkingProcess,
                userMessageTimestamp = userTimestamp,
                aiResponseTimestamp = aiTimestamp,
                processingDuration = aiTimestamp - userTimestamp,
                responseStatus = "completed",
            )
        }
    }
}
