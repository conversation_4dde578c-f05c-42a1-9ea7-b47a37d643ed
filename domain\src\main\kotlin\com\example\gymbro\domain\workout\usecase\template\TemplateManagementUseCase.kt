package com.example.gymbro.domain.workout.usecase.template

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.base.modern.ModernFlowUseCase
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.model.template.toDto
import com.example.gymbro.domain.workout.repository.TemplateRepository
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 新架构：Templates单点数据管理器
 *
 * 设计原则：
 * 1. 整个应用只有一个地方查询数据库
 * 2. 所有模板数据通过唯一的SharedFlow分发
 * 3. 数据变更时自动刷新并通知所有订阅者
 * 4. 避免重复查询和竞态条件
 */
@Singleton
class TemplatesDataManager @Inject constructor(
    private val repository: TemplateRepository,
    private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val logger: Logger,
) {
    // 🔥 核心：唯一的数据流，所有订阅者都从这里获取数据
    private val _templatesFlow = MutableSharedFlow<ModernResult<List<WorkoutTemplateDto>>>(
        replay = 1, // 保留最后一个结果给新订阅者
        extraBufferCapacity = 0,
    )
    val templatesFlow: SharedFlow<ModernResult<List<WorkoutTemplateDto>>> = _templatesFlow.asSharedFlow()

    // 🔥 防止并发查询的互斥锁和协程作用域
    private val queryMutex = Mutex()
    private val managerScope = CoroutineScope(dispatcher + SupervisorJob())
    private var isInitialized = false
    private var currentUserId: String? = null

    /**
     * 🔥 唯一的数据查询方法 - 只有这里会访问数据库
     */
    private suspend fun performSingleQuery(forceRefresh: Boolean = false) = queryMutex.withLock {
        try {
            // 获取当前用户ID
            val userIdResult = getCurrentUserIdUseCase().firstOrNull()
            val userId = when (userIdResult) {
                is ModernResult.Success -> {
                    val realUserId = userIdResult.data
                    if (!realUserId.isNullOrBlank()) {
                        realUserId
                    } else {
                        logger.w("获取用户ID为空，可能未登录")
                        null
                    }
                }
                is ModernResult.Error -> {
                    logger.w("获取用户ID失败: ${userIdResult.error}")
                    null
                }
                else -> {
                    logger.w("用户ID获取状态异常")
                    null
                }
            }

            // 🔥 关键：用户切换时强制刷新
            val shouldQuery = forceRefresh || !isInitialized || currentUserId != userId

            if (!shouldQuery) {
                logger.d("🚫 跳过查询：数据已是最新 (userId=$userId)")
                return@withLock
            }

            currentUserId = userId
            logger.d("🔍 [唯一查询] 开始查询用户模板: userId=$userId")
            println("🔍 UseCase.GetTemplates: 查询用户ID = $userId")

            if (userId != null) {
                // 🔥 核心：唯一的数据库查询
                managerScope.launch {
                    repository.getTemplatesByUser(userId)
                        .map { result ->
                            when (result) {
                                is ModernResult.Success -> {
                                    logger.d("✅ [唯一查询] 成功获取 ${result.data.size} 个模板")
                                    logger.d(
                                        "WK-TEMPLATE-QUERY",
                                        "📋 UseCase.GetTemplates: 查询到 ${result.data.size} 个模板",
                                    )

                                    // 调试输出
                                    result.data.forEachIndexed { index, template ->
                                        logger.d(
                                            "WK-TEMPLATE-QUERY",
                                            "📋 查询结果$index: name='${template.name}', userId='${template.userId}', isDraft=${template.isDraft}",
                                        )
                                    }

                                    // 转换为DTO并缓存
                                    val dtoList = result.data.map { it.toDto() }
                                    ModernResult.Success(dtoList)
                                }
                                is ModernResult.Error -> {
                                    logger.e("❌ [唯一查询] 查询失败: ${result.error}")
                                    println("❌ UseCase.GetTemplates: 查询失败 ${result.error}")
                                    result
                                }
                                is ModernResult.Loading -> ModernResult.Loading
                            }
                        }
                        .onEach { result ->
                            // 🔥 关键：发送到共享流，通知所有订阅者
                            _templatesFlow.emit(result)
                            if (result is ModernResult.Success || result is ModernResult.Error) {
                                isInitialized = true
                            }
                        }
                        .collect()
                }
            } else {
                // 用户未认证，返回空列表
                logger.d("📋 [唯一查询] 用户未认证，返回空列表")
                logger.d("WK-TEMPLATE-QUERY", "📋 UseCase.GetTemplates: 用户未认证，返回空列表")
                _templatesFlow.emit(ModernResult.Success(emptyList()))
                isInitialized = true
            }
        } catch (e: Exception) {
            logger.e(e, "❌ [唯一查询] 查询异常")
            _templatesFlow.emit(
                ModernResult.Error(
                    ModernDataError(
                        operationName = "getTemplates",
                        errorType = GlobalErrorType.Data.General,
                        uiMessage = UiText.DynamicString("获取模板列表失败"),
                        cause = e,
                    ),
                ),
            )
        }
    }

    /**
     * 🔥 初始化数据加载 - 应用启动时调用一次
     */
    suspend fun initialize() {
        logger.d("🚀 初始化Templates数据管理器")
        performSingleQuery(forceRefresh = false)
    }

    /**
     * 🔥 刷新数据 - 数据变更时调用
     */
    suspend fun refresh() {
        logger.d("🔄 刷新Templates数据")
        performSingleQuery(forceRefresh = true)
    }

    /**
     * 🔥 数据变更通知 - 保存/删除/更新后调用
     */
    suspend fun notifyDataChanged() {
        logger.d("📢 通知Templates数据已变更，触发刷新")
        refresh()
    }
}

/**
 * 模板管理UseCase - 重构为单点查询架构
 *
 * 🔥 新设计原则：
 * 1. GetTemplates不再直接查询数据库，而是订阅TemplatesDataManager的数据流
 * 2. 所有数据变更操作（Save/Delete）完成后通知DataManager刷新
 * 3. 确保整个应用只有一个地方触发数据库查询
 */
@Singleton
class TemplateManagementUseCase
@Inject
constructor(
    private val repository: TemplateRepository,
    private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
    private val templatesDataManager: TemplatesDataManager, // 🔥 新增：唯一数据管理器
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val logger: Logger,
) {
    /**
     * 🔥 重构：GetTemplates现在只是订阅数据管理器的流
     * 不再直接查询数据库，避免重复查询
     */
    inner class GetTemplates : ModernFlowUseCase<Unit, List<WorkoutTemplateDto>>(dispatcher, logger) {
        override fun createFlow(parameters: Unit): Flow<ModernResult<List<WorkoutTemplateDto>>> {
            logger.d("📋 [GetTemplates] 订阅Templates数据流")

            // 🔥 关键：只订阅数据管理器的流，不直接查询
            return templatesDataManager.templatesFlow
                .onStart {
                    // 确保数据管理器已初始化
                    templatesDataManager.initialize()
                }
                .catch { throwable ->
                    logger.e(throwable, "订阅Templates数据流发生异常")
                    emit(
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "getTemplates",
                                errorType = GlobalErrorType.Data.General,
                                uiMessage = UiText.DynamicString("获取模板列表失败"),
                                cause = throwable,
                            ),
                        ),
                    )
                }
        }
    }

    /**
     * 获取单个模板
     * 替代原有的GetTemplateUseCase
     * Phase 1修复：返回WorkoutTemplateDto而不是Domain模型
     */
    inner class GetTemplate : ModernUseCase<String, WorkoutTemplateDto?>(dispatcher, logger) {
        override suspend fun execute(templateId: String): ModernResult<WorkoutTemplateDto?> {
            logger.d("获取模板: $templateId")

            if (templateId.isBlank()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "getTemplate",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("模板ID不能为空"),
                    ),
                )
            }

            return when (val result = repository.getTemplateById(templateId)) {
                is ModernResult.Success -> {
                    val templateData = result.data
                    if (templateData != null) {
                        logger.d("成功获取模板: ${templateData.name}")
                        // Phase 1修复：Domain到DTO转换
                        ModernResult.Success(templateData.toDto())
                    } else {
                        logger.w("模板不存在: $templateId")
                        ModernResult.Success(null)
                    }
                }

                is ModernResult.Error -> {
                    logger.e("获取模板失败: ${result.error}")
                    result
                }

                is ModernResult.Loading -> {
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "getTemplate",
                            errorType = GlobalErrorType.System.General,
                            uiMessage = UiText.DynamicString("获取模板失败"),
                        ),
                    )
                }
            }
        }
    }

    /**
     * 保存模板 (创建或更新)
     * 替代原有的SaveTemplateUseCase
     */
    inner class SaveTemplate : ModernUseCase<WorkoutTemplate, String>(dispatcher, logger) {
        override suspend fun execute(template: WorkoutTemplate): ModernResult<String> {
            logger.d("保存模板: ${template.name}")
            println("🔄 UseCase.SaveTemplate: 保存模板 ${template.name}, isDraft=${template.isDraft}")

            // 🎯 支持匿名用户和离线模式
            try {
                // 🔥 修复：确保模板有正确的用户ID，支持匿名用户
                val templateToSave =
                    if (template.userId.isBlank()) {
                        // 如果模板没有用户ID，获取当前用户ID
                        val userIdResult = getCurrentUserIdUseCase().firstOrNull()
                        val currentUserId =
                            when (userIdResult) {
                                is ModernResult.Success -> {
                                    val realUserId = userIdResult.data
                                    if (!realUserId.isNullOrBlank()) {
                                        realUserId // 使用真实用户ID（登录用户或匿名用户）
                                    } else {
                                        // 🔥 用户认证异常时的处理
                                        logger.e("获取用户ID失败，用户可能未认证")
                                        println("❌ UseCase.SaveTemplate: 获取用户ID失败，用户未认证")
                                        throw IllegalStateException("用户未认证，无法保存模板")
                                    }
                                }
                                is ModernResult.Error -> {
                                    // 🔥 认证服务异常时的处理
                                    logger.e("认证服务异常，无法保存模板: ${userIdResult.error}")
                                    println("❌ UseCase.SaveTemplate: 认证服务异常")
                                    throw IllegalStateException("认证服务异常，无法保存模板")
                                }
                                else -> {
                                    // 🔥 Loading状态或其他异常状态
                                    logger.e("用户认证状态异常，无法保存模板")
                                    println("❌ UseCase.SaveTemplate: 用户认证状态异常")
                                    throw IllegalStateException("用户认证状态异常，无法保存模板")
                                }
                            }
                        println("🔄 UseCase.SaveTemplate: 为模板设置用户ID = $currentUserId")
                        template.copy(userId = currentUserId)
                    } else {
                        // 🔥 模板已有用户ID，直接使用
                        println("🔄 UseCase.SaveTemplate: 使用现有用户ID = ${template.userId}")
                        template
                    }

                println("🔄 UseCase.SaveTemplate: 最终保存的模板 userId=${templateToSave.userId}")

                // 🎯 验证模板数据完整性
                if (templateToSave.name.isBlank()) {
                    logger.w("模板名称为空，使用默认名称")
                    val defaultName = "训练模板" // 🔥 修复：统一使用"训练模板"作为默认名称
                    val finalTemplate = templateToSave.copy(name = defaultName)
                    return saveTemplateToRepository(finalTemplate)
                }

                return saveTemplateToRepository(templateToSave)
            } catch (e: Exception) {
                logger.e(e, "保存模板时发生异常: ${template.name}")
                println("❌ UseCase.SaveTemplate: 保存模板异常 ${template.name}: ${e.message}")

                // 🎯 提供用户友好的错误信息
                val errorMessage = when {
                    e.message?.contains("用户") == true -> "用户认证失败，请重新登录后再试"
                    e.message?.contains("网络") == true -> "网络连接失败，请检查网络后重试"
                    e.message?.contains("权限") == true -> "没有权限保存模板，请联系管理员"
                    else -> "保存模板失败，请稍后重试"
                }

                return ModernResult.Error(
                    ModernDataError(
                        operationName = "saveTemplate",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString(errorMessage),
                        cause = e,
                    ),
                )
            }
        }

        private suspend fun saveTemplateToRepository(template: WorkoutTemplate): ModernResult<String> {
            return when (val result = repository.saveTemplate(template)) {
                is ModernResult.Success -> {
                    logger.d("成功保存模板: ${template.name}, ID: ${result.data}")
                    println("✅ UseCase.SaveTemplate: 成功保存模板 ${template.name}, ID: ${result.data}")

                    // 🔥 新架构：通知数据管理器刷新，替代缓存失效
                    templatesDataManager.notifyDataChanged()

                    ModernResult.Success(result.data)
                }

                is ModernResult.Error -> {
                    logger.e("保存模板失败: ${result.error}")
                    println("❌ UseCase.SaveTemplate: 保存模板失败 ${template.name}: ${result.error}")

                    // 🎯 提供更友好的错误信息
                    val friendlyError = when {
                        result.error.uiMessage?.toString()?.contains("用户") == true -> {
                            ModernDataError(
                                operationName = "saveTemplate",
                                errorType = GlobalErrorType.Auth.Unauthorized,
                                uiMessage = UiText.DynamicString("用户认证失败，请重新登录后再试"),
                            )
                        }
                        else -> result.error
                    }
                    ModernResult.Error(friendlyError)
                }

                is ModernResult.Loading -> {
                    println("⚠️ UseCase.SaveTemplate: Repository返回Loading状态")
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "saveTemplate",
                            errorType = GlobalErrorType.System.General,
                            uiMessage = UiText.DynamicString("保存模板失败"),
                        ),
                    )
                }
            }
        }
    }

    /**
     * 删除模板
     * 替代原有的DeleteTemplateUseCase
     */
    inner class DeleteTemplate : ModernUseCase<String, Unit>(dispatcher, logger) {
        override suspend fun execute(templateId: String): ModernResult<Unit> {
            logger.d("删除模板: $templateId")

            if (templateId.isBlank()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "deleteTemplate",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("模板ID不能为空"),
                    ),
                )
            }

            // 先检查模板是否存在
            val templateResult = repository.getTemplateById(templateId)
            if (templateResult is ModernResult.Error) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "deleteTemplate",
                        errorType = GlobalErrorType.Business.NotFound,
                        uiMessage = UiText.DynamicString("模板不存在"),
                    ),
                )
            }

            val template = (templateResult as ModernResult.Success).data

            return when (val result = repository.deleteTemplate(templateId)) {
                is ModernResult.Success -> {
                    logger.d("成功删除模板: ${template?.name ?: templateId}")

                    // 🔥 新架构：通知数据管理器刷新，替代缓存失效
                    templatesDataManager.notifyDataChanged()

                    result
                }

                is ModernResult.Error -> {
                    logger.e("删除模板失败: ${result.error}")
                    result
                }

                is ModernResult.Loading -> {
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "deleteTemplate",
                            errorType = GlobalErrorType.System.General,
                            uiMessage = UiText.DynamicString("删除模板失败"),
                        ),
                    )
                }
            }
        }
    }

    /**
     * 复制模板
     * 替代原有的DuplicateTemplateUseCase
     */
    inner class DuplicateTemplate : ModernUseCase<String, WorkoutTemplateDto>(dispatcher, logger) {
        override suspend fun execute(templateId: String): ModernResult<WorkoutTemplateDto> {
            logger.d("复制模板: $templateId")

            if (templateId.isBlank()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "duplicateTemplate",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("模板ID不能为空"),
                    ),
                )
            }

            // 获取原模板
            val templateResult = repository.getTemplateById(templateId)
            if (templateResult is ModernResult.Error) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "duplicateTemplate",
                        errorType = GlobalErrorType.Business.NotFound,
                        uiMessage = UiText.DynamicString("模板不存在"),
                    ),
                )
            }

            val originalTemplate = (templateResult as ModernResult.Success<WorkoutTemplate>).data

            // 创建副本
            val duplicatedTemplate =
                originalTemplate.copy(
                    id =
                    java.util.UUID
                        .randomUUID()
                        .toString(),
                    name = "${originalTemplate.name} - 副本",
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                    isPublic = false, // 副本默认为私有
                    usageCount = 0, // 重置使用次数
                )

            return when (val result = repository.saveTemplate(duplicatedTemplate)) {
                is ModernResult.Success -> {
                    logger.d("成功复制模板: ${duplicatedTemplate.name}")

                    // 🔥 新架构：通知数据管理器刷新
                    templatesDataManager.notifyDataChanged()

                    // Phase 1修复：返回DTO
                    ModernResult.Success(duplicatedTemplate.toDto())
                }

                is ModernResult.Error -> {
                    logger.e("复制模板失败: ${result.error}")
                    result
                }

                is ModernResult.Loading -> {
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "duplicateTemplate",
                            errorType = GlobalErrorType.System.General,
                            uiMessage = UiText.DynamicString("复制模板失败"),
                        ),
                    )
                }
            }
        }
    }

    /**
     * 事务性保存和版本控制 - 新增
     * 解决原有分步操作的原子性问题
     * 🔥 修复：确保发布时正确更新模板状态
     */
    inner class SaveAndVersionTemplate : ModernUseCase<SaveAndVersionParams, WorkoutTemplate>(
        dispatcher,
        logger,
    ) {
        override suspend fun execute(params: SaveAndVersionParams): ModernResult<WorkoutTemplate> {
            logger.d("事务性保存模板: ${params.template.name}, 发布: ${params.isPublishing}")

            try {
                // 🔥 关键修复：如果是发布，确保模板状态正确
                val templateToSave =
                    if (params.isPublishing) {
                        params.template.copy(
                            isDraft = false, // 🔥 强制设置为非草稿
                            isPublished = true, // 🔥 强制设置为已发布
                            lastPublishedAt = System.currentTimeMillis(), // 🔥 更新发布时间
                            updatedAt = System.currentTimeMillis(), // 🔥 更新修改时间
                        )
                    } else {
                        params.template
                    }

                logger.d(
                    "准备保存模板状态: isDraft=${templateToSave.isDraft}, isPublished=${templateToSave.isPublished}",
                )

                // 1. 保存模板（带正确状态）
                val saveResult = repository.saveTemplate(templateToSave)
                if (saveResult is ModernResult.Error) {
                    logger.e("保存模板失败: ${saveResult.error}")
                    return saveResult
                }

                val savedTemplateId = (saveResult as ModernResult.Success).data
                logger.d("模板保存成功: $savedTemplateId, 最终状态: isDraft=${templateToSave.isDraft}")

                // 2. 如果是发布，创建版本快照
                if (params.isPublishing) {
                    // 注意：这里需要依赖TemplateVersionRepository
                    // 但是为了简化，我们先跳过版本创建
                    // TODO: 在Phase 1完成后添加TemplateVersionRepository的依赖和创建逻辑
                    logger.d("模板发布成功，版本快照将在Phase 1完成后添加")
                }

                // 🔥 修复：返回实际保存的模板状态
                return ModernResult.Success(templateToSave)
            } catch (e: Exception) {
                logger.e(e, "事务性保存失败")
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "saveAndVersionTemplate",
                        errorType = GlobalErrorType.System.General,
                        uiMessage = UiText.DynamicString("保存模板失败: ${e.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 事务性保存参数
     */
    data class SaveAndVersionParams(
        val template: WorkoutTemplate,
        val isPublishing: Boolean = false,
        val versionDescription: String? = null,
    )

    inner class ToggleFavorite : ModernUseCase<String, Unit>(dispatcher, logger) {
        override suspend fun execute(templateId: String): ModernResult<Unit> {
            logger.d("切换模板收藏状态: $templateId")

            if (templateId.isBlank()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "toggleFavorite",
                        errorType = GlobalErrorType.Validation.InvalidInput,
                        uiMessage = UiText.DynamicString("模板ID不能为空"),
                    ),
                )
            }

            // 获取当前模板
            val templateResult = repository.getTemplateById(templateId)
            if (templateResult is ModernResult.Error) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "toggleFavorite",
                        errorType = GlobalErrorType.Business.NotFound,
                        uiMessage = UiText.DynamicString("模板不存在"),
                    ),
                )
            }

            val template = (templateResult as ModernResult.Success<WorkoutTemplate>).data

            // 切换收藏状态
            val updatedTemplate =
                template.copy(
                    isFavorite = !template.isFavorite,
                    updatedAt = System.currentTimeMillis(),
                )

            return when (val result = repository.saveTemplate(updatedTemplate)) {
                is ModernResult.Success -> {
                    val status = if (updatedTemplate.isFavorite) "收藏" else "取消收藏"
                    logger.d("成功${status}模板: ${template.name}")

                    // 🔥 新架构：通知数据管理器刷新
                    templatesDataManager.notifyDataChanged()

                    ModernResult.Success(Unit)
                }

                is ModernResult.Error -> {
                    logger.e("切换收藏状态失败: ${result.error}")
                    result
                }

                is ModernResult.Loading -> {
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "toggleFavorite",
                            errorType = GlobalErrorType.System.General,
                            uiMessage = UiText.DynamicString("切换收藏状态失败"),
                        ),
                    )
                }
            }
        }
    }

    // UseCase实例
    val getTemplates = GetTemplates()
    val getTemplate = GetTemplate()
    val saveTemplate = SaveTemplate()
    val deleteTemplate = DeleteTemplate()
    val duplicateTemplate = DuplicateTemplate()
    val toggleFavorite = ToggleFavorite()
    val saveAndVersionTemplate = SaveAndVersionTemplate()
}
