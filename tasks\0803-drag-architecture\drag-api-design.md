# WorkoutExerciseComponent拖拽API设计

## 🎯 设计目标

基于component-analysis.md的分析结果，设计简洁高效的拖拽API，实现：
1. **简单集成**: 一个参数启用拖拽功能
2. **灵活控制**: 支持不同级别的拖拽操作
3. **性能优化**: 最小化重组和状态开销
4. **向后兼容**: 不影响现有功能

## 📋 API设计方案

### 方案1: 简化单一拖拽 (推荐)

```kotlin
@Composable
fun WorkoutExerciseComponent(
    // 现有参数...
    exercise: ExerciseDto,
    mode: ExerciseComponentMode = ExerciseComponentMode.SESSION,
    exerciseState: SessionExerciseState = SessionExerciseStateFactory.default(),
    // ...其他现有参数
    
    // 🆕 拖拽功能参数
    isDraggable: Boolean = false,                           // 启用拖拽功能
    onDragStart: ((ExerciseDto) -> Unit)? = null,          // 拖拽开始回调
    onDragEnd: ((ExerciseDto, Int, Int) -> Unit)? = null,  // 拖拽结束回调 (item, fromIndex, toIndex)
    dragHandleContent: (@Composable () -> Unit)? = null,   // 自定义拖拽手柄
    
    modifier: Modifier = Modifier
)
```

### 方案2: 多级拖拽控制 (高级)

```kotlin
@Composable  
fun WorkoutExerciseComponent(
    // 现有参数...
    
    // 🆕 拖拽配置
    dragConfig: DragConfig? = null,  // null表示不启用拖拽
    
    modifier: Modifier = Modifier
)

// 拖拽配置数据类
data class DragConfig(
    val level: DragLevel = DragLevel.EXERCISE,              // 拖拽级别
    val onDragStart: (ExerciseDto) -> Unit = {},            // 拖拽开始
    val onDragEnd: (ExerciseDto, Int, Int) -> Unit = {},    // 拖拽结束
    val handlePosition: HandlePosition = HandlePosition.LEFT, // 手柄位置
    val enableHapticFeedback: Boolean = true,               // 触觉反馈
    val customHandle: (@Composable () -> Unit)? = null      // 自定义手柄
)

enum class DragLevel {
    EXERCISE,  // 整个动作拖拽
    SET        // 组级别拖拽
}

enum class HandlePosition {
    LEFT, RIGHT, TOP
}
```

## 🏆 推荐方案: 方案1 简化单一拖拽

**选择理由**:
1. **用户需求匹配**: 符合"单纯引入组件就能实现拖动排序"的要求
2. **API简洁性**: 最少参数，最大效果
3. **学习成本低**: 开发者易于理解和使用
4. **性能最优**: 最小化状态管理复杂度

## 🔧 详细实现设计

### 接口参数说明

1. **isDraggable: Boolean**
   - 控制是否启用拖拽功能
   - 默认false，保持向后兼容

2. **onDragStart: ((ExerciseDto) -> Unit)?**
   - 拖拽开始时的回调
   - 可用于显示拖拽UI反馈或记录操作

3. **onDragEnd: ((ExerciseDto, Int, Int) -> Unit)?**
   - 拖拽结束时的回调
   - 参数: 被拖拽的动作、原索引、目标索引
   - 核心业务逻辑在这里处理

4. **dragHandleContent: (@Composable () -> Unit)?**
   - 自定义拖拽手柄内容
   - 默认使用Material3标准拖拽手柄图标

### UI集成位置

基于component-analysis.md分析，推荐位置：

```kotlin
// 在Card组件左侧添加拖拽手柄
Card(
    modifier = modifier
        .fillMaxWidth()
        .then(
            if (isDraggable) {
                Modifier.dragHandle(
                    onDragStart = { onDragStart?.invoke(exercise) },
                    onDragEnd = { fromIndex, toIndex -> 
                        onDragEnd?.invoke(exercise, fromIndex, toIndex) 
                    }
                )
            } else Modifier
        )
) {
    Row {
        // 拖拽手柄
        if (isDraggable) {
            DragHandle(
                content = dragHandleContent ?: { DefaultDragHandle() }
            )
        }
        
        // 现有内容
        ExerciseContent(...)
    }
}
```

## 🎨 使用示例

### Template Edit中的集成

```kotlin
// 在TemplateEditComponents.kt中
LazyColumn {
    items(exercises) { exercise ->
        WorkoutExerciseComponent(
            exercise = exercise,
            mode = ExerciseComponentMode.TEMPLATE,
            isDraggable = true,
            onDragEnd = { draggedExercise, fromIndex, toIndex ->
                // 一行代码完成排序更新
                onIntent(TemplateEditContract.Intent.ReorderExercise(
                    fromIndex = fromIndex,
                    toIndex = toIndex
                ))
            }
        )
    }
}
```

### Session中的可选拖拽

```kotlin
// 在Session中可以选择不启用拖拽
WorkoutExerciseComponent(
    exercise = exercise,
    mode = ExerciseComponentMode.SESSION,
    isDraggable = false  // Session模式下通常不需要拖拽
)
```

## 🚀 实现优势

1. **零配置启用**: 只需设置`isDraggable = true`
2. **性能优化**: 只有启用拖拽时才添加相关逻辑
3. **视觉一致**: 使用Material3标准拖拽视觉效果
4. **功能完整**: 支持触觉反馈、动画和状态管理
5. **扩展性**: 未来可以基于此API添加更多拖拽特性

## 🔄 集成shared/drag组件

```kotlin
// 内部使用shared/drag组件
private fun Modifier.dragHandle(
    onDragStart: () -> Unit,
    onDragEnd: (Int, Int) -> Unit
): Modifier {
    return this.then(
        // 使用shared/components/drag/DragModifiers
        DragModifiers.draggable(
            dragState = rememberDragState(),
            config = DragConfig.Material3,
            onDragStart = onDragStart,
            onDragEnd = onDragEnd
        )
    )
}
```

## ✅ 下一步行动

1. 实现拖拽手柄UI组件
2. 集成shared/drag组件到WorkoutExerciseComponent
3. 添加Material3动画和触觉反馈
4. 创建使用示例和测试用例