package com.example.gymbro.features.thinkingbox.api

/**
 * UserProfileContext - 用户个人资料上下文
 *
 * 🎯 职责分离架构设计：
 * - 封装用户个人信息，用于AI个性化处理
 * - 遵循Clean Architecture原则，作为领域数据载体
 * - 支持健身应用的专业化用户画像构建
 * - 提供完整的用户特征描述，支持智能推荐
 *
 * 🔥 核心功能：
 * - 基础信息：年龄、性别、健身经验等人口统计学特征
 * - 身体数据：身高、体重、BMI等生理指标
 * - 健身偏好：训练类型、时间安排、目标设定
 * - 限制条件：伤病史、设备限制、时间约束
 *
 * 架构原则：
 * - 数据完整性：提供全面的用户特征描述
 * - 隐私保护：敏感信息的安全处理
 * - 可扩展性：支持未来新增用户属性
 * - 验证机制：确保数据的有效性和一致性
 *
 * @since Coach-ThinkingBox重构v2.0
 */
data class UserProfileContext(
    /**
     * 用户性别
     *
     * 用于性别相关的健身建议和计划制定。
     * 支持的值："男"、"女"、"其他"、"不愿透露"
     */
    val gender: String? = null,

    /**
     * 用户年龄
     *
     * 用于年龄相关的训练强度和方式调整。
     * 范围：通常在13-100之间
     */
    val age: Int? = null,

    /**
     * 健身经验水平
     *
     * 描述用户的健身基础和经验程度。
     * 支持的值："初学者"、"中级"、"高级"、"专业"
     */
    val experience: String? = null,

    /**
     * 身高（厘米）
     *
     * 用于BMI计算和体型相关的训练建议。
     * 范围：通常在100-250cm之间
     */
    val height: Float? = null,

    /**
     * 体重（公斤）
     *
     * 用于BMI计算和负重训练建议。
     * 范围：通常在30-300kg之间
     */
    val weight: Float? = null,

    /**
     * 健身目标列表
     *
     * 用户的主要健身目标，支持多目标设定。
     * 常见目标："减脂"、"增肌"、"塑形"、"提高耐力"、"增强力量"等
     */
    val fitnessGoals: List<String> = emptyList(),

    /**
     * 训练频率偏好
     *
     * 用户希望的每周训练次数。
     * 范围：通常在1-7次之间
     */
    val trainingFrequency: Int? = null,

    /**
     * 单次训练时长偏好（分钟）
     *
     * 用户希望的单次训练时间长度。
     * 范围：通常在15-180分钟之间
     */
    val sessionDuration: Int? = null,

    /**
     * 可用训练时间段
     *
     * 用户可以进行训练的时间段列表。
     * 格式："早晨"、"上午"、"中午"、"下午"、"晚上"、"深夜"
     */
    val availableTimeSlots: List<String> = emptyList(),

    /**
     * 训练场所偏好
     *
     * 用户偏好的训练环境和场所。
     * 支持的值："健身房"、"家庭"、"户外"、"工作场所"等
     */
    val trainingLocation: String? = null,

    /**
     * 可用设备列表
     *
     * 用户可以使用的健身设备。
     * 常见设备："哑铃"、"杠铃"、"跑步机"、"瑜伽垫"、"阻力带"等
     */
    val availableEquipment: List<String> = emptyList(),

    /**
     * 身体限制和伤病史
     *
     * 影响训练计划的身体限制条件。
     * 格式：自由文本描述或结构化的限制类型
     */
    val physicalLimitations: List<String> = emptyList(),

    /**
     * 运动偏好
     *
     * 用户喜欢或不喜欢的运动类型。
     * 常见类型："有氧运动"、"力量训练"、"瑜伽"、"游泳"、"跑步"等
     */
    val exercisePreferences: List<String> = emptyList(),

    /**
     * 营养偏好和限制
     *
     * 饮食相关的偏好和限制条件。
     * 包括："素食"、"低碳水"、"高蛋白"、"过敏信息"等
     */
    val nutritionPreferences: List<String> = emptyList(),

    /**
     * 动机和挑战
     *
     * 用户的健身动机和面临的主要挑战。
     * 用于个性化激励和解决方案推荐。
     */
    val motivationAndChallenges: String? = null,
) {
    /**
     * 计算BMI指数
     *
     * 基于身高和体重计算身体质量指数。
     *
     * @return BMI值，如果身高或体重缺失则返回null
     */
    fun calculateBMI(): Float? {
        return if (height != null && weight != null && height > 0) {
            val heightInMeters = height / 100f
            weight / (heightInMeters * heightInMeters)
        } else {
            null
        }
    }

    /**
     * 获取BMI分类
     *
     * 根据BMI值返回体重分类。
     *
     * @return BMI分类字符串
     */
    fun getBMICategory(): String? {
        val bmi = calculateBMI() ?: return null
        return when {
            bmi < 18.5 -> "偏瘦"
            bmi < 24.0 -> "正常"
            bmi < 28.0 -> "偏胖"
            else -> "肥胖"
        }
    }

    /**
     * 验证用户资料的有效性
     *
     * 检查关键字段是否符合基本要求。
     *
     * @return 如果用户资料有效返回true，否则返回false
     */
    fun isValid(): Boolean {
        // 检查年龄范围
        if (age != null && (age < 13 || age > 100)) {
            return false
        }

        // 检查身高范围
        if (height != null && (height < 100 || height > 250)) {
            return false
        }

        // 检查体重范围
        if (weight != null && (weight < 30 || weight > 300)) {
            return false
        }

        // 检查训练频率
        if (trainingFrequency != null && (trainingFrequency < 1 || trainingFrequency > 7)) {
            return false
        }

        // 检查训练时长
        if (sessionDuration != null && (sessionDuration < 15 || sessionDuration > 180)) {
            return false
        }

        return true
    }

    /**
     * 检查是否有基本身体数据
     *
     * @return 如果包含身高和体重返回true
     */
    fun hasBasicPhysicalData(): Boolean {
        return height != null && weight != null
    }

    /**
     * 检查是否有健身目标
     *
     * @return 如果设定了健身目标返回true
     */
    fun hasFitnessGoals(): Boolean {
        return fitnessGoals.isNotEmpty()
    }

    /**
     * 检查是否有训练偏好
     *
     * @return 如果设定了训练相关偏好返回true
     */
    fun hasTrainingPreferences(): Boolean {
        return trainingFrequency != null ||
            sessionDuration != null ||
            availableTimeSlots.isNotEmpty() ||
            trainingLocation != null
    }

    /**
     * 获取用户资料摘要
     *
     * 用于日志记录和调试。
     *
     * @return 包含关键信息的摘要字符串
     */
    fun getSummary(): String {
        val bmi = calculateBMI()
        return "UserProfileContext(" +
            "gender=$gender, " +
            "age=$age, " +
            "experience=$experience, " +
            "bmi=${bmi?.let { "%.1f".format(it) }}, " +
            "goalsCount=${fitnessGoals.size}, " +
            "hasTrainingPrefs=${hasTrainingPreferences()})"
    }

    companion object {
        /**
         * 创建基础用户资料
         *
         * 只包含最基本的用户信息。
         *
         * @param gender 性别
         * @param age 年龄
         * @param experience 健身经验
         * @return 基础的UserProfileContext实例
         */
        fun createBasic(
            gender: String,
            age: Int,
            experience: String,
        ): UserProfileContext {
            return UserProfileContext(
                gender = gender,
                age = age,
                experience = experience,
            )
        }

        /**
         * 创建包含身体数据的用户资料
         *
         * @param gender 性别
         * @param age 年龄
         * @param height 身高
         * @param weight 体重
         * @param experience 健身经验
         * @return 包含身体数据的UserProfileContext实例
         */
        fun createWithPhysicalData(
            gender: String,
            age: Int,
            height: Float,
            weight: Float,
            experience: String,
        ): UserProfileContext {
            return UserProfileContext(
                gender = gender,
                age = age,
                height = height,
                weight = weight,
                experience = experience,
            )
        }
    }
}
