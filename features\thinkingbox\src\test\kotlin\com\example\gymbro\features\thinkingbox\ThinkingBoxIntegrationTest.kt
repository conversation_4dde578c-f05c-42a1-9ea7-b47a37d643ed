package com.example.gymbro.features.thinkingbox

import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.assertIsDisplayed
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel.ThinkingBoxViewModel
import com.example.gymbro.features.thinkingbox.internal.reducer.SegmentQueueReducer
import com.example.gymbro.features.thinkingbox.internal.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.internal.mapper.DomainMapper
// TokenRouter已删除，新架构不再需要
import com.example.gymbro.designSystem.theme.GymBroTheme
import io.mockk.*
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.flow.flowOf
import org.junit.Rule
import org.junit.Before
import org.junit.Test
import kotlin.test.*

/**
 * ThinkingBox 集成测试套件
 *
 * 🎯 测试覆盖范围：
 * - ThinkingBox组件的完整渲染
 * - Token流接收和处理
 * - XML解析和Segment队列管理
 * - UI状态更新和动画
 * - 与AiResponseComponents.kt的兼容性
 */
class ThinkingBoxIntegrationTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    // Mock dependencies
    // TokenRouter已删除，新架构不再需要
    private lateinit var mockSegmentQueueReducer: SegmentQueueReducer
    private lateinit var mockStreamingParser: StreamingThinkingMLParser
    private lateinit var mockDomainMapper: DomainMapper
    private lateinit var mockViewModel: ThinkingBoxViewModel

    private val testMessageId = "test-message-123"

    @Before
    fun setup() {
        // TokenRouter已删除，新架构不再需要
        mockSegmentQueueReducer = mockk(relaxed = true)
        mockStreamingParser = mockk(relaxed = true)
        mockDomainMapper = mockk(relaxed = true)
        mockViewModel = mockk(relaxed = true)

        // 设置默认的ViewModel状态
        every { mockViewModel.state } returns flowOf(
            ThinkingBoxContract.State(
                messageId = testMessageId,
                segmentsQueue = emptyList(),
                currentSegment = null,
                finalReady = false,
                finalContent = "",
                streaming = true,
                thinkingClosed = false,
                error = null,
                isLoading = false,
            ),
        )
    }

    // ==================== 基础渲染测试 ====================

    @Test
    fun `given ThinkingBox component, when rendered with messageId, then component displays correctly`() {
        // When
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingBox(
                    messageId = testMessageId,
                    viewModel = mockViewModel,
                )
            }
        }

        // Then
        // 验证组件被正确渲染（具体验证内容需要根据实际UI调整）
        composeTestRule.waitForIdle()
        // 由于ThinkingBox可能在没有内容时不显示，这里主要验证没有崩溃
        assertTrue(true)
    }

    @Test
    fun `given ThinkingBox with thinking content, when rendered, then thinking segments displayed`() {
        // Given
        val thinkingSegment = ThinkingBoxContract.SegmentUi(
            id = "segment-1",
            kind = ThinkingBoxContract.SegmentKind.THINKING,
            title = "思考中...",
            content = "让我想想这个问题...",
            isComplete = false,
        )

        every { mockViewModel.state } returns flowOf(
            ThinkingBoxContract.State(
                messageId = testMessageId,
                segmentsQueue = listOf(thinkingSegment),
                currentSegment = thinkingSegment,
                finalReady = false,
                finalContent = "",
                streaming = true,
                thinkingClosed = false,
                error = null,
                isLoading = false,
            ),
        )

        // When
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingBox(
                    messageId = testMessageId,
                    viewModel = mockViewModel,
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText("思考中...").assertIsDisplayed()
        composeTestRule.onNodeWithText("让我想想这个问题...").assertIsDisplayed()
    }

    @Test
    fun `given ThinkingBox with final content, when thinking closed, then final content displayed`() {
        // Given
        val finalContent = "这是最终的回答内容"

        every { mockViewModel.state } returns flowOf(
            ThinkingBoxContract.State(
                messageId = testMessageId,
                segmentsQueue = emptyList(),
                currentSegment = null,
                finalReady = true,
                finalContent = finalContent,
                streaming = false,
                thinkingClosed = true,
                error = null,
                isLoading = false,
            ),
        )

        // When
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingBox(
                    messageId = testMessageId,
                    viewModel = mockViewModel,
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText(finalContent).assertIsDisplayed()
    }

    // ==================== Token流处理测试 ====================

    @Test
    fun `given token stream, when ThinkingBox receives tokens, then ViewModel processes tokens correctly`() = runTest {
        // Given
        val tokens = listOf(
            "<thinking>",
            "让我分析一下这个问题...",
            "</thinking>",
            "<final>",
            "这是我的回答",
            "</final>",
        )

        // When
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingBox(
                    messageId = testMessageId,
                    viewModel = mockViewModel,
                )
            }
        }

        // 模拟token流
        tokens.forEach { token ->
            mockTokenRouter.routeToken(testMessageId, token)
        }

        // Then
        verify(atLeast = tokens.size) {
            mockTokenRouter.routeToken(testMessageId, any())
        }
    }

    @Test
    fun `given XML parsing error, when malformed tokens received, then error handled gracefully`() = runTest {
        // Given
        val malformedTokens = listOf(
            "<thinking>",
            "正常内容",
            "<invalid_tag>", // 无效标签
            "更多内容",
            "</thinking>",
        )

        // Mock parser to handle malformed XML
        every { mockStreamingParser.parseToken(any()) } throws Exception("XML parsing error")

        // When
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingBox(
                    messageId = testMessageId,
                    viewModel = mockViewModel,
                )
            }
        }

        // 模拟发送有问题的token
        malformedTokens.forEach { token ->
            try {
                mockTokenRouter.routeToken(testMessageId, token)
            } catch (e: Exception) {
                // 验证错误被捕获
            }
        }

        // Then
        // 验证组件仍然正常工作，没有崩溃
        composeTestRule.waitForIdle()
        assertTrue(true)
    }

    // ==================== 状态管理测试 ====================

    @Test
    fun `given state changes, when ViewModel state updates, then UI reflects changes`() = runTest {
        // Given
        val initialState = ThinkingBoxContract.State(
            messageId = testMessageId,
            segmentsQueue = emptyList(),
            currentSegment = null,
            finalReady = false,
            finalContent = "",
            streaming = true,
            thinkingClosed = false,
            error = null,
            isLoading = true,
        )

        val updatedState = initialState.copy(
            isLoading = false,
            currentSegment = ThinkingBoxContract.SegmentUi(
                id = "segment-1",
                kind = ThinkingBoxContract.SegmentKind.THINKING,
                title = "分析中",
                content = "正在分析问题...",
                isComplete = false,
            ),
        )

        // When
        every { mockViewModel.state } returns flowOf(initialState, updatedState)

        composeTestRule.setContent {
            GymBroTheme {
                ThinkingBox(
                    messageId = testMessageId,
                    viewModel = mockViewModel,
                )
            }
        }

        // Then
        composeTestRule.waitForIdle()
        // 验证UI反映了状态变化
        composeTestRule.onNodeWithText("分析中").assertIsDisplayed()
    }

    // ==================== 与AiResponseComponents兼容性测试 ====================

    @Test
    fun `given ThinkingBox and AiResponseComponents, when both used together, then no conflicts occur`() {
        // Given
        val finalContent = "这是AI的最终回答"

        every { mockViewModel.state } returns flowOf(
            ThinkingBoxContract.State(
                messageId = testMessageId,
                segmentsQueue = emptyList(),
                currentSegment = null,
                finalReady = true,
                finalContent = finalContent,
                streaming = false,
                thinkingClosed = true,
                error = null,
                isLoading = false,
            ),
        )

        // When - 同时使用ThinkingBox和AiResponseComponents
        composeTestRule.setContent {
            GymBroTheme {
                androidx.compose.foundation.layout.Column {
                    // ThinkingBox组件
                    ThinkingBox(
                        messageId = testMessageId,
                        viewModel = mockViewModel,
                    )

                    // 模拟AiResponseComponents的使用
                    androidx.compose.material3.Text(
                        text = "这是来自AiResponseComponents的内容",
                        modifier = androidx.compose.ui.Modifier.testTag("ai-response-component"),
                    )
                }
            }
        }

        // Then
        composeTestRule.onNodeWithText(finalContent).assertIsDisplayed()
        composeTestRule.onNodeWithText("这是来自AiResponseComponents的内容").assertIsDisplayed()
    }

    // ==================== 性能测试 ====================

    @Test
    fun `given large number of segments, when ThinkingBox renders, then performance acceptable`() {
        // Given
        val largeSegmentList = (1..100).map { index ->
            ThinkingBoxContract.SegmentUi(
                id = "segment-$index",
                kind = ThinkingBoxContract.SegmentKind.THINKING,
                title = "段落 $index",
                content = "这是第 $index 个思考段落的内容",
                isComplete = true,
            )
        }

        every { mockViewModel.state } returns flowOf(
            ThinkingBoxContract.State(
                messageId = testMessageId,
                segmentsQueue = largeSegmentList,
                currentSegment = null,
                finalReady = false,
                finalContent = "",
                streaming = false,
                thinkingClosed = false,
                error = null,
                isLoading = false,
            ),
        )

        // When
        val startTime = System.currentTimeMillis()

        composeTestRule.setContent {
            GymBroTheme {
                ThinkingBox(
                    messageId = testMessageId,
                    viewModel = mockViewModel,
                )
            }
        }

        composeTestRule.waitForIdle()
        val endTime = System.currentTimeMillis()

        // Then
        val renderTime = endTime - startTime
        assertTrue(renderTime < 1000, "ThinkingBox rendering took too long: ${renderTime}ms for 100 segments")
    }

    // ==================== 错误处理测试 ====================

    @Test
    fun `given error state, when ThinkingBox displays error, then error message shown`() {
        // Given
        val errorMessage = com.example.gymbro.core.ui.text.UiText.DynamicString("解析错误")

        every { mockViewModel.state } returns flowOf(
            ThinkingBoxContract.State(
                messageId = testMessageId,
                segmentsQueue = emptyList(),
                currentSegment = null,
                finalReady = false,
                finalContent = "",
                streaming = false,
                thinkingClosed = false,
                error = errorMessage,
                isLoading = false,
            ),
        )

        // When
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingBox(
                    messageId = testMessageId,
                    viewModel = mockViewModel,
                )
            }
        }

        // Then
        composeTestRule.onNodeWithText("解析错误").assertIsDisplayed()
    }
}
