package com.example.gymbro.features.workout.template.edit.internal.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.shared.models.workout.Difficulty
import com.example.gymbro.shared.models.workout.TemplateCategory
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import com.example.gymbro.shared.models.workout.TemplateSource
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto

/**
 * 模板预览组件 - P4阶段核心组件
 *
 * 🎯 P4阶段功能:
 * - 实时预览功能
 * - 统计信息展示
 * - 流畅动画效果
 * - Material Design 3样式
 *
 * 🏗️ 架构原则:
 * - designSystem主题令牌使用
 * - 数据统计计算
 * - 无障碍支持
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemplatePreview(
    template: WorkoutTemplate?,
    exercises: List<TemplateExerciseDto>,
    onHidePreview: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxSize(),
    ) {
        // 预览头部
        PreviewHeader(
            template = template,
            onHidePreview = onHidePreview,
        )

        // 预览内容
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 模板统计信息
            item {
                TemplateStatsCard(
                    template = template,
                    exercises = exercises,
                )
            }

            // 动作列表预览
            item {
                Text(
                    text = "训练动作 (${exercises.size})",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )
            }

            if (exercises.isEmpty()) {
                item {
                    EmptyPreviewCard()
                }
            } else {
                itemsIndexed(exercises) { index, exercise ->
                    ExercisePreviewCard(
                        exercise = exercise,
                        index = index,
                    )
                }
            }

            // 底部间距
            item {
                Spacer(modifier = Modifier.height(Tokens.Card.HeightMin))
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PreviewHeader(
    template: WorkoutTemplate?,
    onHidePreview: () -> Unit,
) {
    TopAppBar(
        title = {
            Text(
                text = "模板预览",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Medium,
            )
        },
        navigationIcon = {
            IconButton(onClick = onHidePreview) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "关闭预览",
                )
            }
        },
        actions = {
            // 分享按钮暂时隐藏，未来功能
            // IconButton(onClick = { /* 分享功能 */ }) {
            //     Icon(
            //         imageVector = Icons.Default.Share,
            //         contentDescription = "分享模板",
            //     )
            // }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.surface,
            titleContentColor = MaterialTheme.colorScheme.onSurface,
            navigationIconContentColor = MaterialTheme.colorScheme.onSurface,
            actionIconContentColor = MaterialTheme.colorScheme.onSurface,
        ),
    )
}

@Composable
private fun TemplateStatsCard(
    template: WorkoutTemplate?,
    exercises: List<TemplateExerciseDto>,
) {
    // 🔥 统一数据源：使用 WorkoutTemplateDto 扩展属性，消除重复计算逻辑 (712template预览card优化.md)
    // 创建临时 WorkoutTemplateDto 以使用标准化的扩展属性
    val templateDto = remember(template, exercises) {
        template?.let {
            WorkoutTemplateDto(
                id = it.id,
                name = it.name,
                description = it.description ?: "",
                // 映射 domain 模型的 Int 难度到 DTO 的 Difficulty 枚举
                difficulty = when (it.difficulty) {
                    1 -> Difficulty.EASY
                    2 -> Difficulty.EASY
                    3 -> Difficulty.MEDIUM
                    4 -> Difficulty.HARD
                    5 -> Difficulty.EXPERT
                    else -> Difficulty.MEDIUM
                },
                // 映射 domain 模型的 String 分类到 DTO 的 TemplateCategory 枚举
                category = when (it.category?.uppercase()) {
                    "STRENGTH" -> TemplateCategory.STRENGTH
                    "CARDIO" -> TemplateCategory.CARDIO
                    "FLEXIBILITY" -> TemplateCategory.FLEXIBILITY
                    "MIXED" -> TemplateCategory.MIXED
                    "REHABILITATION" -> TemplateCategory.REHABILITATION
                    "UPPER_BODY" -> TemplateCategory.UPPER_BODY
                    "LOWER_BODY" -> TemplateCategory.LOWER_BODY
                    "CORE" -> TemplateCategory.CORE
                    "FULL_BODY" -> TemplateCategory.FULL_BODY
                    "CUSTOM" -> TemplateCategory.CUSTOM
                    else -> TemplateCategory.STRENGTH
                },
                exercises = exercises,
                // 映射 domain 模型字段到 DTO 字段
                source = TemplateSource.USER,
                createdAt = it.createdAt,
                updatedAt = it.updatedAt,
                version = it.currentVersion,
            )
        }
    }

    // 🎯 使用标准化扩展属性，确保与其他组件一致
    val totalSets = templateDto?.totalSets ?: 0
    val totalVolume = templateDto?.totalVolume ?: 0f

    val estimatedDuration = calculateEstimatedDuration(exercises)
    val targetMuscles = template?.targetMuscleGroups ?: emptyList()

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.workoutColors.cardBackground, // 🔥 Phase 0: 使用 workoutColors 替代 colorScheme
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Large), // 🔥 Phase 0: 使用 Tokens 替代硬编码
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium), // 🔥 Phase 0: 使用 Tokens 替代硬编码
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Icon(
                    imageVector = Icons.Default.Analytics,
                    contentDescription = null,
                    tint = MaterialTheme.workoutColors.accentPrimary, // 🔥 Phase 0: 使用 workoutColors 替代 colorScheme
                )
                Spacer(modifier = Modifier.width(Tokens.Spacing.Small)) // 🔥 Phase 0: 使用 Tokens 替代硬编码
                Text(
                    text = "训练统计",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                )
            }

            // 统计数据 - 升级为四项统计 (新增总重量)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
            ) {
                StatItem(
                    label = "动作数量",
                    value = "${exercises.size}",
                    icon = Icons.Default.FitnessCenter,
                )

                StatItem(
                    label = "总组数",
                    value = "$totalSets",
                    icon = Icons.Default.Repeat,
                )

                // 🔥 新增：总重量统计项
                StatItem(
                    label = "总重量",
                    value = "%.1f kg".format(totalVolume),
                    icon = Icons.Default.Scale,
                )

                StatItem(
                    label = "预计时长",
                    value = "${estimatedDuration}分钟",
                    icon = Icons.Default.Schedule,
                )
            }

            // 目标肌群
            if (targetMuscles.isNotEmpty()) {
                Column {
                    Text(
                        text = "目标肌群",
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f),
                    )
                    Spacer(modifier = Modifier.height(Tokens.Spacing.XSmall))
                    Text(
                        text = targetMuscles.joinToString(", "),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer,
                    )
                }
            }
        }
    }
}

@Composable
private fun StatItem(
    label: String,
    value: String,
    icon: ImageVector,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(Tokens.Icon.Standard),
            tint = MaterialTheme.colorScheme.onPrimaryContainer,
        )
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onPrimaryContainer,
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f),
        )
    }
}

@Composable
private fun ExercisePreviewCard(
    exercise: TemplateExerciseDto,
    index: Int,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface,
        ),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 序号
            Surface(
                modifier = Modifier.size(Tokens.Icon.Large),
                shape = RoundedCornerShape(Tokens.Radius.Medium),
                color = MaterialTheme.colorScheme.secondaryContainer,
            ) {
                Box(
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = "${index + 1}",
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSecondaryContainer,
                    )
                }
            }

            Spacer(modifier = Modifier.width(Tokens.Spacing.Small))

            // 动作信息
            Column(
                modifier = Modifier.weight(1f),
            ) {
                Text(
                    text = exercise.exerciseName ?: "未知动作",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )

                Spacer(modifier = Modifier.height(Tokens.Spacing.XSmall))

                // 🔥 修复：使用 exercise.summary 显示动态组数和总重量
                Text(
                    text = exercise.summary, // ✅ 一行解决动态组数&总重量
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                )
            }
        }
    }
}

@Composable
private fun EmptyPreviewCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.XLarge),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            Icon(
                imageVector = Icons.Default.Preview,
                contentDescription = null,
                modifier = Modifier.size(Tokens.Icon.XXLarge),
                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
            )

            Text(
                text = "预览为空",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )

            Text(
                text = "添加一些动作后再来查看预览效果",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f),
            )
        }
    }
}

/**
 * 计算预估训练时长
 * 🔥 修复：使用标准化计算逻辑，基于 customSets 优先原则
 */
private fun calculateEstimatedDuration(exercises: List<TemplateExerciseDto>): Int {
    if (exercises.isEmpty()) return 0

    // 🔥 使用与扩展属性一致的计算逻辑
    val totalSets = exercises.sumOf { exercise ->
        if (exercise.customSets.isNotEmpty()) {
            exercise.customSets.size
        } else {
            exercise.sets
        }
    }

    val avgRestTime = exercises.map { it.restTimeSeconds.toDouble() }.average().takeIf { !it.isNaN() } ?: 60.0

    // 基础计算：每组约2分钟（包括动作执行和休息）
    val baseTime = totalSets * 2

    // 根据休息时间调整
    val restAdjustment = (avgRestTime - 60) / 60 * totalSets

    return (baseTime + restAdjustment).toInt().coerceAtLeast(10)
}
