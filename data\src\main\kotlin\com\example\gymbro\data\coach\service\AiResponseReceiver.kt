package com.example.gymbro.data.coach.service

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.domain.coach.model.AiTaskType
import com.example.gymbro.domain.coach.model.StreamEvent
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.serialization.json.*
import timber.log.Timber
import java.util.concurrent.TimeoutException
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚨 【架构重构】此类已废弃 - 被Core-Network统一服务替代
 *
 * ⚠️ 重要说明：
 * - 此类现在是多余的中间层，违反了"唯一数据链路"原则
 * - 所有功能已迁移到 Core-Network 的 UnifiedAiResponseService
 * - AiStreamRepositoryImpl 现在直接调用 UnifiedAiResponseService
 *
 * 🔄 迁移路径：
 * - 旧路径: Coach → AiStreamRepository → AiResponseReceiver → UnifiedAiResponseService
 * - 新路径: Coach → AiStreamRepository → UnifiedAiResponseService (直接)
 *
 * @deprecated 此类将在下一版本中移除，请使用 UnifiedAiResponseService
 */
@Singleton
class AiResponseReceiver @Inject constructor(
    private val unifiedAiResponseService: com.example.gymbro.core.network.service.UnifiedAiResponseService,
    private val networkConfigManager: com.example.gymbro.core.network.config.NetworkConfigManager,
) {
    companion object {
        // 5xx指数退避重试配置
        private val EXPO_BACKOFF = listOf(500L, 1500L, 3500L) // ms
        private const val MAX_RETRY = 3

        // 监控指标 - 修复并发安全问题，使用AtomicLong
        private val sse5xxCount = AtomicLong(0)
        private val totalRequestCount = AtomicLong(0)
        private val retrySuccessCount = AtomicLong(0)
        private val retryTotalCount = AtomicLong(0)
    }

    /**
     * 接收AI流式响应并发布到TokenBus
     */
    suspend fun receiveStreamResponse(
        sessionId: String,
        userMessageId: String,
        aiResponseId: String,
        messages: List<CoreChatMessage>,
        taskType: AiTaskType,
    ): Flow<StreamEvent> = flow {
        // Task1精简：仅保留必要的内容累积器
        val contentBuilder = StringBuilder()
        // New: Accumulator for chunked raw response logging
        val rawResponseLoggingAccumulator = StringBuilder()
        val LOGGING_THRESHOLD = 100 // 最低100字符阈值，避免刷屏

        try {
            Timber.d(
                "开始流式AI请求: sessionId=$sessionId, userMessageId=$userMessageId, aiResponseId=$aiResponseId, taskType=$taskType",
            )
            Timber.d("接收到消息列表: ${messages.size}条消息，避免重复prompt构建")
            totalRequestCount.incrementAndGet()

            // 关键：首包发送Thinking事件（包含完整上下文ID）
            emit(
                StreamEvent.Thinking(
                    sessionId = sessionId,
                    userMessageId = userMessageId,
                    aiResponseId = aiResponseId,
                    timestamp = System.currentTimeMillis(),
                ),
            )
            Timber.d("发送首包Thinking事件: aiResponseId=$aiResponseId")

            // 构建ChatRequest
            val chatRequest = ChatRequest(
                model = "deepseek-chat", // 将由AiRequestSender优化
                messages = messages.map { coreMsg ->
                    com.example.gymbro.shared.models.ai.ChatMessage(
                        role = coreMsg.role,
                        content = coreMsg.content,
                    )
                },
                stream = true,
                maxTokens = 4000,
                temperature = 0.7,
            )

            // 🔥 【修复】使用AdaptiveStreamClient进行真实的流式请求
            // AdaptiveStreamClient会自动将token发布到TokenBus，同时我们需要等待完成
            Timber.d("🚀 启动真实AI流式请求: messageId=$aiResponseId")

            try {
                // TODO: 需要重构为新架构 - 使用UnifiedTokenReceiver
                // 暂时注释掉以让编译通过
                // unifiedTokenReceiver.receiveTokenStream(...)

                // 🔥 【修复】等待流式响应完成的信号
                // 由于AdaptiveStreamClient已经处理token发布，我们这里等待完成信号
                // 通过监听TokenBus的完成事件来确定何时结束
                var isStreamComplete = false
                val streamTimeoutMs = 30_000L // 30秒超时
                val startTime = System.currentTimeMillis()

                // TODO: 需要重构为新架构 - 使用DirectOutputChannel监听完成事件
                val completionJob = kotlinx.coroutines.CoroutineScope(
                    kotlinx.coroutines.Dispatchers.IO,
                ).launch {
                    // 暂时使用简单的延迟来模拟完成
                    kotlinx.coroutines.delay(5000) // 5秒后认为完成
                    isStreamComplete = true
                }

                // 等待完成或超时
                while (!isStreamComplete && (System.currentTimeMillis() - startTime) < streamTimeoutMs) {
                    kotlinx.coroutines.delay(100) // 每100ms检查一次
                }

                completionJob.cancel() // 清理监听任务

                if (!isStreamComplete) {
                    Timber.w("⚠️ AI流式响应超时: messageId=$aiResponseId")
                    emit(
                        StreamEvent.Error(
                            sessionId = sessionId,
                            userMessageId = userMessageId,
                            aiResponseId = aiResponseId,
                            timestamp = System.currentTimeMillis(),
                            error = java.util.concurrent.TimeoutException("AI响应超时"),
                        ),
                    )
                    return@flow
                }

                Timber.d("✅ AI流式响应完成: messageId=$aiResponseId")
            } catch (e: Exception) {
                Timber.e(e, "❌ AI流式请求失败: messageId=$aiResponseId")
                emit(
                    StreamEvent.Error(
                        sessionId = sessionId,
                        userMessageId = userMessageId,
                        aiResponseId = aiResponseId,
                        timestamp = System.currentTimeMillis(),
                        error = e,
                    ),
                )
                return@flow
            }

            // 🔥 【修复】发送完成事件
            // 注意：AdaptiveStreamClient已经发布了完成Token到TokenBus
            // 这里只需要发送StreamEvent.Done给上层调用者
            emit(
                StreamEvent.Done(
                    sessionId = sessionId,
                    userMessageId = userMessageId,
                    aiResponseId = aiResponseId,
                    timestamp = System.currentTimeMillis(),
                    fullText = "AI响应已通过事件总线完成", // 实际内容由ThinkingBox处理
                ),
            )
        } catch (e: TimeoutException) {
            // 首包超时处理，包含partialContent
            Timber.e("首包超时: aiResponseId=$aiResponseId")
            val partialText = contentBuilder.toString()

            // 记录剩余RAW内容
            if (rawResponseLoggingAccumulator.isNotEmpty()) {
                val partialTimeoutChunkToLog = rawResponseLoggingAccumulator.toString().replace(
                    "\n",
                    "\\n",
                )
                rawResponseLoggingAccumulator.clear()
            }

            emit(
                StreamEvent.Error(
                    sessionId = sessionId,
                    userMessageId = userMessageId,
                    aiResponseId = aiResponseId,
                    timestamp = System.currentTimeMillis(),
                    error = e,
                    partialContent = partialText.takeIf { it.isNotEmpty() },
                ),
            )
        } catch (e: Exception) {
            // 异常处理包含partialContent
            val partialText = contentBuilder.toString()

            // 记录剩余RAW内容
            if (rawResponseLoggingAccumulator.isNotEmpty()) {
                val partialExceptionChunkToLog = rawResponseLoggingAccumulator.toString().replace(
                    "\n",
                    "\\n",
                )
                Timber.i(
                    "AI-RAW-ERROR (%d chars): %s",
                    rawResponseLoggingAccumulator.length,
                    partialExceptionChunkToLog,
                )
                rawResponseLoggingAccumulator.clear()
            }

            if (isNetworkError(e)) {
                sse5xxCount.incrementAndGet()
                Timber.e(e, "网络错误最终失败: aiResponseId=$aiResponseId")
                emit(
                    StreamEvent.Error(
                        sessionId = sessionId,
                        userMessageId = userMessageId,
                        aiResponseId = aiResponseId,
                        timestamp = System.currentTimeMillis(),
                        error = RuntimeException("网络连接失败，请稍后重试", e),
                        partialContent = partialText.takeIf { it.isNotEmpty() },
                    ),
                )
            } else {
                Timber.e(e, "流式AI请求失败: aiResponseId=$aiResponseId")
                emit(
                    StreamEvent.Error(
                        sessionId = sessionId,
                        userMessageId = userMessageId,
                        aiResponseId = aiResponseId,
                        timestamp = System.currentTimeMillis(),
                        error = RuntimeException("Unknown SSE failure", e),
                        partialContent = partialText.takeIf { it.isNotEmpty() },
                    ),
                )
            }
        }
    }.flowOn(Dispatchers.IO) // 在 IO 线程执行，避免阻塞主线程

    /**
     * 基于任务类型的流式聊天
     */
    suspend fun streamChatWithTaskType(
        request: ChatRequest,
        taskType: AiTaskType,
    ): Flow<String> = flow {
        Timber.d("开始任务类型流式聊天: taskType=$taskType")

        try {
            // TODO: 需要重构为新架构 - 使用UnifiedTokenReceiver
            // 暂时返回模拟数据以让编译通过
            Timber.tag("ARCH-REFACTOR").w("⚠️ streamChatWithTask需要重构为新架构")

            // 模拟流式响应
            emit("正在处理您的请求...")
            kotlinx.coroutines.delay(1000)
            emit("分析完成，生成回答中...")
            kotlinx.coroutines.delay(1000)
            emit("回答已生成完成。")
        } catch (e: Exception) {
            Timber.e(e, "流式聊天失败: taskType=$taskType")
            // 发送错误信息而不是固定文本
            emit("AI响应异常: ${e.message}")
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 🔥 【架构重构】简化的流式AI请求 - 委托给Core-Network
     */
    suspend fun streamChatWithMessageId(
        request: ChatRequest,
        messageId: String,
        taskType: AiTaskType,
    ): Flow<com.example.gymbro.core.network.output.OutputToken> {
        Timber.tag("SIMPLIFIED-ARCH").i("🚀 [简化架构] 委托流式AI请求: messageId=$messageId, taskType=$taskType")

        return flow<com.example.gymbro.core.network.output.OutputToken> {
            try {
                // 🔥 【架构重构】直接委托给Core-Network统一处理
                unifiedAiResponseService.processAiStreamingResponse(request, messageId)
                    .collect { processedToken ->
                        // 转换为OutputToken格式（向后兼容）
                        emit(
                            com.example.gymbro.core.network.output.OutputToken(
                                content = processedToken,
                                conversationId = messageId,
                                contentType = com.example.gymbro.core.network.detector.ContentType.JSON_SSE,
                                timestamp = System.currentTimeMillis(),
                                metadata = mapOf("source" to "unified-ai-service"),
                            )
                        )
                    }

                Timber.tag("SIMPLIFIED-ARCH").i("✅ [简化架构] 流式请求完成: messageId=$messageId")

            } catch (e: Exception) {
                Timber.e(e, "简化架构流式请求失败: messageId=$messageId, taskType=$taskType")
                // 发送错误 token 事件
                emit(
                    com.example.gymbro.core.network.output.OutputToken(
                        content = "AI响应异常: ${e.message}",
                        conversationId = messageId,
                        contentType = com.example.gymbro.core.network.detector.ContentType.PLAIN_TEXT,
                        timestamp = System.currentTimeMillis(),
                        metadata = mapOf("isComplete" to true, "error" to true),
                    ),
                )
            }
        }.flowOn(kotlinx.coroutines.Dispatchers.IO)
    }

    // 🔥 【架构重构】sendStreamingAiRequest方法已迁移到Core-Network
    // 参见: core-network/src/main/kotlin/com/example/gymbro/core/network/service/UnifiedAiResponseService.kt

    // 🔥 【架构重构】parseSseResponse方法已迁移到Core-Network
    // 参见: core-network/src/main/kotlin/com/example/gymbro/core/network/service/UnifiedAiResponseService.kt

    /**
     * 获取监控指标
     */
    fun getMetrics(): String {
        val totalCount = totalRequestCount.get()
        val sse5xxCountValue = sse5xxCount.get()
        val retryTotalCountValue = retryTotalCount.get()
        val retrySuccessCountValue = retrySuccessCount.get()

        val sse5xxRate = if (totalCount > 0) {
            (sse5xxCountValue.toDouble() / totalCount) * 100
        } else {
            0.0
        }
        val retrySuccessRate = if (retryTotalCountValue > 0) {
            (retrySuccessCountValue.toDouble() / retryTotalCountValue) * 100
        } else {
            0.0
        }
        return "sse_5xx_rate: $sse5xxRate%, retry_success_rate: $retrySuccessRate%"
    }

    // 网络错误判断辅助函数
    private fun isNetworkError(cause: Throwable): Boolean =
        cause.message?.contains("HTTP", ignoreCase = true) == true ||
            cause.message?.contains("network", ignoreCase = true) == true ||
            cause.message?.contains("connection", ignoreCase = true) == true ||
            cause.message?.contains("timeout", ignoreCase = true) == true ||
            cause::class.simpleName?.contains("Http") == true
}
