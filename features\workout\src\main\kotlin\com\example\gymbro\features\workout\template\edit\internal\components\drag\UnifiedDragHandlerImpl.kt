package com.example.gymbro.features.workout.template.edit.internal.components.drag

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

/**
 * 通用拖拽处理器实现
 * 提供标准化的拖拽状态管理和操作逻辑
 */
class UnifiedDragHandlerImpl<T> : UnifiedDragHandler<T> {
    private val _dragState = MutableStateFlow(DragState<T>())
    override val dragState: StateFlow<DragState<T>> = _dragState.asStateFlow()

    private var items: MutableList<T> = mutableListOf()

    // 开始拖拽
    override fun startDrag(item: T, position: Int) {
        _dragState.update {
            it.copy(
                isDragging = true,
                draggedItem = item,
                originalPosition = position,
                currentPosition = position
            )
        }
    }

    // 更新拖拽位置
    override fun updateDragPosition(newPosition: Int) {
        _dragState.update {
            it.copy(currentPosition = newPosition)
        }
    }

    // 完成拖拽
    override fun commitDrag(): Result<Unit> {
        return try {
            val state = _dragState.value
            if (state.isDragging &&
                state.draggedItem != null &&
                state.originalPosition != null &&
                state.currentPosition != null
            ) {
                // 移动元素
                val item = state.draggedItem
                items.removeAt(state.originalPosition)
                items.add(state.currentPosition, item)

                // 重置状态
                _dragState.value = DragState()
                Result.success(Unit)
            } else {
                Result.failure(IllegalStateException("Invalid drag state for commit"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // 取消拖拽
    override fun cancelDrag() {
        _dragState.value = DragState()
    }

    // 是否正在拖拽
    override fun isDragging(): Boolean = _dragState.value.isDragging

    // 设置可拖拽列表
    fun setItems(newItems: List<T>) {
        items = newItems.toMutableList()
    }

    // 获取当前列表
    fun getItems(): List<T> = items
}
