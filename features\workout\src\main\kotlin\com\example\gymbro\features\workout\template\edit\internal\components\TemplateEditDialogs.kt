package com.example.gymbro.features.workout.template.edit.internal.components

import androidx.compose.runtime.Composable
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.GymBroTextEditDialog
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract

/**
 * 模板编辑对话框组件 - 基于Profile模块的成功模式
 *
 * 🎯 设计原则：
 * - 使用Profile模块验证过的对话框编辑模式
 * - 遵循Clean Architecture + MVI 2.0模式
 * - 统一的用户体验和交互流程
 * - 与Profile模块PersonalInfoDialogs保持一致
 */
@Composable
internal fun TemplateEditDialogs(
    state: TemplateEditContract.State,
    onIntent: (TemplateEditContract.Intent) -> Unit,
) {
    // 模板名称编辑对话框
    if (state.showTemplateNameDialog) {
        GymBroTextEditDialog(
            show = true,
            title = UiText.DynamicString("模板名称"),
            value = state.tempTemplateName ?: state.template?.name ?: "",
            onValueChange = {
                onIntent(TemplateEditContract.Intent.UpdateTempTemplateName(it))
            },
            onDismiss = {
                onIntent(TemplateEditContract.Intent.DismissDialog)
            },
            onConfirm = {
                onIntent(TemplateEditContract.Intent.ConfirmTemplateName)
            },
            label = UiText.DynamicString("请输入模板名称"),
            maxLength = 50,
        )
    }

    // 模板描述编辑对话框
    if (state.showTemplateDescriptionDialog) {
        GymBroTextEditDialog(
            show = true,
            title = UiText.DynamicString("模板描述"),
            value = state.tempTemplateDescription ?: state.template?.description ?: "",
            onValueChange = {
                onIntent(TemplateEditContract.Intent.UpdateTempTemplateDescription(it))
            },
            onDismiss = {
                onIntent(TemplateEditContract.Intent.DismissDialog)
            },
            onConfirm = {
                onIntent(TemplateEditContract.Intent.ConfirmTemplateDescription)
            },
            label = UiText.DynamicString("请输入模板描述"),
            maxLength = 200,
        )
    }
}
