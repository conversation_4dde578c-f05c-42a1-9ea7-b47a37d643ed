package com.example.gymbro.features.workout.template.edit.internal.effect

import kotlinx.coroutines.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TemplateEdit 文本输入处理器
 *
 * 🎯 职责：
 * - 文本输入防抖处理
 * - 输入验证
 * - 输入状态管理
 *
 * 🔥 重构改进：
 * - 从ViewModel中提取文本输入逻辑
 * - 统一防抖处理
 * - 简化输入验证
 * - 优化性能
 */
@Singleton
class TemplateEditTextInputHandler @Inject constructor() {

    companion object {
        private const val TEXT_INPUT_DEBOUNCE_MS = 300L
    }

    // 防抖控制
    private var nameUpdateDebounceJob: Job? = null
    private var descriptionUpdateDebounceJob: Job? = null

    // 协程作用域
    private val handlerScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)

    /**
     * 🔥 处理模板名称更新
     * 带防抖的名称输入处理
     */
    fun handleNameUpdate(
        name: String,
        onUpdate: (String) -> Unit,
    ) {
        // 取消之前的防抖任务
        nameUpdateDebounceJob?.cancel()

        // 启动新的防抖任务
        nameUpdateDebounceJob = handlerScope.launch {
            try {
                delay(TEXT_INPUT_DEBOUNCE_MS)

                // 验证名称
                val validatedName = validateTemplateName(name)

                Timber.d("📝 模板名称更新: '$validatedName'")
                onUpdate(validatedName)
            } catch (e: CancellationException) {
                // 防抖被取消，正常情况
                Timber.d("🔄 模板名称防抖被取消")
            } catch (e: Exception) {
                Timber.e(e, "❌ 模板名称更新异常")
                // 即使出错也要更新，避免UI卡住
                onUpdate(name)
            }
        }
    }

    /**
     * 🔥 处理模板描述更新
     * 带防抖的描述输入处理
     */
    fun handleDescriptionUpdate(
        description: String,
        onUpdate: (String) -> Unit,
    ) {
        // 取消之前的防抖任务
        descriptionUpdateDebounceJob?.cancel()

        // 启动新的防抖任务
        descriptionUpdateDebounceJob = handlerScope.launch {
            try {
                delay(TEXT_INPUT_DEBOUNCE_MS)

                // 验证描述
                val validatedDescription = validateTemplateDescription(description)

                Timber.d("📝 模板描述更新: '${validatedDescription.take(50)}...'")
                onUpdate(validatedDescription)
            } catch (e: CancellationException) {
                // 防抖被取消，正常情况
                Timber.d("🔄 模板描述防抖被取消")
            } catch (e: Exception) {
                Timber.e(e, "❌ 模板描述更新异常")
                // 即使出错也要更新，避免UI卡住
                onUpdate(description)
            }
        }
    }

    /**
     * 🔥 验证模板名称
     * 简化的名称验证逻辑
     */
    private fun validateTemplateName(name: String): String {
        return name.trim().take(100) // 限制最大长度为100字符
    }

    /**
     * 🔥 验证模板描述
     * 简化的描述验证逻辑
     */
    private fun validateTemplateDescription(description: String): String {
        return description.trim().take(500) // 限制最大长度为500字符
    }

    /**
     * 🔥 取消所有防抖任务
     */
    fun cancelAllDebounce() {
        nameUpdateDebounceJob?.cancel()
        descriptionUpdateDebounceJob?.cancel()
        Timber.d("🔄 所有文本输入防抖任务已取消")
    }

    /**
     * 🔥 检查是否有待处理的输入
     */
    fun hasPendingInput(): Boolean {
        return nameUpdateDebounceJob?.isActive == true ||
            descriptionUpdateDebounceJob?.isActive == true
    }

    /**
     * 🔥 等待所有防抖任务完成
     */
    suspend fun waitForPendingInput() {
        try {
            nameUpdateDebounceJob?.join()
            descriptionUpdateDebounceJob?.join()
            Timber.d("✅ 所有文本输入防抖任务已完成")
        } catch (e: Exception) {
            Timber.e(e, "❌ 等待文本输入完成时异常")
        }
    }

    /**
     * 🔥 清理资源
     */
    fun cleanup() {
        // 取消所有防抖任务
        cancelAllDebounce()

        // 取消协程作用域
        handlerScope.cancel()

        Timber.d("🧹 TemplateEditTextInputHandler 清理完成")
    }
}
