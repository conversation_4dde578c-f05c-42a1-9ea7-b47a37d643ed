package com.example.gymbro.core.network.service

import com.example.gymbro.core.logging.GymBroLogTags
import com.example.gymbro.core.network.detector.ContentType
import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.core.network.processor.StreamingProcessor
import com.example.gymbro.core.network.rest.RestClient
import com.example.gymbro.core.network.rest.ApiResult
import com.example.gymbro.core.network.config.NetworkConfigManager
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 统一AI响应服务 - Core-Network统一接收点
 *
 * 🎯 新架构职责：
 * - 统一所有AI响应的接收和处理入口
 * - 完整的SSE解析和内容提取（从AiResponseReceiver迁移）
 * - 协议检测和智能路由
 * - 直接输出到各种消费者（ThinkingBox等）
 *
 * 🔥 架构优势：
 * - 单一职责：Core-Network专注于网络响应处理
 * - 模块解耦：Coach模块只负责请求构建
 * - 统一标准：所有AI响应都通过相同的处理管道
 */
@Singleton
class UnifiedAiResponseService @Inject constructor(
    private val restClient: RestClient,
    private val networkConfigManager: NetworkConfigManager,
    private val directOutputChannel: DirectOutputChannel,
    private val streamingProcessor: StreamingProcessor, // 🔥 【利用现有组件】使用现有的SSE解析器
) {

    companion object {
        private val TAG = GymBroLogTags.CoreNetwork.SERVICE_UNIFIED_AI
    }

    /**
     * 🔥 【新架构核心方法】统一AI流式响应处理
     *
     * 替代原有的 AiResponseReceiver.streamWithNewArchitecture()
     *
     * @param request AI请求对象
     * @param messageId 消息ID
     * @return 处理后的token流
     */
    suspend fun processAiStreamingResponse(
        request: ChatRequest,
        messageId: String
    ): Flow<String> = flow {
        val startTime = System.currentTimeMillis()
        var tokenCount = 0

        Timber.tag(TAG).i("🚀 [请求开始] messageId=$messageId, 开始统一AI响应处理")

        try {
            // 🔥 【利用现有组件】Step 1: 发送AI请求并获取SSE响应
            val sseResponse = sendAiRequest(request)

            // 🔥 【利用现有组件】Step 2: 使用现有StreamingProcessor解析SSE
            parseSseToTokenFlow(sseResponse).collect { rawToken ->
                // 使用现有的StreamingProcessor进行内容提取
                val processedToken = streamingProcessor.extractJsonSseContent(rawToken)

                if (processedToken.isNotEmpty()) {
                    tokenCount++

                    // 发送到DirectOutputChannel供消费者订阅
                    directOutputChannel.sendToken(
                        token = processedToken,
                        conversationId = messageId,
                        contentType = ContentType.JSON_SSE,
                        metadata = mapOf(
                            "source" to "unified-ai-service-integrated",
                            "timestamp" to System.currentTimeMillis()
                        )
                    )

                    // 同时emit给调用者（向后兼容）
                    emit(processedToken)
                }
            }

            val processingTime = System.currentTimeMillis() - startTime
            Timber.tag(TAG).i("✅ [请求完成] messageId=$messageId, tokens=$tokenCount, 耗时=${processingTime}ms")

        } catch (e: Exception) {
            val processingTime = System.currentTimeMillis() - startTime
            Timber.tag(TAG).e(e, "❌ [请求失败] messageId=$messageId, tokens=$tokenCount, 耗时=${processingTime}ms, 错误=${e.message}")

            // 发送错误信息到DirectOutputChannel
            directOutputChannel.sendToken(
                token = "AI响应处理失败: ${e.message}",
                conversationId = messageId,
                contentType = ContentType.PLAIN_TEXT,
                metadata = mapOf("error" to true, "source" to "unified-ai-service")
            )
            throw e
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 🔥 【迁移自AiResponseReceiver】发送AI请求
     */
    private suspend fun sendAiRequest(request: ChatRequest): String {
        val config = networkConfigManager.getCurrentConfig()
        val url = "${config.restBase}/v1/chat/completions"

        val requestBody = Json.encodeToString(ChatRequest.serializer(), request)
        val headers = mapOf(
            "Content-Type" to "application/json",
            "Authorization" to "Bearer ${config.apiKey}",
            "Accept" to "text/event-stream",
            "Cache-Control" to "no-cache"
        )

        Timber.tag(TAG).d("📡 [AI请求] 发送到: $url")

        return when (val result = restClient.post(url, requestBody, headers)) {
            is ApiResult.Success -> result.data
            is ApiResult.Error -> throw Exception("AI API请求失败: ${result.error}")
        }
    }

    /**
     * 🔥 【利用现有组件】简化的SSE行分割器
     *
     * 只负责分割SSE行，JSON解析交给StreamingProcessor处理
     */
    private fun parseSseToTokenFlow(sseResponse: String): Flow<String> = flow {
        val lines = sseResponse.split("\n")
        for (line in lines) {
            if (line.startsWith("data: ")) {
                // 🔥 【职责分离】直接emit原始SSE行，让StreamingProcessor处理JSON解析
                emit(line)
            }
        }
    }

    /**
     * 🔥 【新增】直接订阅处理结果的便捷方法
     *
     * 供消费者（如ThinkingBox）直接订阅使用
     */
    fun subscribeToProcessedTokens(messageId: String): Flow<String> {
        return directOutputChannel.subscribeToConversation(messageId)
            .map { outputToken -> outputToken.content }
    }

    /**
     * 🔥 【新增】获取服务状态
     */
    fun getServiceStatus(): ServiceStatus {
        return ServiceStatus(
            isActive = true,
            totalProcessedRequests = 0, // TODO: 添加计数器
            averageLatencyMs = 0L // TODO: 添加性能监控
        )
    }
}

/**
 * 服务状态数据类
 */
data class ServiceStatus(
    val isActive: Boolean,
    val totalProcessedRequests: Long,
    val averageLatencyMs: Long
)
