package com.example.gymbro.features.workout.template.edit.contract

import androidx.compose.runtime.Immutable
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.workout.model.template.TemplateVersion
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.shared.components.drag.DragConfig
import com.example.gymbro.features.workout.shared.components.drag.DragState
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.shared.models.workout.TemplateExerciseDto

/**
 * 模板编辑器Contract - P4阶段增强版
 *
 * 🎯 P4阶段新增功能:
 * - 拖拽排序支持
 * - SharedElements转场准备
 * - 自动保存状态管理
 * - 实时预览功能
 * - 完整的MVI 2.0架构支持
 *
 * 🏗️ 架构原则:
 * - Clean Architecture + MVI 2.0模式
 * - 四数据库架构集成
 * - designSystem主题令牌使用
 */
object TemplateEditContract {

    @Immutable
    data class State(
        // === 核心数据 ===
        val template: WorkoutTemplate? = null,
        val originalTemplate: WorkoutTemplate? = null,
        val exercises: List<TemplateExerciseDto> = emptyList(),
        val selectedExercises: List<Exercise> = emptyList(),
        val currentUserId: String = "", // 🔥 修复：用户ID由StateManager动态设置，初始为空
        // === UI状态 ===
        val isLoading: Boolean = false,
        val isSaving: Boolean = false,
        val isDeleting: Boolean = false,
        val hasUnsavedChanges: Boolean = false,
        val showPreview: Boolean = false,
        val isEditMode: Boolean = true,
        // === 统一拖拽状态管理 (重构简化) ===
        val exerciseDragState: DragState<TemplateExerciseDto> = DragState(),
        val reorderingEnabled: Boolean = true,
        // === 卡片流式布局状态 (P5新增) ===
        val cardDisplayMode: CardDisplayMode = CardDisplayMode.DETAILED,
        
        // === 增强拖拽配置 (Material3集成) ===
        val dragConfig: DragConfig = DragConfig.Material3,
        val dragStartPosition: Offset = Offset.Zero,
        val currentDragPosition: Offset = Offset.Zero,
        val shouldTriggerHaptic: Boolean = false,
        val showDropPreview: Boolean = false,
        val dropPreviewIndex: Int = -1,
        // === 快速操作状态 ===
        val showQuickActions: Boolean = false,
        val quickActionTargetId: String? = null,
        val swipeOffset: Float = 0f,
        // === 自动保存机制 (P4增强) - 🔥 默认禁用自动保存 ===
        val autoSaveState: TemplateContract.AutoSaveState = TemplateContract.AutoSaveState.Inactive,
        val lastSaveTime: Long? = null,
        val autoSaveEnabled: Boolean = false, // 🔥 修复：默认禁用自动保存，避免转圈圈问题
        val saveProgress: Float = 0f,
        // === SharedElements转场状态 (P4新增) ===
        val sharedElementsReady: Boolean = false,
        val transitionState: TransitionState = TransitionState.Idle,
        val sourceElementId: String? = null,
        // === 错误处理 ===
        val error: UiText? = null,
        val saveError: UiText? = null,
        val validationErrors: Map<String, UiText> = emptyMap(),
        // === 动作选择 ===
        val showExerciseSelector: Boolean = false,
        val exerciseSelectorMode: ExerciseSelectorMode = ExerciseSelectorMode.SINGLE,
        // === 对话框状态 ===
        val showSaveDialog: Boolean = false,
        val showDiscardDialog: Boolean = false,
        val showDeleteDialog: Boolean = false,
        val showExitDialog: Boolean = false,

        // === Profile模式对话框状态 ===
        val showTemplateNameDialog: Boolean = false,
        val showTemplateDescriptionDialog: Boolean = false,
        val tempTemplateName: String? = null,
        val tempTemplateDescription: String? = null,
        // === 表单验证 ===
        val templateName: String = "",
        val templateDescription: String = "",
        val isNameValid: Boolean = true,
        val isDescriptionValid: Boolean = true,
        // === 性能优化 ===
        val renderOptimized: Boolean = true,
        val batchUpdatesEnabled: Boolean = true,
        // === 版本控制 (Phase1新增) ===
        val versionHistory: List<TemplateVersion> = emptyList(),
        val currentVersion: Int = 1,
        val isDraft: Boolean = true,
        val isPublished: Boolean = false,
        val lastPublishedAt: Long? = null,
        val showVersionHistory: Boolean = false,
        val isCreatingVersion: Boolean = false,
        val isRestoringVersion: Boolean = false,

        // === Keypad 状态已移除 ===
        // 🔥 组件级别的完全自洽性：Keypad 逻辑已移至 WorkoutExerciseComponent 内部处理
    ) : UiState {

        /**
         * 🎯 核心状态计算方法：获取当前模板的准确状态
         * 
         * 根据模板的实际数据和状态标记，准确判断当前处于哪种状态
         */
        fun getCurrentTemplateState(): TemplateState {
            // 优先使用State中的状态，如果没有则使用Template中的状态
            val currentIsPublished = isPublished ?: template?.isPublished ?: false
            val currentIsDraft = isDraft ?: template?.isDraft ?: true
            
            // 判断是否为新建模板（未保存过）
            val isNewTemplate = template?.id.isNullOrBlank() || 
                template?.id?.startsWith("temp_") == true
            
            return when {
                // 状态1：新建模板，尚未保存过
                isNewTemplate -> TemplateState.UNSAVED_DRAFT
                
                // 状态3：已发布的模板
                currentIsPublished && !currentIsDraft -> TemplateState.PUBLISHED
                
                // 状态2：已保存的草稿
                currentIsDraft && !currentIsPublished -> TemplateState.SAVED_DRAFT
                
                // 异常状态处理：同时为草稿和已发布（理论上不应该出现）
                currentIsDraft && currentIsPublished -> {
                    // 优先认为是已发布状态
                    TemplateState.PUBLISHED
                }
                
                // 默认情况：未保存的草稿
                else -> TemplateState.UNSAVED_DRAFT
            }
        }

        /**
         * 🔥 UI 控制逻辑：判断是否应该显示"保存为草稿"按钮 - 基于状态枚举的重构版
         *
         * 业务规则：
         * - UNSAVED_DRAFT：显示草稿按钮
         * - SAVED_DRAFT：显示草稿按钮  
         * - PUBLISHED：不显示草稿按钮（已发布的模板不能退回草稿状态）
         */
        fun shouldShowSaveAsDraftButton(): Boolean {
            return when (getCurrentTemplateState()) {
                TemplateState.UNSAVED_DRAFT -> true
                TemplateState.SAVED_DRAFT -> true
                TemplateState.PUBLISHED -> false
            }
        }

        /**
         * 🔥 UI 控制逻辑：获取发布/更新按钮的文本 - 基于状态枚举的重构版
         *
         * 业务规则：
         * - UNSAVED_DRAFT：显示"发布模板"
         * - SAVED_DRAFT：显示"发布模板"  
         * - PUBLISHED：显示"更新模板"
         */
        fun getPublishButtonText(): String {
            return when (getCurrentTemplateState()) {
                TemplateState.UNSAVED_DRAFT -> "发布模板"
                TemplateState.SAVED_DRAFT -> "发布模板"
                TemplateState.PUBLISHED -> "更新模板"
            }
        }

        /**
         * 🔥 状态转换验证：检查是否可以执行保存草稿操作
         */
        fun canSaveAsDraft(): Boolean {
            return when (getCurrentTemplateState()) {
                TemplateState.UNSAVED_DRAFT -> true
                TemplateState.SAVED_DRAFT -> true
                TemplateState.PUBLISHED -> false // 已发布的模板不能退回草稿状态
            }
        }

        /**
         * 🔥 状态转换验证：检查是否可以执行发布操作
         */
        fun canPublish(): Boolean {
            // 所有状态都可以发布（新建→发布、草稿→发布、已发布→更新）
            return true
        }

        /**
         * 🔥 调试辅助：获取当前状态的描述 - 增强版
         */
        fun getStatusDescription(): String {
            val currentState = getCurrentTemplateState()
            val stateText = when (currentState) {
                TemplateState.UNSAVED_DRAFT -> "新建草稿"
                TemplateState.SAVED_DRAFT -> "已保存草稿"
                TemplateState.PUBLISHED -> "已发布模板"
            }
            
            return "$stateText (isDraft=${isDraft ?: template?.isDraft}, isPublished=${isPublished ?: template?.isPublished})"
        }

        /**
         * 🔥 Phase 4: 计算总重量（统一计算逻辑）
         * 基于 customSets 进行精确计算，确保与其他地方的计算一致
         */
        val totalWeight: Float
            get() = exercises.sumOf { exercise ->
                if (exercise.customSets.isNotEmpty()) {
                    exercise.customSets.sumOf { set ->
                        (set.targetWeight * set.targetReps).toDouble()
                    }
                } else {
                    // 向后兼容：使用基础字段
                    val weight = exercise.targetWeight ?: 0f
                    (weight * exercise.reps * exercise.sets).toDouble()
                }
            }.toFloat()

        /**
         * 🔥 Phase 4: 计算训练内容摘要（修复计算逻辑）
         * 修复：使用 customSets.size 而不是 sets，确保与实际数据一致
         */
        val workoutSummary: String
            get() {
                val totalSets = exercises.sumOf { exercise ->
                    if (exercise.customSets.isNotEmpty()) {
                        exercise.customSets.size
                    } else {
                        exercise.sets // 向后兼容
                    }
                }
                return "${exercises.size}个动作 · ${totalSets}组"
            }

        /**
         * 🔥 Phase 4: 计算预估训练时长（统一计算逻辑）
         * 基于 customSets 的休息时间和执行时间进行精确计算
         */
        val estimatedDuration: String
            get() {
                if (exercises.isEmpty()) return "0分钟"

                val totalMinutes = exercises.sumOf { exercise ->
                    if (exercise.customSets.isNotEmpty()) {
                        exercise.customSets.sumOf { set ->
                            // 每组执行时间30秒 + 休息时间
                            (30 + set.restTimeSeconds).toDouble()
                        }
                    } else {
                        // 向后兼容：使用基础字段
                        exercise.sets * (30 + exercise.restTimeSeconds).toDouble()
                    }
                } / 60.0 // 转换为分钟

                return when {
                    totalMinutes < 1 -> "< 1分钟"
                    totalMinutes < 60 -> "${totalMinutes.toInt()}分钟"
                    else -> {
                        val hours = (totalMinutes / 60).toInt()
                        val minutes = (totalMinutes % 60).toInt()
                        "${hours}小时${minutes}分钟"
                    }
                }
            }
        
        /**
         * 创建统一拖拽状态实例 (重构简化)
         * 直接返回内部统一管理的拖拽状态
         */
        fun createDragState(): com.example.gymbro.features.workout.shared.components.drag.DragState<TemplateExerciseDto> {
            return exerciseDragState.copy(
                draggedItem = exerciseDragState.draggedItemId?.let { id ->
                    exercises.find { it.id == id }
                },
                isDragEnabled = reorderingEnabled,
                canDropAt = { index -> 
                    index >= 0 && index < exercises.size && index != exerciseDragState.draggedItemIndex
                }
            )
        }
        
        /**
         * 从DragState更新当前状态 (重构简化)
         * 直接更新统一管理的拖拽状态
         */
        fun updateFromDragState(dragState: com.example.gymbro.features.workout.shared.components.drag.DragState<TemplateExerciseDto>): State {
            return copy(
                exerciseDragState = dragState,
                dragStartPosition = dragState.dragStartPosition,
                currentDragPosition = dragState.currentPosition,
                shouldTriggerHaptic = dragState.shouldTriggerHaptic,
                showDropPreview = dragState.showDropPreview,
                dropPreviewIndex = dragState.dropPreviewIndex,
                reorderingEnabled = dragState.isDragEnabled
            )
        }
    }

    sealed interface Intent : AppIntent {
        // === 模板管理 ===
        data class LoadTemplate(
            val templateId: String,
        ) : Intent

        data class SetTemplate(
            val template: WorkoutTemplate,
        ) : Intent

        data class SetCurrentUserId(
            val userId: String,
        ) : Intent // 🔥 修复：设置当前用户ID

        data class UpdateTemplateName(
            val name: String,
        ) : Intent

        data class UpdateTemplateDescription(
            val description: String,
        ) : Intent

        object SaveTemplate : Intent

        object SaveAsNewTemplate : Intent

        object DeleteTemplate : Intent

        object ResetTemplate : Intent

        // === 动作管理 ===
        data class AddExercise(
            val exercise: Exercise,
        ) : Intent

        data class AddExercises(
            val exercises: List<Exercise>,
        ) : Intent

        data class RemoveExercise(
            val exerciseId: String,
        ) : Intent

        data class UpdateExercise(
            val exercise: TemplateExerciseDto,
        ) : Intent

        data class DuplicateExercise(
            val exerciseId: String,
        ) : Intent

        // === 拖拽排序 (P4新增 - 已移动到P5卡片流式布局部分) ===
        data class ReorderExercises(
            val fromIndex: Int,
            val toIndex: Int,
        ) : Intent

        object EnableReordering : Intent

        object DisableReordering : Intent

        // === 自动保存管理 (P4增强) ===
        object TriggerAutoSave : Intent

        object EnableAutoSave : Intent

        object DisableAutoSave : Intent

        data class AutoSaveStateChanged(
            val state: TemplateContract.AutoSaveState,
        ) : Intent

        data class AutoSaveProgress(
            val progress: Float,
        ) : Intent

        object RestoreFromAutoSave : Intent

        object ClearAutoSave : Intent

        // === SharedElements转场 (P4新增) ===
        data class PrepareSharedElements(
            val sourceElementId: String,
        ) : Intent

        object StartTransition : Intent

        object CompleteTransition : Intent

        data class TransitionStateChanged(
            val state: TransitionState,
        ) : Intent

        // === UI控制 ===
        object ShowPreview : Intent

        object HidePreview : Intent

        object TogglePreview : Intent

        object ShowExerciseSelector : Intent

        object HideExerciseSelector : Intent

        data class SetExerciseSelectorMode(
            val mode: ExerciseSelectorMode,
        ) : Intent

        // === 对话框管理 ===
        object ShowSaveDialog : Intent

        object HideSaveDialog : Intent

        object ShowDiscardDialog : Intent

        object HideDiscardDialog : Intent

        object ShowDeleteDialog : Intent

        object HideDeleteDialog : Intent

        object ShowExitDialog : Intent

        object HideExitDialog : Intent

        object ConfirmSave : Intent

        object ConfirmDiscard : Intent

        object ConfirmDelete : Intent

        object ConfirmExit : Intent

        // === 错误处理 ===
        object ClearError : Intent

        object ClearSaveError : Intent

        data class ClearValidationError(
            val field: String,
        ) : Intent

        object ClearAllErrors : Intent

        data class HandleError(
            val error: UiText,
        ) : Intent

        // === 导航 ===
        object NavigateBack : Intent

        object NavigateToPreview : Intent

        data class NavigateToExerciseDetails(
            val exerciseId: String,
        ) : Intent

        object NavigateToExerciseLibrary : Intent

        object ResetNavigationState : Intent

        // === 性能优化 ===
        object EnableRenderOptimization : Intent

        object DisableRenderOptimization : Intent

        object EnableBatchUpdates : Intent

        object DisableBatchUpdates : Intent

        object FlushBatchUpdates : Intent

        // === 版本控制 (Phase1新增) ===
        object CreateVersion : Intent

        data class CreateVersionWithDescription(
            val description: String,
        ) : Intent

        object PublishTemplate : Intent

        object SaveAsDraft : Intent

        // === 模板发布结果处理 ===
        object PublishCompleted : Intent

        object CreateAndSaveImmediately : Intent

        object LoadVersionHistory : Intent

        data class RestoreFromVersion(
            val versionId: String,
        ) : Intent

        object ShowVersionHistory : Intent

        object HideVersionHistory : Intent

        // === 模板基础信息编辑相关 (新增) ===
        object ShowTemplateNameDialog : Intent

        object ShowTemplateDescriptionDialog : Intent

        data class UpdateTempTemplateName(
            val name: String,
        ) : Intent

        data class UpdateTempTemplateDescription(
            val description: String,
        ) : Intent

        object ConfirmTemplateName : Intent

        object ConfirmTemplateDescription : Intent

        object DismissDialog : Intent

        data class SetVersionDescription(
            val description: String,
        ) : Intent

        data class SetVersionHistory(
            val versions: List<TemplateVersion>,
        ) : Intent

        data class VersionCreated(
            val version: TemplateVersion,
        ) : Intent

        object SaveSuccess : Intent

        object DraftSaved : Intent

        // === 新增缺失的Intent ===
        data class CreateEmptyTemplate(
            val templateId: String,
        ) : Intent

        // === 卡片流式布局Intent (P5新增) ===

        // === 拖拽排序Intent (P5增强) ===
        data class StartDrag(
            val exerciseId: String,
            val startIndex: Int,
        ) : Intent

        data class UpdateDragPosition(
            val targetIndex: Int,
            val offset: Float,
        ) : Intent

        data class CompleteDrag(
            val fromIndex: Int,
            val toIndex: Int,
        ) : Intent

        object CancelDrag : Intent
        
        // === 增强拖拽Intent (Material3集成) ===
        data class StartDragWithPosition(
            val exerciseId: String,
            val startIndex: Int,
            val startPosition: Offset
        ) : Intent
        
        data class UpdateDragPositionWithCoordinates(
            val targetIndex: Int,
            val currentPosition: Offset
        ) : Intent
        
        data class TriggerDragHaptic(
            val hapticType: HapticFeedbackType = HapticFeedbackType.LongPress
        ) : Intent
        
        data class UpdateDragPreview(
            val showPreview: Boolean,
            val previewIndex: Int = -1
        ) : Intent
        
        data class SetDragConfig(
            val config: DragConfig
        ) : Intent

        // === 快速操作Intent (P5新增) ===
        data class ShowQuickActions(
            val exerciseId: String,
        ) : Intent

        object HideQuickActions : Intent

        data class QuickDuplicateExercise(
            val exerciseId: String,
        ) : Intent

        data class QuickDeleteExercise(
            val exerciseId: String,
        ) : Intent

        // === Keypad 相关 Intent 已移除 ===
        // 🔥 组件级别的完全自洽性：Keypad Intent 已移至 WorkoutExerciseComponent 内部处理
    }

    sealed interface Effect : UiEffect {
        // === 导航副作用 ===
        object NavigateBack : Effect

        object NavigateBackInternal : Effect // 新增：内部导航，保存后触发

        object PrepareToExit : Effect // 新增：准备退出，触发保存检查

        object NavigateToPreview : Effect

        data class NavigateToExerciseDetails(
            val exerciseId: String,
        ) : Effect

        object NavigateToExerciseLibrary : Effect

        data class NavigateToTemplateDetails(
            val templateId: String,
        ) : Effect

        // === UI反馈副作用 ===
        data class ShowToast(
            val message: UiText,
        ) : Effect

        data class ShowSnackbar(
            val message: UiText,
            val actionLabel: String? = null,
            val action: (() -> Unit)? = null,
        ) : Effect

        data class TriggerHapticFeedback(
            val type: HapticFeedbackType,
        ) : Effect

        // === 动画副作用 (P4新增) ===
        data class AnimateItemRemoval(
            val itemId: String,
        ) : Effect

        data class AnimateItemAddition(
            val itemId: String,
        ) : Effect

        data class AnimateItemReorder(
            val fromIndex: Int,
            val toIndex: Int,
        ) : Effect

        data class AnimateSharedElements(
            val sourceId: String,
            val targetId: String,
        ) : Effect
        
        // === 增强拖拽动画副作用 (Material3集成) ===
        data class AnimateDragStart(
            val itemId: String,
            val config: DragConfig = DragConfig.Material3
        ) : Effect
        
        data class AnimateDragMove(
            val itemId: String,
            val fromPosition: Offset,
            val toPosition: Offset
        ) : Effect
        
        data class AnimateDragComplete(
            val itemId: String,
            val fromIndex: Int,
            val toIndex: Int,
            val success: Boolean
        ) : Effect
        
        data class AnimateDragCancel(
            val itemId: String,
            val returnToOriginalPosition: Boolean = true
        ) : Effect
        
        data class TriggerDragHapticFeedback(
            val type: HapticFeedbackType,
            val condition: String = "drag_interaction"
        ) : Effect

        // === 自动保存副作用 ===
        object TriggerAutoSave : Effect

        data class ShowAutoSaveStatus(
            val state: TemplateContract.AutoSaveState,
        ) : Effect

        object RestoreFromAutoSave : Effect

        // === 系统副作用 ===
        data class ShareTemplate(
            val template: WorkoutTemplate,
        ) : Effect

        data class ExportTemplate(
            val template: WorkoutTemplate,
        ) : Effect

        object RequestStoragePermission : Effect

        // === 对话框副作用 ===
        object ShowUnsavedChangesDialog : Effect

        data class ShowDeleteConfirmDialog(
            val templateName: String,
        ) : Effect

        object ShowExitConfirmDialog : Effect

        // === 错误处理副作用 ===
        data class ShowError(
            val message: UiText,
        ) : Effect

        data class ShowValidationError(
            val field: String,
            val message: UiText,
        ) : Effect

        object ClearAllErrors : Effect

        // === 版本控制副作用 (Phase1新增) ===
        object ShowVersionCreated : Effect

        object ShowVersionRestored : Effect

        object ShowTemplatePublished : Effect

        object ShowDraftSaved : Effect

        data class ShowVersionHistory(
            val versions: List<TemplateVersion>,
        ) : Effect

        data class ShowVersionDescription(
            val description: String,
        ) : Effect

        // === 新增缺失的Effect ===
        data class LoadTemplateData(
            val templateId: String,
        ) : Effect

        object LoadVersionHistory : Effect

        data class SaveTemplate(
            val template: WorkoutTemplate,
        ) : Effect

        // === P1-P2: 新增简化的保存Effect ===
        object SaveAsDraft : Effect

        object PublishTemplate : Effect

        object CreateAndSaveImmediately : Effect

        data class SaveAsNewTemplate(
            val template: WorkoutTemplate,
        ) : Effect

        data class DeleteTemplate(
            val templateId: String,
        ) : Effect

        data class CreateVersion(
            val description: String,
        ) : Effect

        data class RestoreFromVersion(
            val versionId: String,
        ) : Effect

        // === 模板基础信息编辑相关 (新增) ===
        data class SaveTemplateBasicInfo(
            val name: String,
            val description: String,
        ) : Effect
    }

    // === 辅助数据类 ===

    /**
     * 模板状态枚举 - 三种明确的状态定义
     * 
     * 🎯 状态转换规则：
     * - UNSAVED_DRAFT → SAVED_DRAFT：通过 SaveAsDraft 操作
     * - SAVED_DRAFT → PUBLISHED：通过 PublishTemplate 操作
     * - PUBLISHED ↛ SAVED_DRAFT：已发布的模板不能退回草稿状态（单向转换）
     */
    enum class TemplateState {
        /**
         * 状态1：新建草稿/模板，还没有保存过的内容
         * - 特征：新建的模板，尚未执行任何保存操作
         * - UI显示：显示"保存草稿" + "发布模板"按钮
         * - Tab位置：不在任何Tab中（编辑状态）
         */
        UNSAVED_DRAFT,

        /**
         * 状态2：保存为草稿的状态  
         * - 特征：isDraft=true, isPublished=false
         * - UI显示：显示"保存草稿" + "发布模板"按钮
         * - Tab位置：显示在草稿Tab中
         */
        SAVED_DRAFT,

        /**
         * 状态3：保存为模板的状态
         * - 特征：isDraft=false, isPublished=true
         * - UI显示：只显示"更新模板"按钮（隐藏草稿按钮）
         * - Tab位置：显示在模板Tab中
         */
        PUBLISHED
    }

    enum class ExerciseSelectorMode {
        SINGLE,
        MULTIPLE,
    }

    /**
     * 卡片显示模式 (P5新增)
     */
    enum class CardDisplayMode {
        DETAILED, // 单列详细模式
    }

    /**
     * SharedElements转场状态 (P4新增)
     */
    enum class TransitionState {
        Idle, // 空闲状态
        Preparing, // 准备转场
        InProgress, // 转场进行中
        Completed, // 转场完成
    }

    /**
     * 拖拽状态信息 (已迁移到统一拖拽组件)
     * @deprecated 使用 com.example.gymbro.features.workout.shared.components.drag.DragState<T>
     */
    @Deprecated("使用统一拖拽组件 DragState<T>", ReplaceWith("com.example.gymbro.features.workout.shared.components.drag.DragState<TemplateExerciseDto>"))
    data class DragState(
        val isDragging: Boolean = false,
        val draggedItemId: String? = null,
        val draggedItemIndex: Int = -1,
        val dropTargetIndex: Int = -1,
        val dragOffset: Float = 0f,
    )

    /**
     * 验证结果
     */
    data class ValidationResult(
        val isValid: Boolean,
        val errors: Map<String, UiText> = emptyMap(),
    )

    /**
     * 自动保存配置
     */
    data class AutoSaveConfig(
        val enabled: Boolean = true,
        val intervalMs: Long = 30_000L, // 30秒
        val maxRetries: Int = 3,
        val retryDelayMs: Long = 5_000L, // 5秒
    )

    // === 常量定义 ===
    object Constants {
        const val MIN_TEMPLATE_NAME_LENGTH = 1
        const val MAX_TEMPLATE_NAME_LENGTH = 100
        const val MAX_TEMPLATE_DESCRIPTION_LENGTH = 500
        const val MAX_EXERCISES_PER_TEMPLATE = 50
        const val AUTO_SAVE_INTERVAL_MS = 30_000L
        const val DRAG_THRESHOLD_DP = 16f
        const val ANIMATION_DURATION_MS = 300
    }
}
