package com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel

import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingBoxReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ThinkingBoxViewModel - 标准MVI架构 ViewModel
 *
 * 🎯 核心职责：
 * - 继承BaseMviViewModel实现标准MVI模式
 * - 使用ThinkingBoxReducer管理状态转换
 * - 协调Token流解析和状态更新
 * - 处理副作用Effect的分发和执行
 * - 支持完整的生命周期管理
 *
 * 🔥 架构特点：
 * - 标准MVI 2.0架构，符合项目规范
 * - 单向数据流：UI → Intent → Reducer → State → UI
 * - Effect独立处理副作用，不影响State流
 * - 集成DomainMapper和StreamingParser
 * - 支持History写入通过Effect分发
 */
@HiltViewModel
class ThinkingBoxViewModel @Inject constructor(
    private val thinkingBoxReducer: ThinkingBoxReducer,
    private val domainMapper: DomainMapper,
    private val streamingParser: StreamingThinkingMLParser,
    private val directOutputChannel: DirectOutputChannel,
) : BaseMviViewModel<ThinkingBoxContract.Intent, ThinkingBoxContract.State, ThinkingBoxContract.Effect>(
    initialState = ThinkingBoxContract.State(),
) {

    override val reducer:
        Reducer<ThinkingBoxContract.Intent, ThinkingBoxContract.State, ThinkingBoxContract.Effect> =
        thinkingBoxReducer

    // 内部状态管理
    private var mappingContext = DomainMapper.MappingContext()

    // 解析任务
    private var parseJob: Job? = null

    init {
        Timber.tag("TB-VM").d("🚀 [ViewModel初始化] ThinkingBoxViewModel启动")
        initializeEffectHandler()
    }

    /**
     * 初始化Effect处理器
     */
    override fun initializeEffectHandler() {
        viewModelScope.launch {
            effect.collect { effect ->
                handleEffect(effect)
            }
        }
    }

    /**
     * Effect处理器 - 处理所有副作用
     */
    private suspend fun handleEffect(effect: ThinkingBoxContract.Effect) {
        Timber.tag("TB-EFFECT").d("⚡ [Effect处理] ${effect::class.simpleName}")

        when (effect) {
            is ThinkingBoxContract.Effect.StartTokenStreamListening -> {
                startTokenStreamListening(effect.messageId)
            }
            is ThinkingBoxContract.Effect.ScrollToBottom -> {
                // UI层会监听并执行滚动
            }
            is ThinkingBoxContract.Effect.CloseThinkingBox -> {
                // UI层会监听并关闭思考框
            }
            is ThinkingBoxContract.Effect.NotifyHistoryThinking -> {
                // UI层会监听并写入思考历史
            }
            is ThinkingBoxContract.Effect.NotifyHistoryFinal -> {
                // UI层会监听并写入最终答案历史
            }
            is ThinkingBoxContract.Effect.ShowError -> {
                // UI层会监听并显示错误
            }
            is ThinkingBoxContract.Effect.LogDebug -> {
                Timber.tag("TB-DEBUG").d("${effect.message}")
            }
        }
    }

    // 便捷方法供UI层调用
    fun initialize(messageId: String, conversationId: String = "") =
        dispatch(ThinkingBoxContract.Intent.Initialize(messageId, conversationId))
    fun onSegmentRendered(
        segmentId: String,
    ) = dispatch(ThinkingBoxContract.Intent.UiSegmentRendered(segmentId))
    fun reset() = dispatch(ThinkingBoxContract.Intent.Reset)
    fun clearError() = dispatch(ThinkingBoxContract.Intent.ClearError)

    /**
     * 启动Token流监听 - MVI架构版本（架构优化）
     *
     * 🔥 【架构优化】移除对DirectOutputChannel的直接订阅，避免重复订阅
     * 改为通过ThinkingBoxStreamAdapter作为单一订阅点，ViewModel只负责状态管理
     */
    private fun startTokenStreamListening(messageId: String) {
        Timber.tag("TB-STREAM").i("🎯 [Token流启动] messageId=$messageId")

        // 取消之前的解析任务
        parseJob?.cancel()

        parseJob = viewModelScope.launch {
            try {
                Timber.tag("TB-STREAM").i("✅ [委托处理] 通过StreamAdapter处理: messageId=$messageId")

                // 🔥 【架构优化】不再直接订阅DirectOutputChannel
                // ThinkingBoxStreamAdapter会作为单一订阅点，并通过ViewModelProvider直接调用ViewModel方法
                // 这里只需要等待ThinkingEvent的到来，实际的token处理由StreamAdapter负责

                // 重置映射上下文（如果StreamAdapter需要的话，这个逻辑可能需要移到StreamAdapter中）
                mappingContext = DomainMapper.MappingContext()

                Timber.tag("TB-STREAM").i("✅ [等待事件] ViewModel准备接收ThinkingEvent: messageId=$messageId")

                // 注意：实际的token流处理现在由ThinkingBoxStreamAdapter负责
                // ViewModel通过processThinkingEvent方法接收处理后的事件
                // 这个方法现在主要用于初始化状态，实际的数据流由StreamAdapter管理

            } catch (e: Exception) {
                Timber.tag("TB-ERROR").e(e, "❌ [Token流启动失败] messageId=$messageId")
                sendEffect(
                    ThinkingBoxContract.Effect.ShowError(
                        com.example.gymbro.core.ui.text.UiText.DynamicString("Token流启动失败: ${e.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 处理ThinkingEvent - MVI架构版本
     *
     * 🔥 【架构优化】现在作为公共方法，供ThinkingBoxStreamAdapter直接调用
     */
    fun processThinkingEvent(
        event: com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent,
    ) {
        // 使用ThinkingBoxReducer处理事件
        val result = thinkingBoxReducer.handleThinkingEvent(event, currentState)

        // 更新状态
        updateState { result.newState }

        // 分发Effects
        result.effects.forEach { effect ->
            sendEffect(effect)
        }
    }

    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        parseJob?.cancel()
        Timber.d("TB-VM: 🧹 ThinkingBoxViewModel 清理完成")
    }
}
