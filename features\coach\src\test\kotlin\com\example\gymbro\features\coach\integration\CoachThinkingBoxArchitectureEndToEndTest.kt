package com.example.gymbro.features.coach.integration

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.repository.ChatRepository
import com.example.gymbro.domain.thinkingbox.service.ThinkingBoxCompletionListener
import com.example.gymbro.domain.thinkingbox.service.ThinkingBoxContext
import com.example.gymbro.domain.thinkingbox.service.ThinkingBoxError
import com.example.gymbro.domain.thinkingbox.service.ThinkingBoxLauncher
import com.example.gymbro.domain.thinkingbox.service.ThinkingBoxRequest
import com.example.gymbro.domain.thinkingbox.service.UserProfileContext
import com.example.gymbro.features.coach.aicoach.internal.service.CoachCompletionListenerImpl
import com.example.gymbro.features.thinkingbox.internal.service.ThinkingBoxLauncherImpl
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * Coach-ThinkingBox架构重构端到端测试
 *
 * 🎯 测试目标：
 * - 验证新的职责分离架构正确工作
 * - 测试完整的数据流：Coach → ThinkingBoxLauncher → ThinkingBox → 完成回调 → Coach
 * - 确保接口解耦和回调机制正常
 * - 验证错误处理和边界情况
 *
 * 🔄 新架构数据流：
 * ```
 * 用户输入 → Coach保存用户消息 → ThinkingBoxLauncher启动 →
 * Core-Network处理 → ThinkingBox自主显示 → 完成回调Coach保存结果
 * ```
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DisplayName("Coach-ThinkingBox架构重构端到端测试")
class CoachThinkingBoxArchitectureEndToEndTest {

    @MockK
    private lateinit var mockChatRepository: ChatRepository

    @MockK
    private lateinit var mockAICoachRepository: com.example.gymbro.domain.coach.repository.AICoachRepository

    private lateinit var thinkingBoxLauncher: ThinkingBoxLauncher
    private lateinit var completionListener: ThinkingBoxCompletionListener

    @BeforeEach
    fun setup() {
        MockKAnnotations.init(this)

        // 创建真实的实现类进行集成测试
        thinkingBoxLauncher = ThinkingBoxLauncherImpl(
            aiCoachRepository = mockAICoachRepository,
            ioDispatcher = kotlinx.coroutines.Dispatchers.Unconfined,
        )

        completionListener = CoachCompletionListenerImpl(
            chatRepository = mockChatRepository,
            ioDispatcher = kotlinx.coroutines.Dispatchers.Unconfined,
        )
    }

    @Test
    @DisplayName("【端到端】正常AI响应处理流程")
    fun `given valid user message, when processing through new architecture, then should complete successfully`() = runTest {
        // Given - 准备测试数据
        val sessionId = "test-session-123"
        val messageId = "ai-response-456"
        val userMessage = "帮我制定一个训练计划"
        val expectedFinalContent = "这是AI生成的训练计划内容..."

        val request = ThinkingBoxRequest(
            sessionId = sessionId,
            messageId = messageId,
            userMessage = userMessage,
            context = ThinkingBoxContext(
                userProfile = UserProfileContext(
                    gender = "男",
                    age = 25,
                    experience = "初学者",
                ),
            ),
        )

        // Mock AI响应流
        coEvery {
            mockAICoachRepository.getStreamingResponse(any(), any())
        } returns kotlinx.coroutines.flow.flowOf("token1", "token2", "final")

        // Mock 保存操作
        coEvery {
            mockChatRepository.saveMessage(any())
        } returns ModernResult.Success(Unit)

        // When - 启动AI处理
        val launchResult = thinkingBoxLauncher.startAiProcessing(
            request = request,
            completionListener = completionListener,
        )

        // Then - 验证启动成功
        assertTrue(launchResult is ModernResult.Success, "ThinkingBox启动应该成功")

        // 模拟ThinkingBox完成处理并回调
        val callbackResult = completionListener.onThinkingCompleted(
            sessionId = sessionId,
            messageId = messageId,
            finalContent = expectedFinalContent,
        )

        // 验证回调成功
        assertTrue(callbackResult is ModernResult.Success, "完成回调应该成功")

        // 验证保存操作被调用
        coVerify { mockChatRepository.saveMessage(any()) }
    }

    @Test
    @DisplayName("【端到端】AI处理失败流程")
    fun `given AI processing failure, when error occurs, then should handle gracefully`() = runTest {
        // Given - 准备失败场景
        val sessionId = "test-session-123"
        val messageId = "ai-response-456"
        val userMessage = "测试消息"
        val errorMessage = "网络连接失败"

        val request = ThinkingBoxRequest(
            sessionId = sessionId,
            messageId = messageId,
            userMessage = userMessage,
        )

        // Mock AI请求失败
        coEvery {
            mockAICoachRepository.getStreamingResponse(any(), any())
        } throws RuntimeException(errorMessage)

        // Mock 错误消息保存
        coEvery {
            mockChatRepository.saveMessage(any())
        } returns ModernResult.Success(Unit)

        // When - 启动AI处理（应该失败）
        val launchResult = thinkingBoxLauncher.startAiProcessing(
            request = request,
            completionListener = completionListener,
        )

        // 模拟失败回调
        val error = ThinkingBoxError.AiRequestFailed(errorMessage)
        val failureResult = completionListener.onThinkingFailed(
            sessionId = sessionId,
            messageId = messageId,
            error = error,
        )

        // Then - 验证错误处理
        assertTrue(failureResult is ModernResult.Success, "失败回调应该成功处理")

        // 验证错误消息被保存
        coVerify { mockChatRepository.saveMessage(any()) }
    }

    @Test
    @DisplayName("【端到端】用户取消处理流程")
    fun `given user cancellation, when cancel requested, then should stop processing`() = runTest {
        // Given - 准备取消场景
        val messageId = "ai-response-456"

        // When - 取消AI处理
        val cancelResult = thinkingBoxLauncher.cancelAiProcessing(messageId)

        // Then - 验证取消结果
        // 注意：如果没有正在进行的处理，取消操作应该返回错误
        assertTrue(
            cancelResult is ModernResult.Error,
            "取消不存在的处理应该返回错误",
        )
    }

    @Test
    @DisplayName("【端到端】处理状态查询")
    fun `given processing status query, when checking status, then should return correct state`() = runTest {
        // Given - 准备状态查询
        val messageId = "ai-response-456"

        // When - 查询处理状态
        val statusResult = thinkingBoxLauncher.getProcessingStatus(messageId)

        // Then - 验证状态查询
        assertTrue(statusResult is ModernResult.Success, "状态查询应该成功")
        assertEquals(
            com.example.gymbro.domain.thinkingbox.service.ThinkingBoxStatus.Idle::class,
            statusResult.data::class,
            "未启动的处理状态应该是Idle",
        )
    }

    @Test
    @DisplayName("【架构验证】职责分离正确性")
    fun `given new architecture, when analyzing responsibilities, then should be properly separated`() = runTest {
        // Given - 新架构组件
        val launcher = thinkingBoxLauncherImpl
        val listener = coachCompletionListenerImpl

        // When - 分析职责
        val launcherResponsibilities = listOf(
            "启动AI处理",
            "管理处理状态",
            "取消AI处理",
            "调用AICoachRepository",
        )

        val listenerResponsibilities = listOf(
            "接收完成回调",
            "保存AI响应",
            "处理失败回调",
            "更新会话状态",
        )

        // Then - 验证职责分离
        assertTrue(launcherResponsibilities.isNotEmpty(), "ThinkingBoxLauncher应该有明确的职责")
        assertTrue(listenerResponsibilities.isNotEmpty(), "CompletionListener应该有明确的职责")

        // 验证接口解耦
        assertTrue(launcher is ThinkingBoxLauncher, "应该通过接口进行交互")
        assertTrue(listener is ThinkingBoxCompletionListener, "应该通过接口进行交互")
    }

    @Test
    @DisplayName("【性能验证】响应时间要求")
    fun `given performance requirements, when measuring response times, then should meet targets`() = runTest {
        // Given - 性能要求
        val maxLaunchTime = 100L // 毫秒
        val maxCallbackTime = 50L // 毫秒

        val request = ThinkingBoxRequest(
            sessionId = "perf-test",
            messageId = "perf-msg",
            userMessage = "性能测试",
        )

        // Mock快速响应
        coEvery {
            mockAICoachRepository.getStreamingResponse(any(), any())
        } returns kotlinx.coroutines.flow.flowOf("fast-token")

        coEvery {
            mockChatRepository.saveMessage(any())
        } returns ModernResult.Success(Unit)

        // When - 测量启动时间
        val launchStartTime = System.currentTimeMillis()
        val launchResult = thinkingBoxLauncher.startAiProcessing(request, completionListener)
        val launchEndTime = System.currentTimeMillis()

        // 测量回调时间
        val callbackStartTime = System.currentTimeMillis()
        val callbackResult = completionListener.onThinkingCompleted(
            sessionId = "perf-test",
            messageId = "perf-msg",
            finalContent = "快速响应",
        )
        val callbackEndTime = System.currentTimeMillis()

        // Then - 验证性能
        val launchTime = launchEndTime - launchStartTime
        val callbackTime = callbackEndTime - callbackStartTime

        assertTrue(launchTime <= maxLaunchTime, "启动时间应该 ≤ ${maxLaunchTime}ms，实际：${launchTime}ms")
        assertTrue(callbackTime <= maxCallbackTime, "回调时间应该 ≤ ${maxCallbackTime}ms，实际：${callbackTime}ms")
    }

    // 辅助属性用于测试
    private val thinkingBoxLauncherImpl get() = thinkingBoxLauncher as ThinkingBoxLauncherImpl
    private val coachCompletionListenerImpl get() = completionListener as CoachCompletionListenerImpl
}
