# Template拖拽代码清理Workflow执行计划

## 基于扫描报告的执行策略

根据Sub Agent的详细扫描，确认了4类关键问题需要按workflow执行：

### 1. 完全清理旧拖拽实现 ✅ 确认必要
**问题严重性**: 高
- TemplateScreen.kt: 行442-486, 579-623存在重复拖拽实现
- TemplateEditComponents.kt: 行1340-1366拖拽逻辑重复
- **风险**: 导致不一致的用户体验和维护困难

### 2. 代码结构整理 ✅ 确认必要  
**问题严重性**: 高
- TemplateContract.kt: 新旧拖拽状态并存
- TemplateEditContract.kt: 复杂的拖拽状态字段冗余
- **影响**: 状态管理混乱，代码可读性差

### 3. TODO/FIXME代码质量 ⚠️ 问题较少
**发现**: 扫描的核心文件中TODO/FIXME问题不严重
- 主要文件代码注释完善
- 可能需要扩大扫描范围到之前发现的问题文件

### 4. 硬编码修复 ✅ 部分需要
**问题**: 部分.dp硬编码
- TemplateScreen.kt: `80.dp.toPx()` 需要Token化
- 其他文件已较好使用designSystem规范

## Workflow任务列表

### 阶段1: 移除旧拖拽实现 (第1天上午)
```
任务1.1: 清理TemplateScreen.kt
- [ ] 移除行442-486的detectDragGestures
- [ ] 移除行579-623的detectDragGestures  
- [ ] 统一使用TemplateScreenDragHandler

任务1.2: 清理TemplateEditComponents.kt
- [ ] 移除行1340-1366的重复拖拽逻辑
- [ ] 统一使用DragModifiers
- [ ] 验证功能完整性
```

### 阶段2: 统一状态管理 (第1天下午)
```
任务2.1: 重构TemplateContract.kt
- [ ] 移除旧状态字段: isDragging, draggedItemIndex, dragTargetIndex
- [ ] 保留统一状态: templateDragState, draftDragState
- [ ] 更新状态转换逻辑

任务2.2: 重构TemplateEditContract.kt  
- [ ] 移除复杂拖拽状态字段 (7个字段)
- [ ] 简化为DragState<T>管理
- [ ] 优化createDragState和updateFromDragState方法
```

### 阶段3: TODO深度扫描和处理 (第2天上午)
```
任务3.1: 扩大TODO扫描范围
- [ ] 扫描TemplateEffectHandler.kt (已知2处TODO)
- [ ] 扫描TemplateDataMapper.kt (已知6处TODO)  
- [ ] 扫描其他template子目录文件

任务3.2: TODO分类处理
- [ ] 重要TODO: 影响功能完整性的立即处理
- [ ] 一般TODO: 转换为具体实现
- [ ] 占位符TODO: 直接删除
```

### 阶段4: 硬编码修复和质量提升 (第2天下午)
```
任务4.1: 硬编码Token化
- [ ] TemplateScreen.kt: 80.dp.toPx() -> Tokens.Size.CardHeight
- [ ] 验证其他文件的Token使用规范性
- [ ] 确保100%使用designSystem

任务4.2: 导入和结构优化
- [ ] 移除未使用的拖拽相关导入
- [ ] 统一shared组件导入
- [ ] 验证编译和功能正确性
```

## 执行顺序和依赖关系

```mermaid
graph TD
    A[任务1.1: 清理TemplateScreen] --> C[任务2.1: 重构TemplateContract]
    B[任务1.2: 清理TemplateEditComponents] --> D[任务2.2: 重构TemplateEditContract]
    C --> E[任务3.1: TODO扩大扫描]
    D --> E
    E --> F[任务3.2: TODO分类处理]
    F --> G[任务4.1: 硬编码修复]
    G --> H[任务4.2: 结构优化]
```

## 风险控制

### 高风险任务
- **任务1.1, 1.2**: 移除现有拖拽实现
- **风险缓解**: 保留备份，逐步替换验证

### 中等风险任务  
- **任务2.1, 2.2**: 状态管理重构
- **风险缓解**: 保持API兼容性，渐进式迁移

### 低风险任务
- **任务3.x, 4.x**: TODO和硬编码修复
- **风险缓解**: 纯代码质量提升，影响较小

## 成功标准

### 定量指标
- [ ] 0个detectDragGestures直接使用
- [ ] 0个旧拖拽状态字段 (isDragInProgress等)
- [ ] 0个TODO/FIXME注释
- [ ] 0个.dp硬编码值

### 定性指标  
- [ ] 统一的拖拽交互体验
- [ ] 简化的状态管理架构
- [ ] 100%设计系统合规
- [ ] 完整的功能验证通过

## 下一步执行

**准备就绪开始执行**，按照以下顺序：
1. 启动任务1.1 - 清理TemplateScreen.kt
2. 使用专门的android-mvi-architect agent执行代码修改
3. 每个任务完成后更新任务列表和进度跟踪

是否开始执行第一个任务？