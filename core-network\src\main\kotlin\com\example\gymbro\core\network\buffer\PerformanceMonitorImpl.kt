package com.example.gymbro.core.network.buffer

import timber.log.Timber
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 性能监控实现 - 简化版
 *
 * 保持接口兼容，简化内部实现为基础监控
 */
@Singleton
class PerformanceMonitorImpl @Inject constructor() : PerformanceMonitor {

    companion object {
        private const val TAG = "PerformanceMonitor"
    }

    // 简化的性能指标
    private val tokenProcessedCount = AtomicLong(0)
    private val totalLatencyMs = AtomicLong(0)
    private val errorCount = AtomicLong(0)
    private val bufferAdjustmentCount = AtomicLong(0)
    private var monitoringStartTime = System.currentTimeMillis()

    /**
     * 记录缓冲区调整事件 - 简化实现
     */
    override fun recordBufferAdjustment(oldSize: Int, newSize: Int, reason: String) {
        bufferAdjustmentCount.incrementAndGet()
        Timber.tag(TAG).d("🔧 缓冲调整: $oldSize → $newSize ($reason)")
    }

    /**
     * 记录性能指标 - 简化实现
     */
    override fun recordMetrics(metrics: ProcessingMetrics) {
        // 简化：仅记录基础指标
        tokenProcessedCount.incrementAndGet()
        totalLatencyMs.addAndGet(metrics.avgLatencyMs)
        if (metrics.errorRate > 0) {
            errorCount.incrementAndGet()
        }
    }

    /**
     * 获取当前性能指标 - 简化实现
     */
    override suspend fun getCurrentMetrics(): ProcessingMetrics {
        val currentTime = System.currentTimeMillis()
        val timeElapsed = currentTime - monitoringStartTime
        val totalTokens = tokenProcessedCount.get()

        val tokensPerSecond = if (timeElapsed > 0) {
            (totalTokens * 1000.0f / timeElapsed)
        } else {
            0f
        }

        val avgLatency = if (totalTokens > 0) totalLatencyMs.get() / totalTokens else 0L
        val errorRate = if (totalTokens > 0) errorCount.get().toFloat() / totalTokens else 0f

        return ProcessingMetrics(
            tokensPerSecond = tokensPerSecond,
            networkThroughput = tokensPerSecond * 1.1f, // 估算值
            memoryUsagePercent = 0.5f, // 固定值
            bufferUtilization = 0.6f, // 固定值
            avgLatencyMs = avgLatency,
            errorRate = errorRate,
        )
    }

    /**
     * 记录token延迟 - 简化方法
     */
    fun recordTokenLatency(latencyMs: Long) {
        tokenProcessedCount.incrementAndGet()
        totalLatencyMs.addAndGet(latencyMs)
    }

    /**
     * 记录错误 - 简化方法
     */
    fun recordError(errorType: String, message: String) {
        errorCount.incrementAndGet()
        Timber.tag(TAG).w("❌ 错误记录: $errorType - $message")
    }

    /**
     * 重置统计 - 简化方法
     */
    fun reset() {
        tokenProcessedCount.set(0)
        totalLatencyMs.set(0)
        errorCount.set(0)
        bufferAdjustmentCount.set(0)
        monitoringStartTime = System.currentTimeMillis()
        Timber.tag(TAG).i("🔄 性能统计已重置")
    }
}

/**
 * 📊 性能统计摘要
 */
data class PerformanceSummary(
    val uptimeMs: Long, // 运行时间
    val totalTokensProcessed: Long, // 总处理Token数
    val totalErrors: Long, // 总错误数
    val totalBufferAdjustments: Long, // 总缓冲调整次数
    val avgLatencyMs: Long, // 平均延迟
    val overallThroughput: Float, // 总体吞吐量
    val overallErrorRate: Float, // 总体错误率
    val currentMetrics: ProcessingMetrics, // 当前指标
)
