package com.example.gymbro.features.thinkingbox.domain.guardrail

import timber.log.Timber
import java.nio.charset.Charset
import java.nio.charset.CodingErrorAction
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingMLGuardrail - 智能内容修复器
 *
 * 🔥 根据 finalmermaid大纲.md 重新设计
 * 处理 LLM 输出的混合格式，确保解析器能正确处理
 *
 * 核心功能：
 * - 清理 <think> 内的 <phase:XXX> 标签，只保留文本内容
 * - 确保 <think> 内容被正确处理为 perthink 阶段
 * - 修复不完整的标签
 * - 确保 XML 结构完整性
 * - 不再转换 <phase:XXX> 为 <title>，避免时序混乱
 */
@Singleton
class ThinkingMLGuardrail @Inject constructor() {

    // 🔥 【新增】半截tag缓冲，解决token流跨边界问题
    private var chunkBuf = StringBuilder()

    /**
     * 🔥 轻量级token级别清理 - 双缓冲方案专用
     * 只做最基本的字符修复，不做复杂的XML结构修复
     */
    fun sanitizeToken(token: String): String {
        return try {
            token
                .replace("\u0000", "") // 移除null字符
                .replace("\uFFFD", "") // 移除替换字符
                .replace(Regex("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]"), "") // 移除控制字符
        } catch (e: Exception) {
            Timber.w("TB-Guardrail:  Token清理失败", e)
            token
        }
    }

    // 🔥 UTF-8 解码器，处理乱码问题
    private val utf8Decoder = Charset.forName("UTF-8").newDecoder().apply {
        onMalformedInput(CodingErrorAction.REPLACE)
        onUnmappableCharacter(CodingErrorAction.REPLACE)
    }

    companion object {
        private const val TAG = "TB-GUARDRAIL"

        // 🔥 全量正则表达式常量
        private val OPEN_THINK = Regex("<\\s*think\\s*>", RegexOption.IGNORE_CASE)
        private val CLOSE_THINK = Regex("</\\s*think\\s*>", RegexOption.IGNORE_CASE)

        // 🔥 万能 Phase 捕获：允许多个 '<'，允许缺少闭 '>'
        private val ANY_PHASE_TAG = Regex("<+\\s*phase:([A-Za-z_]+)\\s*>?", RegexOption.IGNORE_CASE)
        private val CLOSE_PHASE = Regex("</\\s*phase:[A-Za-z_]+\\s*>", RegexOption.IGNORE_CASE)

        // 🔥 裸 phase: 标记处理（优先级最高，处理半截标签）
        private val BARE_PHASE = Regex("phase\\s*:\\s*([A-Za-z_]+)", RegexOption.IGNORE_CASE)
        private val BROKEN_FINAL = Regex("<\\s*final[^>]*(?<!>)", RegexOption.IGNORE_CASE)

        // 🔥 重复标签检测
        private val DUPLICATE_THINKING_OPEN = Regex("(<thinking>\\s*)+", RegexOption.IGNORE_CASE)
        private val DUPLICATE_THINKING_CLOSE = Regex("(\\s*</thinking>)+", RegexOption.IGNORE_CASE)
    }

    suspend fun retry(): Boolean {
        // 临时实现 - 模拟重试逻辑
        return true
    }

    fun validate(input: String): Boolean {
        // 临时实现 - 总是返回 true
        return true
    }

    /**
     * 快速发送方法 - 用于外部调用
     */
    fun emit(raw: String) {
        // 🔥 快速路径：如果内容不含 '<' '>' 直接发送
        if (!raw.contains('<') && !raw.contains('>')) {
            emitSafe(raw)
            return
        }

        // 否则走完整的验证修复流程
        val result = validateAndFix(raw)
        emitSafe(result.content)
    }

    /**
     * 安全发送方法 - 内部使用
     */
    private fun emitSafe(content: String) {
        // TODO: 集成到实际的发送机制
        // 这里可以发送到下游的 Parser
        Timber.v("TB-Guardrail:  发送内容: ${content.take(100)}${if (content.length > 100) "..." else ""}")
    }

    /**
     * 验证并修复内容
     * 🔥 根据 finalmermaid大纲.md v5 正确实现
     * 核心原则：清理非法标签，不做转换，保持合法标签不变
     */
    fun validateAndFix(rawContent: String): GuardrailResult {
        // 🔥 关键日志：只在内容包含标签时记录
        if (rawContent.contains("<") && rawContent.length > 100) {
            Timber.i("TB-Guardrail:  GUARDRAIL 处理内容，长度: ${rawContent.length}")
        }

        // 空内容检查
        if (rawContent.isBlank()) {
            return GuardrailResult(
                isValid = false,
                content = "",
                confidence = 0.0f,
                errors = listOf(GuardrailError("空内容验证失败")),
                fixes = emptyList(),
            )
        }

        // 🔥 UTF-8 处理：确保字符编码正确
        val cleanContent = try {
            // 简单的 UTF-8 验证和清理
            rawContent.replace("\uFFFD", "?") // 替换替换字符
                .replace(Regex("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]"), "") // 移除控制字符
        } catch (e: Exception) {
            Timber.w("TB-Guardrail:  UTF-8 清理失败，使用原始内容", e)
            rawContent
        }

        // 🔥 【修复①】把上一轮遗留 + 新 chunk 拼一起
        var work = chunkBuf.append(cleanContent).toString()

        // ① 如果结尾仍缺 '>'，保留到下一轮
        val lastOpen = work.lastIndexOf('<')
        if (lastOpen != -1 && work.indexOf('>', lastOpen) == -1) {
            chunkBuf = StringBuilder(work.substring(lastOpen))
            work = work.substring(0, lastOpen)
        } else {
            chunkBuf.clear()
        }

        // ② 🔥 修复 hinking → thinking 拼写错误
        work = work.replace(Regex("<\\/?hinking", RegexOption.IGNORE_CASE)) { matchResult ->
            if (matchResult.value.startsWith("</")) "</thinking>" else "<thinking>"
        }

        var fixedContent = work
        val fixes = mutableListOf<String>()

        // 只在内容较长时记录日志
        if (rawContent.length > 500) {
            Timber.d("TB-Guardrail:  开始修复内容，原始长度: ${rawContent.length}")
        }

        // 🔥 根据 finalmermaid大纲.md v5 的正确修复逻辑
        try {
            // 1️⃣ 清理非法标签（不转换，直接删除）
            fixedContent = removeIllegalTags(fixedContent, fixes)

            // 2️⃣ 修复不完整的合法标签
            fixedContent = fixIncompleteValidTags(fixedContent, fixes)

            // 3️⃣ 确保标记完整性
            if (fixedContent.contains("<final>") && !fixedContent.contains("</final>")) {
                fixedContent += "</final>"
                fixes.add("添加缺失的 </final> 标记")
            }

            if (fixedContent.contains("<thinking>") && !fixedContent.contains("</thinking>") && !fixedContent.contains("<final>")) {
                fixedContent += "</thinking>"
                fixes.add("添加缺失的 </thinking> 标记")
            }

            // 4️⃣ 清理多余的空白字符
            fixedContent = fixedContent
                .replace(Regex("\\n\\s*\\n\\s*\\n"), "\n\n")
                .replace(Regex("^\\s+", RegexOption.MULTILINE), "")
                .trim()
        } catch (e: Exception) {
            Timber.e("TB-Guardrail:  修复过程中发生异常", e)
            fixes.add("修复过程异常: ${e.message}")
        }

        // 验证修复结果
        val isValid = fixedContent.isNotBlank() && fixedContent.length < 100000
        val confidence = when {
            isValid && fixes.isNotEmpty() -> 0.9f
            isValid -> 1.0f
            else -> 0.2f
        }

        // 只在有修复或内容较长时记录日志
        if (fixes.isNotEmpty()) {
            Timber.i("TB-Guardrail:  修复完成，应用了 ${fixes.size} 个修复，最终长度: ${fixedContent.length}")
        } else if (fixedContent.length > 1000) {
            Timber.d("TB-Guardrail:  验证完成，内容长度: ${fixedContent.length}")
        }

        // 🔥 强制 UTF-8 编码修复乱码问题
        val utf8FixedContent = try {
            fixedContent.toByteArray(Charsets.UTF_8).toString(Charsets.UTF_8)
        } catch (e: Exception) {
            Timber.w("TB-Guardrail:  UTF-8 编码修复失败，使用原内容", e)
            fixedContent
        }

        // 🔥 关键日志：只在有修复时记录输出
        if (fixes.isNotEmpty()) {
            Timber.i("TB-Guardrail:  GUARDRAIL 修复完成，应用了 ${fixes.size} 个修复，最终长度: ${utf8FixedContent.length}")
        }

        return GuardrailResult(
            isValid = isValid,
            content = utf8FixedContent,
            confidence = confidence,
            errors = if (isValid) emptyList() else listOf(GuardrailError("内容验证失败")),
            fixes = fixes.map { GuardrailFix(it) },
        )
    }

    /**
     * 🔥 【新增】分离完整内容和半截tag
     * 解决token流跨tag边界被切的问题
     */
    private fun splitLastIncompleteTag(content: String): Pair<String, String> {
        if (content.isEmpty()) return Pair("", "")

        // 查找最后一个可能的半截tag开始位置
        val lastOpenBracket = content.lastIndexOf('<')
        if (lastOpenBracket == -1) {
            return Pair(content, "")
        }

        // 检查从最后一个'<'到结尾是否是半截tag
        val possibleTag = content.substring(lastOpenBracket)

        // 如果包含'>'，说明是完整的，不需要缓冲
        if (possibleTag.contains('>')) {
            return Pair(content, "")
        }

        // 检查是否是我们关心的tag开始（简化版本）
        val isIncompleteTag = possibleTag.length > 1 && possibleTag.length < 15 &&
            possibleTag.matches(Regex("</?\\s*[a-zA-Z]*", RegexOption.IGNORE_CASE))

        return if (isIncompleteTag) {
            // 分离：完整部分 + 半截部分
            Timber.v("TB-Guardrail:  检测到半截tag: '$possibleTag'，缓冲处理")
            Pair(content.substring(0, lastOpenBracket), possibleTag)
        } else {
            // 不是tag，全部返回
            Pair(content, "")
        }
    }

    /**
     * 🔥 根据 finalmermaid大纲.md v5 清理非法标签
     * 全局删除所有带冒号的标签：<phase:PLAN>、</phase:XXX>、phase:XXX 等
     */
    private fun removeIllegalTags(content: String, fixes: MutableList<String>): String {
        var result = content
        var hasChanges = false

        // 🔥 全局清理：删除所有带冒号的标签（开标签、闭标签、裸标记）
        // 匹配 <phase:XXX>、</phase:XXX>、<phase:XXX、phase:XXX 等所有变体
        val illegalTagPattern = Regex("""<*/?phase\s*:\s*[A-Za-z_]+[^>]*>?""", RegexOption.IGNORE_CASE)

        if (result.contains(illegalTagPattern)) {
            result = result.replace(illegalTagPattern, "")
                .replace(Regex("""\s*phase\s*:\s*[A-Za-z_]+""", RegexOption.IGNORE_CASE), "") // 清理裸标记
            hasChanges = true
            fixes.add("全局删除所有非法带冒号标签")
            Timber.i("TB-Guardrail:  🧹 全局清理所有带冒号的非法标签")
        }

        // 清理多余空白
        if (hasChanges) {
            result = result.replace(Regex("""\n\s*\n\s*\n"""), "\n\n").trim()
        }

        return result
    }

    /**
     * 修复不完整的合法标签
     * 处理如 <final 或 <thinking 这样的残缺标签
     */
    private fun fixIncompleteValidTags(content: String, fixes: MutableList<String>): String {
        var result = content

        // 1. 修复残缺的 <final> 标记
        if (result.contains(BROKEN_FINAL)) {
            result = result.replace(BROKEN_FINAL, "<final>")
            fixes.add("修复残缺的 <final> 标记")
            Timber.i("TB-Guardrail:  🔧 修复残缺 final 标签")
        }

        // 2. 转换 <think> 为 <thinking>（如果需要）
        if (result.contains(OPEN_THINK)) {
            result = result.replace(OPEN_THINK, "<thinking>")
            fixes.add("转换 <think> 为 <thinking>")
        }

        if (result.contains(CLOSE_THINK)) {
            result = result.replace(CLOSE_THINK, "</thinking>")
            fixes.add("转换 </think> 为 </thinking>")
        }

        // 3. 合并重复的 thinking 标签
        if (result.split("<thinking>").size > 2) {
            result = result.replace(DUPLICATE_THINKING_OPEN, "<thinking>")
            fixes.add("合并重复的 <thinking> 开标签")
        }

        if (result.split("</thinking>").size > 2) {
            result = result.replace(DUPLICATE_THINKING_CLOSE, "</thinking>")
            fixes.add("合并重复的 </thinking> 闭标签")
        }

        return result
    }
}

/**
 * GuardrailResult - 验证结果数据类
 */
data class GuardrailResult(
    val isValid: Boolean = true,
    val content: String = "",
    val confidence: Float = 1.0f,
    val errors: List<GuardrailError> = emptyList(),
    val fixes: List<GuardrailFix> = emptyList(),
    val errorMessage: String? = null,
) {
    fun hasFixes(): Boolean = fixes.isNotEmpty()
}

/**
 * GuardrailError - 验证错误
 */
data class GuardrailError(
    val message: String,
    val severity: String = "ERROR",
) {
    fun getShortDescription(): String = message
}

/**
 * GuardrailFix - 修复信息
 */
data class GuardrailFix(
    val description: String,
    val type: String = "AUTO",
)
