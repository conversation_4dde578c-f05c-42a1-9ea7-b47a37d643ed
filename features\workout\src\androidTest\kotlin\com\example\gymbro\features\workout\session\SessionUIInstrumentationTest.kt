package com.example.gymbro.features.workout.session

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.features.workout.session.internal.components.SessionStatistics
import com.example.gymbro.features.workout.session.internal.components.SessionStatisticsComponent
import com.example.gymbro.shared.models.exercise.MuscleGroup
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import kotlinx.collections.immutable.persistentListOf
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import kotlin.test.*

/**
 * Session UI组件仪器化测试
 *
 * 验证在真实Android设备上的UI交互：
 * 1. SessionScreen的完整渲染和交互
 * 2. 动作添加按钮的功能
 * 3. AI助手按钮的位置和功能
 * 4. 统计组件的数据显示
 * 5. 用户交互响应的准确性
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class SessionUIInstrumentationTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    @get:Rule
    val composeTestRule = createComposeRule()

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun sessionStatisticsComponent_shouldDisplayCorrectData() {
        // Given - 测试统计数据
        val testStatistics = SessionStatistics(
            totalExercises = 5,
            completedExercises = 3,
            totalSets = 15,
            completedSets = 10,
            currentVolume = 1250.0,
            elapsedTimeMs = 3600000L, // 1小时
            estimatedCalories = 350,
            completionRate = 0.67f, // 67%
        )

        // When - 渲染统计组件
        composeTestRule.setContent {
            GymBroTheme {
                SessionStatisticsComponent(
                    statistics = testStatistics,
                    modifier = androidx.compose.ui.Modifier.fillMaxWidth(),
                )
            }
        }

        // Then - 验证UI元素正确显示
        composeTestRule.onNodeWithText("5").assertIsDisplayed() // 总动作数
        composeTestRule.onNodeWithText("3").assertIsDisplayed() // 完成动作数
        composeTestRule.onNodeWithText("15").assertIsDisplayed() // 总组数
        composeTestRule.onNodeWithText("10").assertIsDisplayed() // 完成组数
        composeTestRule.onNodeWithText("1:00:00").assertIsDisplayed() // 训练时长
        composeTestRule.onNodeWithText("350").assertIsDisplayed() // 预估卡路里

        // 验证进度显示
        composeTestRule.onNodeWithText("67%").assertIsDisplayed() // 完成率
    }

    @Test
    fun sessionScreen_addExerciseButton_shouldBeInBottomRightPosition() {
        // Given - 空的训练会话状态
        val emptyState = SessionContract.State(
            sessionId = "ui_test_session",
            exercises = persistentListOf(),
            totalExercises = 0,
        )

        var addExerciseClicked = false

        // When - 渲染SessionScreen
        composeTestRule.setContent {
            GymBroTheme {
                SessionScreen(
                    state = emptyState,
                    onIntent = { intent ->
                        if (intent is SessionContract.Intent.ShowExerciseSelector) {
                            addExerciseClicked = true
                        }
                    },
                    onNavigateToExerciseLibrary = { /* 测试导航 */ },
                )
            }
        }

        // Then - 验证添加动作按钮存在且可点击
        composeTestRule
            .onNodeWithContentDescription("添加动作")
            .assertIsDisplayed()
            .assertHasClickAction()

        // 点击按钮验证功能
        composeTestRule
            .onNodeWithContentDescription("添加动作")
            .performClick()

        // 验证点击事件被正确处理
        assertTrue(addExerciseClicked, "添加动作按钮点击应该触发ShowExerciseSelector Intent")
    }

    @Test
    fun sessionScreen_aiAssistantButton_shouldBeInTopRightPosition() {
        // Given - 有动作的训练会话状态
        val stateWithExercises = SessionContract.State(
            sessionId = "ai_test_session",
            exercises = persistentListOf(
                createTestSessionExerciseUiModel("test_ex", "测试动作"),
            ),
            totalExercises = 1,
        )

        var aiAssistantClicked = false

        // When - 渲染SessionScreen
        composeTestRule.setContent {
            GymBroTheme {
                SessionScreen(
                    state = stateWithExercises,
                    onIntent = { intent ->
                        // 根据实际的AI助手Intent类型进行判断
                        if (intent.toString().contains("AI") || intent.toString().contains("Assistant")) {
                            aiAssistantClicked = true
                        }
                    },
                    onNavigateToExerciseLibrary = { /* 测试导航 */ },
                )
            }
        }

        // Then - 验证AI助手按钮在顶部区域
        composeTestRule
            .onNodeWithContentDescription("AI助手")
            .assertIsDisplayed()
            .assertHasClickAction()

        // 验证按钮位置在顶部（通过检查是否在header区域）
        composeTestRule
            .onNodeWithContentDescription("AI助手")
            .assertIsDisplayed()
    }

    @Test
    fun sessionScreen_exerciseList_shouldRenderCorrectly() {
        // Given - 包含多个动作的状态
        val stateWithMultipleExercises = SessionContract.State(
            sessionId = "exercise_list_test",
            exercises = persistentListOf(
                createTestSessionExerciseUiModel("bench_press", "卧推"),
                createTestSessionExerciseUiModel("squat", "深蹲"),
                createTestSessionExerciseUiModel("deadlift", "硬拉"),
            ),
            totalExercises = 3,
            completedExercises = 1,
            totalSets = 9,
            completedSetsCount = 3,
        )

        // When - 渲染SessionScreen
        composeTestRule.setContent {
            GymBroTheme {
                SessionScreen(
                    state = stateWithMultipleExercises,
                    onIntent = { },
                    onNavigateToExerciseLibrary = { },
                )
            }
        }

        // Then - 验证所有动作都正确显示
        composeTestRule.onNodeWithText("卧推").assertIsDisplayed()
        composeTestRule.onNodeWithText("深蹲").assertIsDisplayed()
        composeTestRule.onNodeWithText("硬拉").assertIsDisplayed()

        // 验证动作数量统计正确显示
        composeTestRule.onNodeWithText("3").assertIsDisplayed() // 总动作数

        // 验证列表可以滚动（如果需要）
        composeTestRule
            .onNodeWithText("硬拉")
            .assertIsDisplayed()

        // 可以进一步测试滚动行为
        composeTestRule.onRoot().performTouchInput {
            swipeUp(startY = center.y, endY = center.y - 500f)
        }
    }

    @Test
    fun sessionScreen_emptyState_shouldShowCorrectPrompt() {
        // Given - 空的训练会话
        val emptyState = SessionContract.State(
            sessionId = "empty_test_session",
            exercises = persistentListOf(),
            totalExercises = 0,
        )

        // When - 渲染空状态
        composeTestRule.setContent {
            GymBroTheme {
                SessionScreen(
                    state = emptyState,
                    onIntent = { },
                    onNavigateToExerciseLibrary = { },
                )
            }
        }

        // Then - 验证空状态提示正确显示
        composeTestRule
            .onNodeWithText("开始添加动作来创建你的训练")
            .assertIsDisplayed()

        // 验证添加动作按钮在空状态下也可见
        composeTestRule
            .onNodeWithContentDescription("添加动作")
            .assertIsDisplayed()
    }

    @Test
    fun sessionScreen_loadingState_shouldShowProgressIndicator() {
        // Given - 加载状态
        val loadingState = SessionContract.State(
            sessionId = "loading_test_session",
            exercises = persistentListOf(),
            isLoading = true,
        )

        // When - 渲染加载状态
        composeTestRule.setContent {
            GymBroTheme {
                SessionScreen(
                    state = loadingState,
                    onIntent = { },
                    onNavigateToExerciseLibrary = { },
                )
            }
        }

        // Then - 验证加载指示器显示
        composeTestRule
            .onNodeWithContentDescription("加载中")
            .assertIsDisplayed()
    }

    @Test
    fun sessionScreen_errorState_shouldShowErrorMessage() {
        // Given - 错误状态
        val errorState = SessionContract.State(
            sessionId = "error_test_session",
            exercises = persistentListOf(),
            error = UiText.DynamicString("测试错误信息"),
        )

        var errorCleared = false

        // When - 渲染错误状态
        composeTestRule.setContent {
            GymBroTheme {
                SessionScreen(
                    state = errorState,
                    onIntent = { intent ->
                        if (intent is SessionContract.Intent.ClearError) {
                            errorCleared = true
                        }
                    },
                    onNavigateToExerciseLibrary = { },
                )
            }
        }

        // Then - 验证错误信息显示
        composeTestRule
            .onNodeWithText("测试错误信息")
            .assertIsDisplayed()

        // 验证可以清除错误（如果有清除按钮）
        composeTestRule
            .onNodeWithText("重试")
            .performClick()

        assertTrue(errorCleared, "应该能清除错误状态")
    }

    @Test
    fun sessionScreen_userInteractions_shouldTriggerCorrectIntents() {
        // Given - 带有动作的状态
        val interactiveState = SessionContract.State(
            sessionId = "interaction_test_session",
            exercises = persistentListOf(
                createTestSessionExerciseUiModel("test_exercise", "交互测试动作"),
            ),
            totalExercises = 1,
        )

        val capturedIntents = mutableListOf<SessionContract.Intent>()

        // When - 渲染交互式SessionScreen
        composeTestRule.setContent {
            GymBroTheme {
                SessionScreen(
                    state = interactiveState,
                    onIntent = { intent ->
                        capturedIntents.add(intent)
                    },
                    onNavigateToExerciseLibrary = { },
                )
            }
        }

        // Then - 测试各种用户交互

        // 1. 点击添加动作按钮
        composeTestRule
            .onNodeWithContentDescription("添加动作")
            .performClick()

        // 验证触发了正确的Intent
        assertTrue(
            capturedIntents.any { it is SessionContract.Intent.ShowExerciseSelector },
            "应该触发ShowExerciseSelector Intent",
        )

        // 2. 如果有暂停按钮，测试暂停功能
        composeTestRule
            .onNodeWithContentDescription("暂停训练")
            .performClick()

        // 验证暂停Intent
        assertTrue(
            capturedIntents.any { it is SessionContract.Intent.PauseSession },
            "应该触发PauseSession Intent",
        )
    }

    // === 辅助方法 ===

    private fun createTestSessionExerciseUiModel(
        exerciseId: String,
        name: String,
    ): SessionContract.SessionExerciseUiModel {
        val sets = listOf(
            com.example.gymbro.domain.exercise.model.ExerciseSet(
                id = "set_${exerciseId}_1",
                exerciseId = exerciseId,
                sessionId = "ui_test_session",
                order = 0,
                weight = 50f,
                reps = 10,
                isCompleted = false,
                createdAt = System.currentTimeMillis(),
            ),
        )

        val sessionExercise = com.example.gymbro.domain.workout.model.session.SessionExercise(
            id = "session_ex_$exerciseId",
            sessionId = "ui_test_session",
            exerciseId = exerciseId,
            order = 0,
            sets = sets,
            name = name,
            targetSets = 3,
            completedSets = 0,
            isCompleted = false,
            createdAt = System.currentTimeMillis(),
        )

        val exercise = Exercise(
            id = exerciseId,
            name = UiText.DynamicString(name),
            muscleGroup = MuscleGroup.CHEST,
            equipment = emptyList(),
            description = UiText.DynamicString("UI测试动作：$name"),
            createdAt = System.currentTimeMillis(),
        )

        return SessionContract.SessionExerciseUiModel(
            sessionExercise = sessionExercise,
            exercise = exercise,
        )
    }
}