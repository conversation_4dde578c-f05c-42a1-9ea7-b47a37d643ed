package com.example.gymbro.features.workout.template.edit.internal.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import kotlinx.coroutines.delay

/**
 * Toast类型枚举
 * 定义了不同类型的Toast消息样式
 */
enum class ToastType {
    SUCCESS,
    INFO,
    WARNING,
    ERROR,
}

/**
 * 统一的Toast组件
 *
 * 符合GymBro设计系统规范，使用Token系统，支持多种Toast类型
 *
 * @param message 要显示的消息
 * @param type Toast类型
 * @param visible 是否可见
 * @param onDismiss 消失回调
 * @param modifier 修饰符
 */
@Composable
fun UnifiedToast(
    message: UiText,
    type: ToastType,
    visible: Boolean,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (visible) {
        LaunchedEffect(Unit) {
            delay(3000) // 3秒后自动消失
            onDismiss()
        }

        val (backgroundColor, iconColor, icon) = when (type) {
            ToastType.SUCCESS -> Triple(
                Tokens.Color.Success,
                Color.White,
                Icons.Default.CheckCircle,
            )
            ToastType.INFO -> Triple(
                Tokens.Color.Info,
                Color.White,
                Icons.Default.Info,
            )
            ToastType.WARNING -> Triple(
                Tokens.Color.Warning,
                Color.White,
                Icons.Default.Warning,
            )
            ToastType.ERROR -> Triple(
                Tokens.Color.Error,
                Color.White,
                Icons.Default.Error,
            )
        }

        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            shape = RoundedCornerShape(Tokens.Radius.Small),
            colors = CardDefaults.cardColors(
                containerColor = backgroundColor,
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = Tokens.Elevation.Small,
            ),
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(Tokens.Spacing.Medium),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = iconColor,
                    modifier = Modifier.size(Tokens.Icon.Standard),
                )

                Text(
                    text = message.asString(),
                    style = MaterialTheme.typography.bodyMedium,
                    color = iconColor,
                    modifier = Modifier.weight(1f),
                )

                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.size(Tokens.Icon.Standard),
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = iconColor,
                        modifier = Modifier.size(Tokens.Icon.Small),
                    )
                }
            }
        }
    }
}

/**
 * 自动保存Toast组件
 *
 * 专门用于模板编辑的自动保存提示
 *
 * @param visible 是否可见
 * @param onDismiss 消失回调
 * @param modifier 修饰符
 */
@Composable
fun AutoSaveToast(
    visible: Boolean,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    UnifiedToast(
        message = UiText.DynamicString("模板已自动保存"),
        type = ToastType.SUCCESS,
        visible = visible,
        onDismiss = onDismiss,
        modifier = modifier,
    )
}

/**
 * 模板编辑自动保存指示器
 *
 * 用于显示模板编辑状态的指示器组件
 *
 * @param isAutoSaving 是否正在自动保存
 * @param lastSaveTime 最后保存时间
 * @param modifier 修饰符
 */
@Composable
fun TemplateEditAutoSaveIndicator(
    isAutoSaving: Boolean,
    lastSaveTime: String?,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
    ) {
        if (isAutoSaving) {
            CircularProgressIndicator(
                modifier = Modifier.size(Tokens.Icon.Small),
                strokeWidth = Tokens.Size.IndicatorSmall,
                color = Tokens.Color.Info,
            )
            Text(
                text = "保存中...",
                style = MaterialTheme.typography.bodySmall,
                color = Tokens.Color.Info,
            )
        } else if (lastSaveTime != null) {
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = null,
                tint = Tokens.Color.Success,
                modifier = Modifier.size(Tokens.Icon.Small),
            )
            Text(
                text = "已保存 $lastSaveTime",
                style = MaterialTheme.typography.bodySmall,
                color = Tokens.Color.Success,
            )
        }
    }
}

@GymBroPreview
@Composable
private fun toastComponentsPreview() {
    GymBroTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // Success Toast
            UnifiedToast(
                message = UiText.DynamicString("操作成功完成"),
                type = ToastType.SUCCESS,
                visible = true,
                onDismiss = { },
            )

            // Error Toast
            UnifiedToast(
                message = UiText.DynamicString("发生错误，请重试"),
                type = ToastType.ERROR,
                visible = true,
                onDismiss = { },
            )

            // Auto Save Indicator
            TemplateEditAutoSaveIndicator(
                isAutoSaving = false,
                lastSaveTime = "刚刚",
            )

            // Auto Saving Indicator
            TemplateEditAutoSaveIndicator(
                isAutoSaving = true,
                lastSaveTime = null,
            )
        }
    }
}
