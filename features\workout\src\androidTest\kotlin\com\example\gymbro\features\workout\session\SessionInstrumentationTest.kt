package com.example.gymbro.features.workout.session

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.features.workout.session.internal.reducer.SessionReducer
import com.example.gymbro.shared.models.exercise.MuscleGroup
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import javax.inject.Inject
import kotlin.test.*

/**
 * Session模块仪器化测试
 *
 * 验证在真实Android环境中的核心功能：
 * 1. SessionReducer在Android设备上的正确行为
 * 2. 动作添加功能的UI集成
 * 3. 数据持久化在真实数据库中的表现
 * 4. 内存管理和性能表现
 * 5. 真实设备上的状态管理
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class SessionInstrumentationTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    @get:Rule
    val composeTestRule = createComposeRule()

    @Inject
    lateinit var sessionReducer: SessionReducer

    private val context = InstrumentationRegistry.getInstrumentation().targetContext

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun sessionReducer_addExercises_shouldWorkOnRealDevice() = runTest {
        // Given - 真实的Android环境下的初始状态
        val initialState = SessionContract.State(
            sessionId = "instrumentation_test_${System.currentTimeMillis()}",
            exercises = persistentListOf(),
        )

        // 创建真实的Exercise对象
        val realExercises = listOf(
            createRealExercise("bench_press_real", "杠铃卧推"),
            createRealExercise("squat_real", "深蹲"),
            createRealExercise("deadlift_real", "硬拉"),
        )

        // When - 在真实环境中添加动作
        val addIntent = SessionContract.Intent.AddExercises(realExercises)
        val result = sessionReducer.reduce(addIntent, initialState)

        // Then - 验证在真实设备上的结果
        val newState = result.newState
        assertEquals(3, newState.exercises.size, "应该成功添加3个动作")
        assertEquals(3, newState.totalExercises, "总动作数应该为3")
        assertTrue(newState.hasUnsavedChanges, "应该有未保存的更改")

        // 验证每个动作的完整性
        realExercises.forEachIndexed { index, originalExercise ->
            val addedExercise = newState.exercises[index]
            assertEquals(originalExercise.id, addedExercise.exerciseId, "动作ID应该匹配")
            assertNotNull(addedExercise.sessionExercise, "SessionExercise不应该为null")
            assertTrue(addedExercise.sessionExercise.sets.isNotEmpty(), "应该有默认的训练组")

            // 验证UiText正确处理
            when (val name = originalExercise.name) {
                is UiText.DynamicString -> {
                    assertEquals(name.value, addedExercise.sessionExercise.name, "动作名称应该正确转换")
                }
            }
        }

        // 验证统计数据计算正确
        val totalSets = newState.exercises.sumOf { it.sets.size }
        assertEquals(totalSets, newState.totalSets, "总组数统计应该正确")
    }

    @Test
    fun sessionState_progressCalculation_shouldBeAccurateOnDevice() = runTest {
        // Given - 有部分完成组数的状态
        val exercisesWithProgress = persistentListOf(
            createSessionExerciseWithCompletedSets("ex1", completedSets = 2, totalSets = 3),
            createSessionExerciseWithCompletedSets("ex2", completedSets = 1, totalSets = 3),
            createSessionExerciseWithCompletedSets("ex3", completedSets = 3, totalSets = 3), // 完全完成
        )

        val state = SessionContract.State(
            sessionId = "progress_test_${System.currentTimeMillis()}",
            exercises = exercisesWithProgress,
            totalExercises = 3,
            completedSetsCount = 6, // 2 + 1 + 3 = 6
            totalSets = 9, // 3 + 3 + 3 = 9
            completedExercises = 1, // 只有ex3完全完成
        )

        // Then - 验证计算精度在真实设备上的表现
        assertEquals(6f / 9f, state.progressPercentage, 0.001f, "进度百分比计算应该精确")
        assertTrue(state.canCompleteWorkout, "应该可以完成训练")
        assertEquals(1, state.completedExercises, "完成的动作数应该正确")

        // 验证当前动作逻辑
        assertNotNull(state.currentExercise, "应该有当前动作")
        assertEquals("ex1", state.currentExercise?.exerciseId, "当前动作应该是第一个")
    }

    @Test
    fun sessionReducer_exerciseSelector_shouldCreateNavigationEffect() = runTest {
        // Given
        val state = SessionContract.State(
            sessionId = "selector_test_${System.currentTimeMillis()}",
        )

        // When - 触发动作选择器
        val intent = SessionContract.Intent.ShowExerciseSelector
        val result = sessionReducer.reduce(intent, state)

        // Then - 应该创建导航Effect
        assertNotNull(result.effects, "应该有副作用")
        assertTrue(
            result.effects!!.any { it is SessionContract.Effect.NavigateToExerciseLibrary },
            "应该包含导航到动作库的Effect",
        )
    }

    @Test
    fun sessionData_memoryUsage_shouldBeEfficientOnDevice() = runTest {
        // Given - 大量动作数据以测试内存效率
        val largeExerciseList = (1..50).map { index ->
            createRealExercise("exercise_$index", "动作_$index")
        }

        val initialState = SessionContract.State(
            sessionId = "memory_test_${System.currentTimeMillis()}",
            exercises = persistentListOf(),
        )

        // When - 批量添加大量动作
        val startTime = System.currentTimeMillis()
        val addIntent = SessionContract.Intent.AddExercises(largeExerciseList)
        val result = sessionReducer.reduce(addIntent, initialState)
        val endTime = System.currentTimeMillis()

        // Then - 验证性能和内存使用
        val processingTime = endTime - startTime
        assertTrue(processingTime < 1000, "处理50个动作应该在1秒内完成，实际用时: ${processingTime}ms")

        assertEquals(50, result.newState.exercises.size, "应该成功添加50个动作")
        assertEquals(50, result.newState.totalExercises, "总动作数应该正确")

        // 验证数据结构的完整性没有因为大量数据而损坏
        result.newState.exercises.forEachIndexed { index, exercise ->
            assertEquals("exercise_${index + 1}", exercise.exerciseId, "动作ID顺序应该正确")
            assertNotNull(exercise.sessionExercise, "每个动作都应该有SessionExercise")
            assertTrue(exercise.sessionExercise.sets.isNotEmpty(), "每个动作都应该有默认组数")
        }
    }

    @Test
    fun sessionState_persistentList_shouldMaintainImmutabilityOnDevice() = runTest {
        // Given - 初始状态
        val initialState = SessionContract.State(
            sessionId = "immutability_test_${System.currentTimeMillis()}",
            exercises = persistentListOf(),
        )

        val exercise = createRealExercise("immutability_test", "不可变性测试")

        // When - 添加动作
        val addIntent = SessionContract.Intent.AddExercises(listOf(exercise))
        val result = sessionReducer.reduce(addIntent, initialState)

        // Then - 验证不可变性
        assertNotSame(initialState.exercises, result.newState.exercises, "exercises应该是新的实例")
        assertEquals(0, initialState.exercises.size, "原始状态不应该被修改")
        assertEquals(1, result.newState.exercises.size, "新状态应该包含添加的动作")

        // 验证状态对象的不可变性
        assertNotSame(initialState, result.newState, "State对象应该是新的实例")
        assertFalse(initialState.hasUnsavedChanges, "原始状态的hasUnsavedChanges不应该被修改")
        assertTrue(result.newState.hasUnsavedChanges, "新状态应该标记有未保存更改")
    }

    @Test
    fun sessionReducer_errorRecovery_shouldWorkCorrectlyOnDevice() = runTest {
        // Given - 模拟错误状态
        val stateWithError = SessionContract.State(
            sessionId = "error_test_${System.currentTimeMillis()}",
            error = UiText.DynamicString("测试错误信息"),
            exercises = persistentListOf(
                createSessionExerciseWithCompletedSets("preserved_ex", 1, 3),
            ),
        )

        // When - 清除错误
        val clearErrorIntent = SessionContract.Intent.ClearError
        val result = sessionReducer.reduce(clearErrorIntent, stateWithError)

        // Then - 验证错误清除但数据保留
        assertNull(result.newState.error, "错误应该被清除")
        assertEquals(1, result.newState.exercises.size, "动作数据应该被保留")
        assertEquals("preserved_ex", result.newState.exercises[0].exerciseId, "动作数据应该完整")
    }

    // === 辅助方法 ===

    private fun createRealExercise(id: String, name: String): Exercise {
        return Exercise(
            id = id,
            name = UiText.DynamicString(name),
            muscleGroup = MuscleGroup.CHEST,
            equipment = emptyList(),
            description = UiText.DynamicString("仪器化测试动作：$name"),
            defaultSets = 3,
            defaultReps = 10,
            defaultWeight = 60f,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
        )
    }

    private fun createSessionExerciseWithCompletedSets(
        exerciseId: String,
        completedSets: Int,
        totalSets: Int,
    ): SessionContract.SessionExerciseUiModel {
        val sets = (1..totalSets).map { setIndex ->
            com.example.gymbro.domain.exercise.model.ExerciseSet(
                id = "set_${exerciseId}_$setIndex",
                exerciseId = exerciseId,
                sessionId = "instrumentation_test",
                order = setIndex - 1,
                weight = 50f,
                reps = 10,
                isCompleted = setIndex <= completedSets,
                createdAt = System.currentTimeMillis(),
            )
        }

        val sessionExercise = com.example.gymbro.domain.workout.model.session.SessionExercise(
            id = "session_ex_$exerciseId",
            sessionId = "instrumentation_test",
            exerciseId = exerciseId,
            order = 0,
            sets = sets,
            name = "测试动作 $exerciseId",
            targetSets = totalSets,
            completedSets = completedSets,
            isCompleted = completedSets >= totalSets,
            createdAt = System.currentTimeMillis(),
        )

        val exercise = Exercise(
            id = exerciseId,
            name = UiText.DynamicString("测试动作 $exerciseId"),
            muscleGroup = MuscleGroup.CHEST,
            equipment = emptyList(),
            description = UiText.DynamicString("仪器化测试动作"),
            createdAt = System.currentTimeMillis(),
        )

        return SessionContract.SessionExerciseUiModel(
            sessionExercise = sessionExercise,
            exercise = exercise,
        )
    }
}