package com.example.gymbro.features.workout.session

import androidx.benchmark.junit4.BenchmarkRule
import androidx.benchmark.junit4.measureRepeated
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.features.workout.session.internal.reducer.SessionReducer
import com.example.gymbro.shared.models.exercise.MuscleGroup
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import javax.inject.Inject
import kotlin.system.measureTimeMillis
import kotlin.test.*

/**
 * Session模块性能仪器化测试
 *
 * 验证在真实Android设备上的性能表现：
 * 1. 大量动作添加的性能
 * 2. 状态计算的响应时间
 * 3. 内存使用效率
 * 4. UI渲染性能
 * 5. 数据库操作性能
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class SessionPerformanceInstrumentationTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    @get:Rule
    val benchmarkRule = BenchmarkRule()

    @Inject
    lateinit var sessionReducer: SessionReducer

    private val context = InstrumentationRegistry.getInstrumentation().targetContext

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun sessionReducer_addLargeExerciseList_shouldCompleteWithinTimeLimit() = runTest {
        // Given - 大量动作数据（模拟真实使用场景）
        val largeExerciseList = (1..100).map { index ->
            createPerformanceTestExercise("performance_ex_$index", "性能测试动作$index")
        }

        val initialState = SessionContract.State(
            sessionId = "performance_test_${System.currentTimeMillis()}",
            exercises = persistentListOf(),
        )

        // When - 测量添加大量动作的性能
        val executionTime = measureTimeMillis {
            val addIntent = SessionContract.Intent.AddExercises(largeExerciseList)
            val result = sessionReducer.reduce(addIntent, initialState)

            // 验证结果正确性
            assertEquals(100, result.newState.exercises.size)
            assertEquals(100, result.newState.totalExercises)
        }

        // Then - 验证性能指标
        assertTrue(
            executionTime < 2000, // 2秒内完成
            "添加100个动作应该在2秒内完成，实际用时: ${executionTime}ms",
        )

        println("性能测试结果 - 添加100个动作用时: ${executionTime}ms")
    }

    @Test
    fun sessionReducer_massiveStateCalculation_shouldBeEfficient() = runTest {
        // Given - 包含大量数据的复杂状态
        val massiveExerciseList = (1..200).map { index ->
            createSessionExerciseWithManySets("massive_ex_$index", 10) // 每个动作10组
        }

        val complexState = SessionContract.State(
            sessionId = "massive_state_test",
            exercises = massiveExerciseList,
            totalExercises = 200,
            totalSets = 2000, // 200动作 * 10组
            completedSetsCount = 1000, // 50%完成
        )

        // When - 测量复杂状态计算性能
        val calculationTime = measureTimeMillis {
            // 测试各种状态派生属性的计算
            val progressPercentage = complexState.progressPercentage
            val canComplete = complexState.canCompleteWorkout
            val currentExercise = complexState.currentExercise
            val formattedTime = complexState.formattedElapsedTime

            // 验证计算结果正确性
            assertEquals(0.5f, progressPercentage, 0.01f)
            assertTrue(canComplete)
            assertNotNull(currentExercise)
        }

        // Then - 验证计算性能
        assertTrue(
            calculationTime < 500, // 500ms内完成
            "大量数据状态计算应该在500ms内完成，实际用时: ${calculationTime}ms",
        )

        println("性能测试结果 - 复杂状态计算用时: ${calculationTime}ms")
    }

    @Test
    fun sessionReducer_frequentStateUpdates_shouldMaintainPerformance() = runTest {
        // Given - 初始状态
        var currentState = SessionContract.State(
            sessionId = "frequent_updates_test",
            exercises = persistentListOf(
                createSessionExerciseWithManySets("frequent_ex", 20),
            ),
        )

        // When - 模拟频繁的状态更新（如用户快速输入重量、次数）
        val totalUpdateTime = measureTimeMillis {
            repeat(100) { updateIndex ->
                // 模拟更新重量
                val updateWeightIntent = SessionContract.Intent.UpdateWeight(
                    exerciseIndex = 0,
                    setIndex = updateIndex % 20,
                    weight = 50f + updateIndex,
                )

                val result = sessionReducer.reduce(updateWeightIntent, currentState)
                currentState = result.newState

                // 验证更新正确性
                assertTrue(currentState.exercises.isNotEmpty())
            }
        }

        // Then - 验证频繁更新的性能
        val averageUpdateTime = totalUpdateTime.toFloat() / 100
        assertTrue(
            averageUpdateTime < 50, // 单次更新平均50ms内
            "单次状态更新平均应该在50ms内完成，实际平均用时: ${averageUpdateTime}ms",
        )

        println("性能测试结果 - 100次状态更新总用时: ${totalUpdateTime}ms，平均: ${averageUpdateTime}ms")
    }

    @Test
    fun sessionState_memoryUsage_shouldBeReasonable() = runTest {
        // Given - 记录初始内存使用
        val runtime = Runtime.getRuntime()
        System.gc() // 强制垃圾回收以获得准确的内存基线
        Thread.sleep(100)

        val initialMemory = runtime.totalMemory() - runtime.freeMemory()

        // When - 创建大量会话数据
        val largeSessionStates = mutableListOf<SessionContract.State>()

        repeat(50) { sessionIndex ->
            val exercises = (1..20).map { exerciseIndex ->
                createSessionExerciseWithManySets("mem_test_${sessionIndex}_$exerciseIndex", 5)
            }

            val state = SessionContract.State(
                sessionId = "memory_test_session_$sessionIndex",
                exercises = exercises,
                totalExercises = 20,
                totalSets = 100,
            )

            largeSessionStates.add(state)
        }

        System.gc() // 再次垃圾回收
        Thread.sleep(100)
        val finalMemory = runtime.totalMemory() - runtime.freeMemory()

        // Then - 验证内存使用合理
        val memoryIncrease = finalMemory - initialMemory
        val memoryIncreaseKB = memoryIncrease / 1024

        assertTrue(
            memoryIncreaseKB < 50 * 1024, // 50MB以内
            "50个复杂会话状态的内存增长应该在50MB以内，实际增长: ${memoryIncreaseKB}KB",
        )

        println("性能测试结果 - 内存增长: ${memoryIncreaseKB}KB")

        // 清理测试数据
        largeSessionStates.clear()
        System.gc()
    }

    @Test
    fun sessionReducer_concurrentStateUpdates_shouldBeThreadSafe() = runTest {
        // Given - 共享状态
        val sharedState = SessionContract.State(
            sessionId = "concurrent_test",
            exercises = persistentListOf(
                createSessionExerciseWithManySets("concurrent_ex", 10),
            ),
        )

        val results = mutableListOf<SessionContract.State>()
        val executionTime = measureTimeMillis {
            // When - 模拟并发状态更新
            val threads = (1..10).map { threadIndex ->
                Thread {
                    repeat(10) { updateIndex ->
                        val intent = SessionContract.Intent.UpdateReps(
                            exerciseIndex = 0,
                            setIndex = updateIndex % 10,
                            reps = threadIndex + updateIndex,
                        )

                        val result = sessionReducer.reduce(intent, sharedState)
                        synchronized(results) {
                            results.add(result.newState)
                        }
                    }
                }
            }

            threads.forEach { it.start() }
            threads.forEach { it.join() }
        }

        // Then - 验证并发安全性和性能
        assertEquals(100, results.size, "应该有100个结果")

        assertTrue(
            executionTime < 5000, // 5秒内完成
            "10个线程各进行10次更新应该在5秒内完成，实际用时: ${executionTime}ms",
        )

        // 验证数据一致性
        results.forEach { state ->
            assertEquals(1, state.exercises.size, "每个结果状态都应该有1个动作")
            assertEquals(10, state.exercises[0].sets.size, "每个动作都应该有10组")
        }

        println("性能测试结果 - 并发更新用时: ${executionTime}ms")
    }

    @Test
    fun sessionReducer_benchmarkAddExercises_measurePerformance() {
        // Given - 基准测试数据
        val testExercises = (1..10).map { index ->
            createPerformanceTestExercise("benchmark_ex_$index", "基准测试动作$index")
        }

        val initialState = SessionContract.State(
            sessionId = "benchmark_test",
            exercises = persistentListOf(),
        )

        // When & Then - 使用Benchmark库进行精确测量
        benchmarkRule.measureRepeated {
            val addIntent = SessionContract.Intent.AddExercises(testExercises)
            val result = sessionReducer.reduce(addIntent, initialState)

            // 确保计算完成
            assertEquals(10, result.newState.exercises.size)
        }
    }

    @Test
    fun sessionState_dataTransformation_shouldBeOptimized() = runTest {
        // Given - 需要转换的原始数据
        val rawExercises = (1..50).map { index ->
            createPerformanceTestExercise("transform_ex_$index", "转换测试动作$index")
        }

        val initialState = SessionContract.State(
            sessionId = "transformation_test",
            exercises = persistentListOf(),
        )

        // When - 测量数据转换性能
        val transformationTime = measureTimeMillis {
            val addIntent = SessionContract.Intent.AddExercises(rawExercises)
            val result = sessionReducer.reduce(addIntent, initialState)

            // 触发各种数据转换
            val newState = result.newState
            val totalSets = newState.totalSets
            val progress = newState.progressPercentage
            val canComplete = newState.canCompleteWorkout

            // 验证转换结果
            assertTrue(totalSets > 0)
            assertTrue(progress >= 0f)
        }

        // Then - 验证转换性能
        assertTrue(
            transformationTime < 1000, // 1秒内完成
            "50个动作的数据转换应该在1秒内完成，实际用时: ${transformationTime}ms",
        )

        println("性能测试结果 - 数据转换用时: ${transformationTime}ms")
    }

    // === 辅助方法 ===

    private fun createPerformanceTestExercise(id: String, name: String): Exercise {
        return Exercise(
            id = id,
            name = UiText.DynamicString(name),
            muscleGroup = MuscleGroup.CHEST,
            equipment = emptyList(),
            description = UiText.DynamicString("性能测试动作：$name"),
            defaultSets = 3,
            defaultReps = 10,
            defaultWeight = 50f,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
        )
    }

    private fun createSessionExerciseWithManySets(
        exerciseId: String,
        numberOfSets: Int,
    ): SessionContract.SessionExerciseUiModel {
        val sets = (1..numberOfSets).map { setIndex ->
            com.example.gymbro.domain.exercise.model.ExerciseSet(
                id = "set_${exerciseId}_$setIndex",
                exerciseId = exerciseId,
                sessionId = "performance_test",
                order = setIndex - 1,
                weight = 50f + setIndex,
                reps = 10 + setIndex,
                isCompleted = setIndex <= numberOfSets / 2, // 一半完成
                createdAt = System.currentTimeMillis(),
            )
        }

        val sessionExercise = com.example.gymbro.domain.workout.model.session.SessionExercise(
            id = "session_ex_$exerciseId",
            sessionId = "performance_test",
            exerciseId = exerciseId,
            order = 0,
            sets = sets,
            name = "性能测试动作 $exerciseId",
            targetSets = numberOfSets,
            completedSets = numberOfSets / 2,
            isCompleted = false,
            createdAt = System.currentTimeMillis(),
        )

        val exercise = Exercise(
            id = exerciseId,
            name = UiText.DynamicString("性能测试动作 $exerciseId"),
            muscleGroup = MuscleGroup.CHEST,
            equipment = emptyList(),
            description = UiText.DynamicString("性能测试动作"),
            createdAt = System.currentTimeMillis(),
        )

        return SessionContract.SessionExerciseUiModel(
            sessionExercise = sessionExercise,
            exercise = exercise,
        )
    }
}