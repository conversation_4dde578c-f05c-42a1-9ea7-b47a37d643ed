package com.example.gymbro.features.workout.template.edit.internal.components.drag

import androidx.compose.foundation.gestures.Orientation
import kotlinx.coroutines.flow.StateFlow

/**
 * 统一拖拽处理接口，定义拖拽操作的标准协议
 */
interface UnifiedDragHandler<T> {
    // 当前拖拽状态流
    val dragState: StateFlow<DragState<T>>

    // 开始拖拽
    fun startDrag(item: T, position: Int)

    // 更新拖拽位置
    fun updateDragPosition(newPosition: Int)

    // 完成拖拽
    fun commitDrag(): Result<Unit>

    // 取消拖拽
    fun cancelDrag()

    // 是否正在拖拽
    fun isDragging(): <PERSON>olean
}

// 拖拽状态
data class DragState<T>(
    val isDragging: Boolean = false,
    val draggedItem: T? = null,
    val originalPosition: Int? = null,
    val currentPosition: Int? = null,
    val orientation: Orientation = Orientation.Vertical
)
