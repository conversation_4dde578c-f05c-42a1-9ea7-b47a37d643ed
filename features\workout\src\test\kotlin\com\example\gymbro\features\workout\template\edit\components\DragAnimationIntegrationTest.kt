package com.example.gymbro.features.workout.template.edit.components

import com.example.gymbro.features.workout.shared.components.drag.DragConfig
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import org.junit.Test
import org.junit.Assert.*
import androidx.compose.ui.geometry.Offset
import com.example.gymbro.features.workout.template.edit.internal.components.getDragAnimationInfo

/**
 * DragAnimationIntegration兼容性测试
 *
 * 验证新的Material3动画集成功能的正确性
 */
class DragAnimationIntegrationTest {

    @Test
    fun `getDragAnimationInfo should return correct info for dragging item`() {
        val state = TemplateEditContract.State(
            isDragInProgress = true,
            draggedExerciseId = "exercise1",
            dragTargetIndex = 1,
            dragConfig = DragConfig.Material3
        )

        val info = state.getDragAnimationInfo("exercise1")

        assertTrue("Should be dragging", info.isDragging)
        assertTrue("Should animate", info.shouldAnimate)
        assertEquals("Should have correct config", DragConfig.Material3, info.config)
        assertEquals("Should have full progress", 1f, info.dragProgress, 0.1f)
    }

    @Test
    fun `getDragAnimationInfo should return correct info for non-dragging item`() {
        val state = TemplateEditContract.State(
            isDragInProgress = true,
            draggedExerciseId = "exercise1",
            dragTargetIndex = 1
        )

        val info = state.getDragAnimationInfo("exercise2")

        assertFalse("Should not be dragging", info.isDragging)
        assertTrue("Should still animate (drop target)", info.shouldAnimate)
        assertEquals("Should have zero progress", 1f, info.dragProgress, 0.1f) // Still 1f because drag is in progress
    }

    @Test
    fun `createDragState should convert TemplateEditContract State correctly`() {
        val exercise = TemplateExerciseDto(
            id = "exercise1",
            name = "Test Exercise",
            sets = 3,
            reps = 12,
            targetWeight = 50f,
            restTimeSeconds = 60,
            customSets = emptyList()
        )

        val state = TemplateEditContract.State(
            exercises = listOf(exercise),
            isDragInProgress = true,
            draggedExerciseId = "exercise1",
            draggedItemIndex = 0,
            dragTargetIndex = 1,
            dragOffset = 100f,
            dragStartPosition = Offset(10f, 20f),
            currentDragPosition = Offset(15f, 120f),
            reorderingEnabled = true,
            shouldTriggerHaptic = true,
            showDropPreview = true,
            dropPreviewIndex = 1
        )

        val dragState = state.createDragState()

        assertTrue("Should be in progress", dragState.isDragInProgress)
        assertEquals("Should have correct item", exercise, dragState.draggedItem)
        assertEquals("Should have correct ID", "exercise1", dragState.draggedItemId)
        assertEquals("Should have correct indices", 0, dragState.draggedItemIndex)
        assertEquals("Should have correct target", 1, dragState.dragTargetIndex)
        assertEquals("Should have correct Y offset", 100f, dragState.dragOffset.y, 0.1f)
        assertEquals("Should have correct start position", Offset(10f, 20f), dragState.dragStartPosition)
        assertEquals("Should have correct current position", Offset(15f, 120f), dragState.currentPosition)
        assertTrue("Should be enabled", dragState.isDragEnabled)
        assertTrue("Should trigger haptic", dragState.shouldTriggerHaptic)
        assertTrue("Should show preview", dragState.showDropPreview)
        assertEquals("Should have correct preview index", 1, dragState.dropPreviewIndex)
    }

    @Test
    fun `updateFromDragState should update TemplateEditContract State correctly`() {
        val initialState = TemplateEditContract.State()

        val dragState = com.example.gymbro.features.workout.shared.components.drag.DragState<TemplateExerciseDto>(
            isDragInProgress = true,
            draggedItemId = "exercise1",
            draggedItemIndex = 0,
            dragTargetIndex = 1,
            dragOffset = Offset(0f, 100f),
            dragStartPosition = Offset(10f, 20f),
            currentPosition = Offset(15f, 120f),
            isDragEnabled = true,
            shouldTriggerHaptic = true,
            showDropPreview = true,
            dropPreviewIndex = 1
        )

        val updatedState = initialState.updateFromDragState(dragState)

        assertTrue("Should be in progress", updatedState.isDragInProgress)
        assertEquals("Should have correct ID", "exercise1", updatedState.draggedExerciseId)
        assertEquals("Should have correct indices", 0, updatedState.draggedItemIndex)
        assertEquals("Should have correct target", 1, updatedState.dragTargetIndex)
        assertEquals("Should have correct Y offset", 100f, updatedState.dragOffset, 0.1f)
        assertEquals("Should have correct start position", Offset(10f, 20f), updatedState.dragStartPosition)
        assertEquals("Should have correct current position", Offset(15f, 120f), updatedState.currentDragPosition)
        assertTrue("Should be enabled", updatedState.reorderingEnabled)
        assertTrue("Should trigger haptic", updatedState.shouldTriggerHaptic)
        assertTrue("Should show preview", updatedState.showDropPreview)
        assertEquals("Should have correct preview index", 1, updatedState.dropPreviewIndex)
    }

    @Test
    fun `canDropAt function should work correctly`() {
        val exercises = listOf(
            TemplateExerciseDto(id = "ex1", name = "Ex1", sets = 3, reps = 12, targetWeight = 50f, restTimeSeconds = 60, customSets = emptyList()),
            TemplateExerciseDto(id = "ex2", name = "Ex2", sets = 3, reps = 12, targetWeight = 50f, restTimeSeconds = 60, customSets = emptyList()),
            TemplateExerciseDto(id = "ex3", name = "Ex3", sets = 3, reps = 12, targetWeight = 50f, restTimeSeconds = 60, customSets = emptyList())
        )

        val state = TemplateEditContract.State(
            exercises = exercises,
            isDragInProgress = true,
            draggedExerciseId = "ex1",
            draggedItemIndex = 0
        )

        val dragState = state.createDragState()

        assertTrue("Should allow drop at index 1", dragState.canDropAt(1))
        assertTrue("Should allow drop at index 2", dragState.canDropAt(2))
        assertFalse("Should not allow drop at same index", dragState.canDropAt(0))
        assertFalse("Should not allow drop at negative index", dragState.canDropAt(-1))
        assertFalse("Should not allow drop beyond list", dragState.canDropAt(3))
    }
}
