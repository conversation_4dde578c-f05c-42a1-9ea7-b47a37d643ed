package com.example.gymbro.core.network.logging

import android.util.Log
import com.example.gymbro.core.di.qualifiers.ApplicationScope
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import timber.log.Timber
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 Token日志采集器 - RAW TOKEN批量采集核心
 *
 * 高性能的批量日志采集系统：
 * - 使用CNET-*前缀统一管理
 * - 批量缓冲避免逐token开销
 * - 异步处理不阻塞主流程
 * - 智能采样和背压控制
 *
 * 设计目标：
 * - 采集延迟 <1ms per batch
 * - 内存占用 <10MB total
 * - 支持高并发场景
 * - 零配置自动优化
 */
@Singleton
class TokenLogCollector @Inject constructor(
    @ApplicationScope private val applicationScope: CoroutineScope,
) {

    companion object {
        private const val TAG = "TokenLogCollector"

        // 缓冲区配置
        private const val BUFFER_CAPACITY = 100
        private const val MAX_BUFFER_SIZE_BYTES = 1024 * 1024 // 1MB
        private const val FLUSH_INTERVAL_MS = 500L
        private const val MAX_FLUSH_DELAY_MS = 2000L

        // 日志标签 - 使用CNET前缀
        object LogTags {
            const val TOKEN_RECEIVED = "CNET-TOKEN-RECV" // 接收的RAW TOKEN
            const val TOKEN_OUTPUT = "CNET-TOKEN-OUT" // 输出的RAW TOKEN
            const val TOKEN_STATS = "CNET-TOKEN-STATS" // 统计信息
            const val TOKEN_BATCH = "CNET-TOKEN-BATCH" // 批量操作
            const val TOKEN_ERROR = "CNET-TOKEN-ERROR" // 错误信息
        }
    }

    // 双缓冲区设计
    private val receivedTokenBuffer = TokenBuffer(
        capacity = BUFFER_CAPACITY,
        maxSizeBytes = MAX_BUFFER_SIZE_BYTES.toLong(),
    )

    private val outputTokenBuffer = TokenBuffer(
        capacity = BUFFER_CAPACITY,
        maxSizeBytes = MAX_BUFFER_SIZE_BYTES.toLong(),
    )

    // 统计信息
    private val totalReceivedTokens = AtomicLong(0)
    private val totalOutputTokens = AtomicLong(0)
    private val totalBatchesFlushed = AtomicLong(0)
    private val totalErrorsLogged = AtomicLong(0)

    // 状态监控
    private val _collectorStatus = MutableSharedFlow<CollectorStatus>(replay = 1)
    val collectorStatus: SharedFlow<CollectorStatus> = _collectorStatus.asSharedFlow()

    // 异步处理协程
    private var flushJob: Job? = null
    private var isStarted = false

    init {
        startAsyncProcessing()
    }

    /**
     * 采集接收的RAW TOKEN（批量）
     *
     * @param tokens token列表
     * @param source 来源标识（HTTP_SSE/WEBSOCKET等）
     * @param conversationId 会话ID
     */
    suspend fun collectReceivedTokens(
        tokens: List<String>,
        source: String,
        conversationId: String,
    ) {
        if (tokens.isEmpty()) return

        try {
            val addedCount = receivedTokenBuffer.addBatch(
                tokens = tokens,
                source = source,
                conversationId = conversationId,
                type = TokenType.RECEIVED,
            )

            totalReceivedTokens.addAndGet(addedCount.toLong())

            // 检查是否需要立即刷新
            if (receivedTokenBuffer.shouldFlush()) {
                triggerFlush()
            }
        } catch (e: Exception) {
            logError("采集接收token失败", e, source, conversationId)
        }
    }

    /**
     * 采集输出的RAW TOKEN（批量）
     *
     * @param tokens token列表
     * @param target 目标标识（ThinkingBox等）
     * @param conversationId 会话ID
     */
    suspend fun collectOutputTokens(
        tokens: List<String>,
        target: String,
        conversationId: String,
    ) {
        if (tokens.isEmpty()) return

        try {
            val addedCount = outputTokenBuffer.addBatch(
                tokens = tokens,
                source = target,
                conversationId = conversationId,
                type = TokenType.OUTPUT,
            )

            totalOutputTokens.addAndGet(addedCount.toLong())

            // 检查是否需要立即刷新
            if (outputTokenBuffer.shouldFlush()) {
                triggerFlush()
            }
        } catch (e: Exception) {
            logError("采集输出token失败", e, target, conversationId)
        }
    }

    /**
     * 采集单个接收token（便捷方法）
     */
    suspend fun collectReceivedToken(token: String, source: String, conversationId: String) {
        collectReceivedTokens(listOf(token), source, conversationId)
    }

    /**
     * 采集单个输出token（便捷方法）
     */
    suspend fun collectOutputToken(token: String, target: String, conversationId: String) {
        collectOutputTokens(listOf(token), target, conversationId)
    }

    /**
     * 获取采集器状态
     */
    suspend fun getStatus(): CollectorStatus {
        val receivedStatus = receivedTokenBuffer.getStatus()
        val outputStatus = outputTokenBuffer.getStatus()

        return CollectorStatus(
            isActive = isStarted,
            totalReceivedTokens = totalReceivedTokens.get(),
            totalOutputTokens = totalOutputTokens.get(),
            totalBatchesFlushed = totalBatchesFlushed.get(),
            totalErrorsLogged = totalErrorsLogged.get(),
            receivedBufferUsage = receivedStatus.usagePercentage,
            outputBufferUsage = outputStatus.usagePercentage,
            memoryUsageMB = (receivedStatus.sizeBytes + outputStatus.sizeBytes) / (1024 * 1024),
            lastFlushTimestamp = System.currentTimeMillis(),
        )
    }

    /**
     * 启动异步处理
     */
    private fun startAsyncProcessing() {
        if (isStarted) return

        flushJob = applicationScope.launch {
            isStarted = true

            Timber.tag(LogTags.TOKEN_STATS).i("🚀 TokenLogCollector已启动，定时刷新间隔=${FLUSH_INTERVAL_MS}ms")

            while (isActive) {
                try {
                    delay(FLUSH_INTERVAL_MS)

                    // 检查并刷新缓冲区
                    flushBuffersIfNeeded()

                    // 更新状态
                    updateCollectorStatus()
                } catch (e: CancellationException) {
                    Timber.tag(LogTags.TOKEN_STATS).i("📴 TokenLogCollector已停止")
                    break
                } catch (e: Exception) {
                    Timber.tag(LogTags.TOKEN_ERROR).e(e, "❌ 异步处理失败")
                }
            }

            isStarted = false
        }
    }

    /**
     * 触发立即刷新
     */
    private fun triggerFlush() {
        applicationScope.launch {
            flushBuffersToLog()
        }
    }

    /**
     * 检查并刷新缓冲区
     */
    private suspend fun flushBuffersIfNeeded() {
        val shouldFlushReceived = receivedTokenBuffer.shouldFlush(MAX_FLUSH_DELAY_MS)
        val shouldFlushOutput = outputTokenBuffer.shouldFlush(MAX_FLUSH_DELAY_MS)

        if (shouldFlushReceived || shouldFlushOutput) {
            flushBuffersToLog()
        }
    }

    /**
     * 将缓冲区数据刷新到日志
     */
    private suspend fun flushBuffersToLog() {
        try {
            // 并行刷新两个缓冲区
            val receivedEntries = applicationScope.async { receivedTokenBuffer.flush() }
            val outputEntries = applicationScope.async { outputTokenBuffer.flush() }

            val received = receivedEntries.await()
            val output = outputEntries.await()

            // 记录接收token日志
            if (received.isNotEmpty()) {
                logTokenBatch(received, TokenType.RECEIVED)
            }

            // 记录输出token日志
            if (output.isNotEmpty()) {
                logTokenBatch(output, TokenType.OUTPUT)
            }

            if (received.isNotEmpty() || output.isNotEmpty()) {
                totalBatchesFlushed.incrementAndGet()

                Timber.tag(LogTags.TOKEN_BATCH).d(
                    "📊 批量刷新完成: received=${received.size}, output=${output.size}",
                )
            }
        } catch (e: Exception) {
            Timber.tag(LogTags.TOKEN_ERROR).e(e, "❌ 刷新缓冲区失败")
            totalErrorsLogged.incrementAndGet()
        }
    }

    /**
     * 记录token批量日志
     */
    private fun logTokenBatch(entries: List<TokenEntry>, type: TokenType) {
        if (entries.isEmpty()) return

        // 按会话ID分组
        val groupedByConversation = entries.groupBy { it.conversationId }

        groupedByConversation.forEach { (conversationId, tokens) ->
            val totalSize = tokens.sumOf { it.sizeBytes }
            val sources = tokens.map { it.source }.distinct()
            val timeRange = tokens.minOf { it.timestamp } to tokens.maxOf { it.timestamp }

            val logTag = when (type) {
                TokenType.RECEIVED -> LogTags.TOKEN_RECEIVED
                TokenType.OUTPUT -> LogTags.TOKEN_OUTPUT
            }

            val direction = when (type) {
                TokenType.RECEIVED -> "→"
                TokenType.OUTPUT -> "←"
            }

            // 结构化批量日志
            Timber.tag(logTag).i(
                "📦 [$conversationId] ${sources.joinToString(",")} $direction " +
                    "Batch[${tokens.size} tokens, ${formatBytes(totalSize)}, " +
                    "${timeRange.second - timeRange.first}ms]",
            )

            // 详细token内容（仅在debug模式）
            if (Log.isLoggable(logTag, Log.DEBUG)) {
                tokens.take(5).forEach { entry -> // 只显示前5个token
                    Timber.tag(logTag).d(
                        "  └─ [${entry.timestamp}] \"${entry.content.take(
                            50,
                        )}${if (entry.content.length > 50) "..." else ""}\"",
                    )
                }
                if (tokens.size > 5) {
                    Timber.tag(logTag).d("  └─ ... 还有${tokens.size - 5}个token")
                }
            }
        }
    }

    /**
     * 记录错误日志
     */
    private fun logError(
        message: String,
        error: Throwable,
        source: String,
        conversationId: String,
    ) {
        Timber.tag(LogTags.TOKEN_ERROR).e(
            error,
            "❌ $message: source=$source, conversation=$conversationId",
        )
        totalErrorsLogged.incrementAndGet()
    }

    /**
     * 更新采集器状态
     */
    private suspend fun updateCollectorStatus() {
        val status = getStatus()
        _collectorStatus.tryEmit(status)

        // 定期输出统计信息
        if (totalBatchesFlushed.get() % 10 == 0L && totalBatchesFlushed.get() > 0) {
            Timber.tag(LogTags.TOKEN_STATS).i(
                "📈 TokenLogCollector统计: " +
                    "received=${status.totalReceivedTokens}, " +
                    "output=${status.totalOutputTokens}, " +
                    "batches=${status.totalBatchesFlushed}, " +
                    "memory=${status.memoryUsageMB}MB",
            )
        }
    }

    /**
     * 格式化字节数
     */
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            else -> "${bytes / (1024 * 1024)}MB"
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        flushJob?.cancel()
        isStarted = false

        // 最后一次刷新
        applicationScope.launch {
            flushBuffersToLog()
            receivedTokenBuffer.clear()
            outputTokenBuffer.clear()
        }

        Timber.tag(LogTags.TOKEN_STATS).i("🧹 TokenLogCollector已清理")
    }
}

/**
 * 📊 采集器状态信息
 */
data class CollectorStatus(
    val isActive: Boolean,
    val totalReceivedTokens: Long,
    val totalOutputTokens: Long,
    val totalBatchesFlushed: Long,
    val totalErrorsLogged: Long,
    val receivedBufferUsage: Int, // 百分比
    val outputBufferUsage: Int, // 百分比
    val memoryUsageMB: Long,
    val lastFlushTimestamp: Long,
)