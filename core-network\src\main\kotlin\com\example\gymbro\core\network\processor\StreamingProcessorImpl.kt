package com.example.gymbro.core.network.processor

import com.example.gymbro.core.logging.GymBroLogTags
import com.example.gymbro.core.network.detector.ContentType
import kotlinx.serialization.json.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 流式处理器实现 - 统一SSE解析版
 *
 * 新职责：
 * - 统一SSE响应解析（从AiResponseReceiver迁移）
 * - 协议检测和内容提取
 * - 直接处理和路由到消费者
 */
@Singleton
class StreamingProcessorImpl @Inject constructor(
    private val contentExtractor: ContentExtractor,
    private val outputSanitizer: OutputSanitizer,
) : StreamingProcessor {

    companion object {
        private val TAG = GymBroLogTags.CoreNetwork.PROCESSOR_STREAM

        // 🔥 【迁移自AiResponseReceiver】SSE解析配置
        private val JSON_PARSER = kotlinx.serialization.json.Json {
            ignoreUnknownKeys = true
            isLenient = true
        }
    }

    // 🏷️ 【日志统计】批量处理统计
    @Volatile
    private var totalTokensProcessed = 0L
    @Volatile
    private var totalParseErrors = 0L
    @Volatile
    private var lastStatsLogTime = 0L

    private val statsLogInterval = 30_000L // 30秒记录一次统计

    override fun processImmediate(
        token: String,
        contentType: ContentType,
        messageId: String,
    ): String {
        return try {
            totalTokensProcessed++

            // 🏷️ 【日志统计】定期记录批量处理统计
            logBatchStatsIfNeeded()

            // 简化处理：根据内容类型直接处理
            val result = when (contentType) {
                ContentType.JSON_SSE -> processJsonSse(token)
                ContentType.XML_THINKING -> {
                    Timber.tag("CNET-PROCESSOR-Fix").e("📋 [XML_THINKING处理] 直接返回")
                    token // 直接返回，让ThinkingBox处理
                }
                ContentType.PLAIN_TEXT -> {
                    Timber.tag("CNET-PROCESSOR-Fix").e("📋 [PLAIN_TEXT处理] 直接返回纯文本")
                    token // 🔥 【修复关键】纯文本直接返回，无需JSON处理
                }
                else -> {
                    Timber.tag("CNET-PROCESSOR-Fix").e("📋 [其他类型处理] 直接返回: $contentType")
                    token // 其他类型直接返回
                }
            }

            // 🔥 【调试追踪】记录处理结果
            Timber.tag(
                "CNET-PROCESSOR-Fix",
            ).e(
                "✅ [处理结果] 输入='${token.take(
                    20,
                )}...', 输出='${result.take(20)}...', 长度变化=${token.length}->${result.length}",
            )

            result
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "处理失败，返回原token")
            token // 失败时返回原token
        }
    }

    /**
     * 简化JSON SSE处理
     */
    private fun processJsonSse(token: String): String {
        return try {
            val content = contentExtractor.extractJsonSseContent(token)
            outputSanitizer.sanitizeForDirectOutput(content)
        } catch (e: Exception) {
            ""
        }
    }
}

/**
 * 📄 内容提取器接口
 */
interface ContentExtractor {
    /**
     * 即时提取JSON SSE内容
     */
    fun extractJsonSseContent(token: String): String

    /**
     * 即时提取JSON流内容
     */
    fun extractJsonStreamContent(token: String): String

    /**
     * 提取WebSocket内容
     */
    fun extractWebSocketContent(token: String): String
}

/**
 * 🛡️ 输出净化器接口
 */
interface OutputSanitizer {
    /**
     * 为直接输出净化内容
     */
    fun sanitizeForDirectOutput(content: String): String
}

/**
 * 📄 内容提取器实现 - 从protocol目录移动过来
 *
 * 优化：移除缓冲机制，改为即时提取
 */
@Singleton
class ContentExtractorImpl @Inject constructor(
    private val json: kotlinx.serialization.json.Json,
) : ContentExtractor {

    companion object {
        private const val TAG = "ContentExtractor"
        private const val BATCH_SIZE = 100 // 100 token 批量输出
    }

    // 🔥 【批量日志】Token 收集器
    private val tokenBatch = mutableListOf<String>()
    private var lastFlushTime = System.currentTimeMillis()
    private val FLUSH_TIMEOUT_MS = 10000L // 10秒超时强制刷新，给更多时间收集
    private var totalTokensProcessed = 0 // 总处理token计数器

    /**
     * 🔥 【批量日志】添加 token 到批量收集器
     */
    private fun addTokenToBatch(message: String) {
        synchronized(tokenBatch) {
            tokenBatch.add(message)
            totalTokensProcessed++

            val currentTime = System.currentTimeMillis()
            val timeSinceLastFlush = currentTime - lastFlushTime

            // 🔥 【修复批量逻辑】严格的批量触发条件
            val shouldFlush = when {
                // 条件1: 达到真正的批量大小
                tokenBatch.size >= BATCH_SIZE -> {
                    Timber.tag("CNET-JSON-TRACE").d("🎯 批量触发: 达到${BATCH_SIZE}个token")
                    true
                }
                // 条件2: 超时且有足够的token（至少20个）
                timeSinceLastFlush > FLUSH_TIMEOUT_MS && tokenBatch.size >= 20 -> {
                    Timber.tag(
                        "CNET-JSON-TRACE",
                    ).d("⏰ 超时触发: ${tokenBatch.size}个token, 超时${timeSinceLastFlush}ms")
                    true
                }
                else -> false
            }

            if (shouldFlush) {
                flushTokenBatch()
                lastFlushTime = currentTime
            }
        }
    }

    /**
     * 🔥 【批量日志】刷新 token 批量输出
     */
    private fun flushTokenBatch() {
        if (tokenBatch.isEmpty()) return

        // 🔥 【修复】提高最小阈值，确保批量输出有意义
        if (tokenBatch.size < 10) {
            Timber.tag("CNET-JSON-TRACE").d("⚠️ 批量太小(${tokenBatch.size}个)，跳过输出")
            return
        }

        // 🔥 【修复】生成有意义的批量摘要
        val batchSize = tokenBatch.size
        val totalProcessed = totalTokensProcessed
        val timeSpan = System.currentTimeMillis() - lastFlushTime

        // 统计不同类型的token
        val inputTokens = tokenBatch.count { it.contains("[JSON SSE输入]") }
        val skippedTokens = tokenBatch.count { it.contains("[JSON SSE跳过]") }
        val processedTokens = tokenBatch.count { it.contains("[JSON SSE处理]") }
        val errorTokens = tokenBatch.count { it.contains("[JSON SSE解析失败]") }

        // 生成摘要信息
        val summary = buildString {
            appendLine("📊 批量摘要: ${batchSize}个token条目 (总计${totalProcessed}个)")
            appendLine("⏱️ 时间跨度: ${timeSpan}ms")
            appendLine(
                "📈 统计: 输入${inputTokens}个, 跳过${skippedTokens}个, 处理${processedTokens}个, 错误${errorTokens}个",
            )
            if (batchSize <= 20) {
                appendLine("📝 详细内容:")
                tokenBatch.forEach { appendLine("  $it") }
            } else {
                appendLine("📝 前10条内容:")
                tokenBatch.take(10).forEach { appendLine("  $it") }
                appendLine("  ... 还有${batchSize - 10}条 ...")
            }
        }

        Timber.tag("CNET-JSON-TRACE").e("📦 [批量输出 $batchSize tokens]\n$summary")

        tokenBatch.clear()
    }

    /**
     * 🔥 【批量日志】强制刷新 token 批量输出（忽略最小阈值）
     */
    private fun forceFlushTokenBatch() {
        if (tokenBatch.isEmpty()) return

        // 🔥 【强制刷新】无论数量多少都输出，用于流结束时的清理
        val batchSize = tokenBatch.size
        val totalProcessed = totalTokensProcessed
        val timeSpan = System.currentTimeMillis() - lastFlushTime

        // 统计不同类型的token
        val inputTokens = tokenBatch.count { it.contains("[JSON SSE输入]") }
        val skippedTokens = tokenBatch.count { it.contains("[JSON SSE跳过]") }
        val processedTokens = tokenBatch.count { it.contains("[JSON SSE处理]") }
        val errorTokens = tokenBatch.count { it.contains("[JSON SSE解析失败]") }

        // 生成摘要信息
        val summary = buildString {
            appendLine("📊 最终批量摘要: ${batchSize}个token条目 (总计${totalProcessed}个)")
            appendLine("⏱️ 时间跨度: ${timeSpan}ms")
            appendLine(
                "📈 统计: 输入${inputTokens}个, 跳过${skippedTokens}个, 处理${processedTokens}个, 错误${errorTokens}个",
            )
            appendLine("📝 所有内容:")
            tokenBatch.forEach { appendLine("  $it") }
        }

        Timber.tag("CNET-JSON-TRACE").e("🏁 [强制输出 $batchSize tokens]\n$summary")

        tokenBatch.clear()
    }

    /**
     * 🔥 【批量日志】强制刷新剩余 token（用于清理）
     */
    fun forceFlushRemainingTokens() {
        synchronized(tokenBatch) {
            if (tokenBatch.isNotEmpty()) {
                Timber.tag("CNET-JSON-TRACE").d("🧹 外部调用强制刷新剩余${tokenBatch.size}个token")
                forceFlushTokenBatch() // 使用强制刷新，忽略最小阈值
            }
        }
    }

    /**
     * 🔥 【架构重构】统一SSE解析入口 - 从AiResponseReceiver迁移
     *
     * **新数据流说明**：
     * 1. Core-Network成为SSE解析的唯一入口点
     * 2. 接收原始SSE响应：`data: {"choices":[{"delta":{"content":"太"}}]}`
     * 3. 解析并提取纯文本内容：`"太"`
     * 4. 直接路由到ThinkingBox等消费者
     */
    override fun extractJsonSseContent(token: String): String {
        // 🔥 【批量日志】收集 token 而不是立即输出
        addTokenToBatch("🔍 [统一SSE解析] token='${token.take(50)}${if (token.length > 50) "..." else ""}'")

        // 🔥 【统一解析点】处理SSE格式的响应
        return if (token.startsWith("data: ")) {
            parseSseResponseLine(token)
        } else {
            // 已经是纯文本内容，直接传递
            addTokenToBatch("✅ [纯文本传递] 直接传递: '${token.take(50)}...'")
            token
        }
    }

    /**
     * 🔥 【迁移自AiResponseReceiver】解析单行SSE响应
     *
     * 从 AiResponseReceiver.parseSseResponse() 方法迁移而来
     */
    private fun parseSseResponseLine(sseToken: String): String {
        try {
            val data = sseToken.substring(6) // 移除"data: "前缀
            if (data.trim() == "[DONE]") {
                addTokenToBatch("🏁 [SSE完成] 接收到完成信号")
                return ""
            }

            // 🔥 【核心JSON解析】使用统一的JSON解析器
            val jsonElement = JSON_PARSER.parseToJsonElement(data)
            val choices = jsonElement.jsonObject["choices"]?.jsonArray

            if (choices != null && choices.isNotEmpty()) {
                val delta = choices[0].jsonObject["delta"]?.jsonObject
                val content = delta?.get("content")?.jsonPrimitive?.content

                if (!content.isNullOrEmpty()) {
                    addTokenToBatch("✅ [JSON解析成功] 提取内容: '${content.take(30)}...'")
                    return content
                }
            }

            addTokenToBatch("⚠️ [JSON解析] 未找到有效内容")
            return ""

        } catch (e: Exception) {
            addTokenToBatch("❌ [JSON解析失败] ${e.message}")
            Timber.tag(TAG).w(e, "SSE JSON解析失败: $sseToken")
            return ""
        }
    }

        val jsonPart = token.removePrefix("data: ").trim()
        if (jsonPart == "[DONE]") {
            addTokenToBatch("✅ [JSON SSE完成] 收到[DONE]标记")
            // 🔥 【修复】流结束时强制刷新剩余token，无论数量多少
            synchronized(tokenBatch) {
                if (tokenBatch.isNotEmpty()) {
                    Timber.tag("CNET-JSON-TRACE").d("🏁 流结束，强制刷新剩余${tokenBatch.size}个token")
                    forceFlushTokenBatch() // 使用强制刷新，忽略最小阈值
                }
            }
            return ""
        }

        return try {
            val jsonElement = json.parseToJsonElement(jsonPart)
            val jsonObj = jsonElement.jsonObject

            // 🔥 【增强JSON SSE解析】支持更多格式和容错处理
            val result = when {
                // OpenAI格式: {"choices":[{"delta":{"content":"text"}}]}
                jsonObj.containsKey("choices") -> {
                    val choices = jsonObj["choices"]?.jsonArray
                    val firstChoice = choices?.firstOrNull()?.jsonObject
                    val delta = firstChoice?.get("delta")?.jsonObject
                    delta?.get("content")?.jsonPrimitive?.content ?: ""
                }

                // Claude格式: {"delta":{"text":"content"}}
                jsonObj.containsKey("delta") -> {
                    val delta = jsonObj["delta"]?.jsonObject
                    delta?.get("text")?.jsonPrimitive?.content
                        ?: delta?.get("content")?.jsonPrimitive?.content
                        ?: ""
                }

                // 通用格式: {"content":"text"} 或 {"text":"content"}
                jsonObj.containsKey("content") -> {
                    jsonObj["content"]?.jsonPrimitive?.content ?: ""
                }

                jsonObj.containsKey("text") -> {
                    jsonObj["text"]?.jsonPrimitive?.content ?: ""
                }

                // 🔥 【新增】支持更多可能的字段
                jsonObj.containsKey("message") -> {
                    jsonObj["message"]?.jsonPrimitive?.content ?: ""
                }

                jsonObj.containsKey("data") -> {
                    val data = jsonObj["data"]
                    when {
                        data is JsonPrimitive -> data.content
                        data is JsonObject -> {
                            val dataObj = data.jsonObject
                            dataObj["content"]?.jsonPrimitive?.content
                                ?: dataObj["text"]?.jsonPrimitive?.content
                                ?: ""
                        }
                        else -> ""
                    }
                }

                else -> {
                    // 🔥 【调试】记录未识别的JSON结构
                    addTokenToBatch("⚠️ [未识别JSON结构] keys: ${jsonObj.keys}")
                    ""
                }
            }

            // 🔥 【批量日志】记录提取结果
            if (result.isNotEmpty()) {
                addTokenToBatch("✅ [JSON SSE提取成功] content='${result.take(100)}...'")
            } else {
                addTokenToBatch("⚠️ [JSON SSE提取为空] jsonPart='${jsonPart.take(200)}...'")
            }

            result
        } catch (e: Exception) {
            totalParseErrors++
            addTokenToBatch("❌ [JSON SSE解析失败] jsonPart='${jsonPart.take(200)}...' error=${e.message}")
            ""
        }
    }

    /**
     * 即时提取JSON流内容
     */
    override fun extractJsonStreamContent(token: String): String {
        // 🔥 【批量日志】收集 JSON 流处理日志
        addTokenToBatch("🔍 [JSON流输入] token='${token.take(200)}...'")

        return try {
            // 🔥 【增强JSON解析】支持更多JSON格式和容错处理
            val trimmedToken = token.trim()

            // 检查是否为空或无效JSON
            if (trimmedToken.isEmpty() || (!trimmedToken.startsWith("{") && !trimmedToken.startsWith("["))) {
                addTokenToBatch("⚠️ [JSON流跳过] 不是有效JSON格式: '$trimmedToken'")
                return ""
            }

            val jsonElement = json.parseToJsonElement(trimmedToken)

            val result = when {
                // 处理JSON对象
                jsonElement is JsonObject -> {
                    val jsonObj = jsonElement.jsonObject

                    // 尝试多种可能的内容字段
                    jsonObj["text"]?.jsonPrimitive?.content
                        ?: jsonObj["message"]?.jsonPrimitive?.content
                        ?: jsonObj["content"]?.jsonPrimitive?.content
                        ?: jsonObj["data"]?.jsonPrimitive?.content
                        ?: jsonObj["chunk"]?.jsonPrimitive?.content
                        // 🔥 【新增】支持嵌套结构
                        ?: jsonObj["response"]?.jsonObject?.get("text")?.jsonPrimitive?.content
                        ?: jsonObj["result"]?.jsonObject?.get("content")?.jsonPrimitive?.content
                        ?: ""
                }

                // 处理JSON数组（可能包含多个消息）
                jsonElement is JsonArray -> {
                    val jsonArray = jsonElement.jsonArray
                    val contents = mutableListOf<String>()

                    jsonArray.forEach { item ->
                        if (item is JsonObject) {
                            val obj = item.jsonObject
                            val content = obj["text"]?.jsonPrimitive?.content
                                ?: obj["content"]?.jsonPrimitive?.content
                                ?: obj["message"]?.jsonPrimitive?.content
                            if (!content.isNullOrEmpty()) {
                                contents.add(content)
                            }
                        }
                    }

                    contents.joinToString("")
                }

                else -> ""
            }

            // 🔥 【批量日志】记录提取结果
            if (result.isNotEmpty()) {
                addTokenToBatch("✅ [JSON流提取成功] content='${result.take(100)}...'")
            } else {
                addTokenToBatch("⚠️ [JSON流提取为空] 解析的JSON结构: ${jsonElement.toString().take(200)}...")
            }

            result
        } catch (e: Exception) {
            addTokenToBatch("❌ [JSON流解析失败] token='${token.take(200)}...' error=${e.message}")
            ""
        }
    }

    /**
     * 提取WebSocket内容
     */
    override fun extractWebSocketContent(token: String): String {
        // 简化的WebSocket帧解析
        return if (token.startsWith("WS:")) {
            token.removePrefix("WS:")
        } else {
            token
        }
    }
}

/**
 * 🛡️ 输出净化器实现 - 从security目录重命名
 *
 * 优化：简化为直接输出净化，移除XML转义（让ThinkingBox处理）
 */
@Singleton
class OutputSanitizerImpl @Inject constructor(
    private val piiSanitizer: com.example.gymbro.core.network.security.PiiSanitizer,
) : OutputSanitizer {

    /**
     * 为直接输出净化内容
     *
     * 🔥 关键优化：移除XML转义，让ThinkingBox自己处理
     * 减少20ms延迟
     */
    override fun sanitizeForDirectOutput(content: String): String {
        if (content.isEmpty()) return ""

        // 仅做PII过滤，移除XML转义
        return piiSanitizer.sanitizeContent(content)
    }

    /**
     * 🏷️ 【日志统计】定期记录批量处理统计
     */
    private fun logBatchStatsIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastStatsLogTime >= statsLogInterval) {
            val errorRate = if (totalTokensProcessed > 0) {
                (totalParseErrors * 100.0 / totalTokensProcessed).toInt()
            } else 0

            Timber.tag(TAG).i(
                "📊 [批量统计] 已处理tokens=$totalTokensProcessed, " +
                "解析错误=$totalParseErrors, 错误率=${errorRate}%"
            )

            lastStatsLogTime = currentTime
        }
    }
}
