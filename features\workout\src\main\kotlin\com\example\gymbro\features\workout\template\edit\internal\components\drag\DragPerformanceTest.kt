package com.example.gymbro.features.workout.template.edit.internal.components.drag

import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import kotlin.system.measureTimeMillis

@RunWith(AndroidJUnit4::class)
class DragPerformanceTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun `drag performance test with large list`() = runTest {
        val largeList = (1..1000).map { "Item $it" }
        val dragHandler = UnifiedDragHandlerImpl<String>()
        dragHandler.setItems(largeList)

        val dragTime = measureTimeMillis {
            dragHandler.startDrag("Item 500", 499)
            dragHandler.updateDragPosition(50)
            dragHandler.commitDrag()
        }

        // 性能基准：拖拽操作应在50ms内完成
        assert(dragTime < 50) { "Drag operation took $dragTime ms, which exceeds performance threshold" }
    }
}
