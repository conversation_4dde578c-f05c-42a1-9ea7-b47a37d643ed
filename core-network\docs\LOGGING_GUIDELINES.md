# Core-Network 日志管理规范

## 🏷️ 统一日志标签系统

### 标签管理位置
Core-Network模块的日志标签现在由 **Core模块的TimberManager** 统一管理，位于：
```
core/src/main/kotlin/com/example/gymbro/core/logging/TimberManager.kt
```

### 使用方式
```kotlin
import com.example.gymbro.core.logging.GymBroLogTags

// 使用统一的日志标签
Timber.tag(GymBroLogTags.CoreNetwork.SERVICE_UNIFIED_AI).i("统一AI响应服务日志")
Timber.tag(GymBroLogTags.CoreNetwork.PROCESSOR_STREAM).d("流式处理器日志")
Timber.tag(GymBroLogTags.CoreNetwork.OUTPUT_DIRECT).i("直接输出通道日志")
```

### 标准日志标签
```kotlin
// 核心服务层
GymBroLogTags.CoreNetwork.SERVICE_UNIFIED_AI = "CNET-SERVICE-UnifiedAi"

// 处理器层
GymBroLogTags.CoreNetwork.PROCESSOR_STREAM = "CNET-PROCESSOR-Stream"
GymBroLogTags.CoreNetwork.PROCESSOR_CONTENT = "CNET-PROCESSOR-Content"
GymBroLogTags.CoreNetwork.PROCESSOR_SANITIZER = "CNET-PROCESSOR-Sanitizer"

// 输出层
GymBroLogTags.CoreNetwork.OUTPUT_DIRECT = "CNET-OUTPUT-Direct"

// 网络层
GymBroLogTags.CoreNetwork.REST_CLIENT = "CNET-REST-Client"
GymBroLogTags.CoreNetwork.INTERCEPTOR_AUTH = "CNET-INTERCEPTOR-Auth"
GymBroLogTags.CoreNetwork.INTERCEPTOR_NETWORK = "CNET-INTERCEPTOR-Network"
GymBroLogTags.CoreNetwork.INTERCEPTOR_RETRY = "CNET-INTERCEPTOR-Retry"
GymBroLogTags.CoreNetwork.INTERCEPTOR_LOGGING = "CNET-INTERCEPTOR-Logging"

// 配置层
GymBroLogTags.CoreNetwork.CONFIG_MANAGER = "CNET-CONFIG-Manager"

// 监控层
GymBroLogTags.CoreNetwork.MONITOR_NETWORK = "CNET-MONITOR-Network"
GymBroLogTags.CoreNetwork.MONITOR_PERFORMANCE = "CNET-MONITOR-Performance"

// 安全层
GymBroLogTags.CoreNetwork.SECURITY_PII = "CNET-SECURITY-Pii"

// 状态层
GymBroLogTags.CoreNetwork.STATE_NETWORK = "CNET-STATE-Network"

// 依赖注入层
GymBroLogTags.CoreNetwork.DI_MODULE = "CNET-DI-Module"
```

### 标签格式规范
所有core-network模块的日志标签遵循格式：`CNET-{子包名}-{功能}`

## 📊 日志内容规范

### ✅ 应该记录的内容

#### 1. 汇总性统计信息
```kotlin
// ✅ 正确示例：批量处理统计
Timber.tag(GymBroLogTags.CoreNetwork.PROCESSOR_STREAM).i(
    "📊 [批量统计] 已处理tokens=$totalTokensProcessed, " +
    "解析错误=$totalParseErrors, 错误率=${errorRate}%"
)

// ✅ 正确示例：输出统计
Timber.tag(GymBroLogTags.CoreNetwork.OUTPUT_DIRECT).i(
    "📊 [输出统计] 已输出tokens=$totalTokensOutput, " +
    "活跃订阅者=$activeSubscribers, 总订阅者=$totalSubscribers"
)
```

#### 2. 关键业务流程节点
```kotlin
// ✅ 正确示例：请求生命周期
Timber.tag(GymBroLogTags.CoreNetwork.SERVICE_UNIFIED_AI).i(
    "🚀 [请求开始] messageId=$messageId, 开始统一AI响应处理"
)

Timber.tag(GymBroLogTags.CoreNetwork.SERVICE_UNIFIED_AI).i(
    "✅ [请求完成] messageId=$messageId, tokens=$tokenCount, 耗时=${processingTime}ms"
)
```

#### 3. 性能指标和监控数据
```kotlin
// ✅ 正确示例：性能监控
Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).i(
    "✅ [请求成功] POST 响应=${responseBody.length}字符, 耗时=${processingTime}ms"
)

Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).e(
    "❌ [请求失败] POST 错误=${result.error}, 耗时=${processingTime}ms, 错误率=${errorRate}%"
)
```

#### 4. 状态变化和配置更新
```kotlin
// ✅ 正确示例：状态变化
Timber.tag(GymBroLogTags.CoreNetwork.OUTPUT_DIRECT).d(
    "🔗 [订阅] conversationId=$conversationId, 活跃订阅者=$activeSubscribers"
)

// ✅ 正确示例：组件创建
Timber.tag(GymBroLogTags.CoreNetwork.DI_MODULE).d(
    "🔧 [组件创建] UnifiedAiResponseService - 统一AI响应服务"
)
```

### ❌ 禁止记录的内容

#### 1. Token级别的详细内容
```kotlin
// ❌ 错误示例：记录具体token内容
Timber.tag(TAG).v("📤 token内容: $tokenContent") // 禁止

// ✅ 正确示例：记录token统计
Timber.tag(TAG).d("📤 [token处理] 长度=${token.length}字符") // 允许
```

#### 2. 敏感数据和用户隐私
```kotlin
// ❌ 错误示例：记录敏感信息
Timber.tag(TAG).d("API Key: $apiKey") // 禁止
Timber.tag(TAG).d("用户消息: $userMessage") // 禁止

// ✅ 正确示例：脱敏记录
Timber.tag(TAG).d("🔑 [认证添加] Bearer token已添加") // 允许
```

#### 3. 过度详细的调试信息
```kotlin
// ❌ 错误示例：每个token的处理日志
tokens.forEach { token ->
    Timber.tag(TAG).v("处理token: $token") // 禁止
}

// ✅ 正确示例：批量处理汇总
Timber.tag(TAG).i("📊 [批量处理] 处理了${tokens.size}个tokens") // 允许
```

## 📈 日志级别使用指导

### ERROR 级别
- 系统错误、异常情况
- API请求失败
- 网络连接异常
- 数据解析错误

```kotlin
Timber.tag(GymBroLogTags.CoreNetwork.SERVICE_UNIFIED_AI).e(
    "❌ [请求失败] messageId=$messageId, 错误=${e.message}"
)
```

### WARN 级别
- 警告信息、性能问题
- 响应时间过长
- 重试次数过多
- 配置异常

```kotlin
Timber.tag(GymBroLogTags.CoreNetwork.INTERCEPTOR_RETRY).w(
    "🔄 [重试] HTTP ${response.code}, 第${attempt + 1}次重试"
)
```

### INFO 级别
- 关键业务流程、状态变化
- 请求开始/完成
- 配置变更
- 服务状态变化

```kotlin
Timber.tag(GymBroLogTags.CoreNetwork.OUTPUT_DIRECT).i(
    "📊 [输出统计] 已输出tokens=$totalTokensOutput"
)
```

### DEBUG 级别
- 详细调试信息（仅开发环境）
- 内部状态变化
- 详细性能指标
- 调试追踪信息

```kotlin
Timber.tag(GymBroLogTags.CoreNetwork.DI_MODULE).d(
    "🔧 [组件创建] UnifiedAiResponseService"
)
```

## 🔍 日志过滤和分析

### 按模块过滤
```bash
# 查看所有core-network日志
adb logcat | grep "CNET-"

# 查看特定子模块日志
adb logcat | grep "CNET-SERVICE-"
adb logcat | grep "CNET-PROCESSOR-"
adb logcat | grep "CNET-OUTPUT-"
```

### 按功能过滤
```bash
# 查看统计信息
adb logcat | grep "📊 \[.*统计\]"

# 查看请求生命周期
adb logcat | grep "🚀 \[请求开始\]\|✅ \[请求完成\]\|❌ \[请求失败\]"

# 查看性能指标
adb logcat | grep "耗时=.*ms"
```

## 📋 实施检查清单

### 开发时检查
- [ ] 使用正确的日志标签格式
- [ ] 记录汇总信息而非详细内容
- [ ] 避免记录敏感数据
- [ ] 使用适当的日志级别
- [ ] 包含关键性能指标

### 代码审查检查
- [ ] 无token级别的详细日志
- [ ] 无敏感数据泄露
- [ ] 日志标签符合规范
- [ ] 日志内容有助于问题诊断
- [ ] 性能影响最小化

### 生产环境监控
- [ ] 关键指标可追踪
- [ ] 错误率可监控
- [ ] 性能趋势可分析
- [ ] 问题可快速定位
