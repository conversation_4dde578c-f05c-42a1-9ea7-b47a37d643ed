package com.example.gymbro.features.thinkingbox.domain.parser

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * FunctionCallDetector - 流式Function Call检测器
 *
 * 🎯 设计目标：
 * - 在token流中实时检测function call
 * - 支持跨chunk的JSON解析
 * - 兼容现有的function call格式
 * - 遵循MVI 2.0架构规范
 *
 * 🔧 检测策略：
 * - 累积式缓冲：收集文本直到检测到完整的function call
 * - 多模式匹配：支持多种function call格式
 * - 状态机处理：跟踪JSON解析状态
 */
@Singleton
class FunctionCallDetector @Inject constructor() {

    private val TAG = "TB-FC-DETECTOR"
    private val json = Json { ignoreUnknownKeys = true }

    /**
     * Function Call检测状态 - 公开给DetectionContext使用
     */
    enum class DetectionState {
        IDLE, // 空闲状态，等待检测
        COLLECTING, // 收集JSON内容中
        COMPLETE, // 检测完成
    }

    /**
     * 检测上下文 - 公开给StreamingThinkingMLParser使用
     */
    data class DetectionContext(
        var state: DetectionState = DetectionState.IDLE,
        val buffer: StringBuilder = StringBuilder(),
        var braceCount: Int = 0,
        var startIndex: Int = -1,
        var functionName: String? = null,
    )

    /**
     * 在文本chunk中检测function call
     *
     * @param textChunk 输入的文本块
     * @param context 检测上下文（保持状态）
     * @param messageId 消息ID（用于日志）
     * @return 检测到的SemanticEvent列表
     */
    fun detectInChunk(
        textChunk: String,
        context: DetectionContext,
        messageId: String,
    ): List<SemanticEvent> {
        val events = mutableListOf<SemanticEvent>()

        // 将新的chunk添加到缓冲区
        context.buffer.append(textChunk)
        val currentContent = context.buffer.toString()

        Timber.v("TB-Parser: [$messageId] Processing chunk: ${textChunk.take(50)}...")
        Timber.v("TB-Parser: [$messageId] Buffer content: ${currentContent.take(100)}...")

        when (context.state) {
            DetectionState.IDLE -> {
                // 查找function call开始标记
                val functionCallStart = findFunctionCallStart(currentContent)
                if (functionCallStart != null) {
                    context.state = DetectionState.COLLECTING
                    context.startIndex = functionCallStart.startIndex
                    context.functionName = functionCallStart.functionName
                    context.braceCount = 0

                    Timber.d(
                        "TB-Parser: [$messageId] Function call detection started: ${context.functionName}",
                    )

                    // 继续处理收集逻辑
                    events.addAll(processCollecting(context, messageId))
                }
            }

            DetectionState.COLLECTING -> {
                events.addAll(processCollecting(context, messageId))
            }

            DetectionState.COMPLETE -> {
                // 已完成，重置状态
                resetContext(context)
            }
        }

        return events
    }

    /**
     * 处理收集状态
     */
    private fun processCollecting(
        context: DetectionContext,
        messageId: String,
    ): List<SemanticEvent> {
        val events = mutableListOf<SemanticEvent>()
        val content = context.buffer.toString()

        if (context.startIndex == -1) return events

        // 从startIndex开始分析JSON结构
        val jsonCandidate = content.substring(context.startIndex)
        val completeFunctionCall = extractCompleteFunctionCall(jsonCandidate)

        if (completeFunctionCall != null) {
            // 检测到完整的function call
            val functionCallEvent = parseFunctionCall(completeFunctionCall, messageId)
            if (functionCallEvent != null) {
                events.add(functionCallEvent)
                Timber.i("TB-Parser: [$messageId] Function call detected: ${functionCallEvent.functionName}")
            }

            // 重置上下文
            context.state = DetectionState.COMPLETE
        }

        return events
    }

    /**
     * 查找function call开始位置
     */
    private fun findFunctionCallStart(content: String): FunctionCallStart? {
        // 支持的function call格式模式
        val patterns = listOf(
            // 模式1: gymbro.template.generate({...})
            """(gymbro\.\w+\.\w+)\s*\(\s*\{""".toRegex(),
            // 模式2: {"name": "gymbro.template.generate", ...}
            """\{\s*"name"\s*:\s*"(gymbro\.\w+\.\w+)"""".toRegex(),
            // 模式3: ```json\n{"name": "gymbro..."}
            """```json\s*\{\s*"name"\s*:\s*"(gymbro\.\w+\.\w+)"""".toRegex(),
        )

        for (pattern in patterns) {
            val match = pattern.find(content)
            if (match != null) {
                val functionName = match.groupValues[1]
                val startIndex = when {
                    pattern.pattern.contains("```json") -> content.indexOf("{", match.range.first)
                    pattern.pattern.contains("gymbro.*\\(") -> content.indexOf("{", match.range.first)
                    else -> match.range.first
                }

                if (startIndex != -1) {
                    return FunctionCallStart(functionName, startIndex)
                }
            }
        }

        return null
    }

    /**
     * 提取完整的function call JSON
     */
    private fun extractCompleteFunctionCall(jsonCandidate: String): String? {
        var braceCount = 0
        var inString = false
        var escaped = false
        var startIndex = -1

        for (i in jsonCandidate.indices) {
            val char = jsonCandidate[i]

            when {
                escaped -> {
                    escaped = false
                }
                char == '\\' && inString -> {
                    escaped = true
                }
                char == '"' -> {
                    inString = !inString
                }
                !inString -> {
                    when (char) {
                        '{' -> {
                            if (braceCount == 0) startIndex = i
                            braceCount++
                        }
                        '}' -> {
                            braceCount--
                            if (braceCount == 0 && startIndex != -1) {
                                // 找到完整的JSON
                                return jsonCandidate.substring(startIndex, i + 1)
                            }
                        }
                    }
                }
            }
        }

        return null
    }

    /**
     * 解析function call JSON并创建事件
     */
    private fun parseFunctionCall(
        jsonString: String,
        messageId: String,
    ): SemanticEvent.FunctionCallDetected? {
        return try {
            val jsonElement = json.parseToJsonElement(jsonString)
            val jsonObject = jsonElement.jsonObject

            // 尝试不同的解析策略
            val functionName = jsonObject["name"]?.jsonPrimitive?.content
            val arguments = jsonObject["arguments"]?.toString()

            if (functionName != null && functionName.startsWith("gymbro.")) {
                SemanticEvent.FunctionCallDetected(
                    functionName = functionName,
                    arguments = arguments,
                    isComplete = true,
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Timber.w(e, "TB-Parser: [$messageId] Failed to parse function call JSON: $jsonString")
            null
        }
    }

    /**
     * 重置检测上下文
     */
    private fun resetContext(context: DetectionContext) {
        context.state = DetectionState.IDLE
        context.buffer.clear()
        context.braceCount = 0
        context.startIndex = -1
        context.functionName = null
    }

    /**
     * 创建新的检测上下文
     */
    fun createContext(): DetectionContext = DetectionContext()

    /**
     * Function Call开始位置数据类
     */
    private data class FunctionCallStart(
        val functionName: String,
        val startIndex: Int,
    )
}
