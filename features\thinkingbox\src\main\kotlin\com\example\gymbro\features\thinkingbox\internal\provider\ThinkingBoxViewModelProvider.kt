package com.example.gymbro.features.thinkingbox.internal.provider

import com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel.ThinkingBoxViewModel
import kotlinx.coroutines.flow.StateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingBoxViewModel提供器
 *
 * 用于在非Compose环境中访问ThinkingBoxViewModel实例。
 * 解决了ThinkingBoxDisplayImpl需要监听ViewModel状态的问题。
 *
 * @since Coach-ThinkingBox重构
 */
@Singleton
class ThinkingBoxViewModelProvider @Inject constructor() {

    // 存储活跃的ViewModel实例
    private val activeViewModels = mutableMapOf<String, ThinkingBoxViewModel>()

    /**
     * 注册ViewModel实例
     *
     * @param messageId 消息ID
     * @param viewModel ViewModel实例
     */
    fun registerViewModel(messageId: String, viewModel: ThinkingBoxViewModel) {
        Timber.d(
            "TB-Provider:TB-Provider: 🔗 [ThinkingBoxViewModelProvider] 注册ViewModel: messageId=$messageId",
        )
        activeViewModels[messageId] = viewModel
    }

    /**
     * 获取ViewModel实例
     *
     * @param messageId 消息ID
     * @return ViewModel实例，如果不存在则返回null
     */
    fun getViewModel(messageId: String): ThinkingBoxViewModel? {
        return activeViewModels[messageId]
    }

    /**
     * 获取ViewModel的状态流
     *
     * @param messageId 消息ID
     * @return 状态流，如果ViewModel不存在则返回null
     */
    fun getStateFlow(
        messageId: String,
    ): StateFlow<com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract.State>? {
        return activeViewModels[messageId]?.state
    }

    /**
     * 注销ViewModel实例
     *
     * @param messageId 消息ID
     */
    fun unregisterViewModel(messageId: String) {
        Timber.d("TB-Provider:🗑️ [ThinkingBoxViewModelProvider] 注销ViewModel: messageId=$messageId")
        activeViewModels.remove(messageId)
    }

    /**
     * 清理所有ViewModel实例
     */
    fun clearAll() {
        Timber.d("TB-Provider:🧹 [ThinkingBoxViewModelProvider] 清理所有ViewModel")
        activeViewModels.clear()
    }

    /**
     * 获取活跃的ViewModel数量
     */
    fun getActiveCount(): Int = activeViewModels.size
}
