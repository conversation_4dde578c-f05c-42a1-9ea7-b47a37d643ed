# Template文件详细问题扫描报告

## 概述
本报告对Template模块的关键文件进行了深度扫描，重点关注拖拽实现、代码结构、TODO/FIXME问题和硬编码。

## 文件1: TemplateScreen.kt
### 旧拖拽实现问题
- 位置：行442-486, 579-623
- 问题：存在多个独立的拖拽实现，缺乏统一标准
- 风险：高，可能导致不一致的用户体验
- 替换建议：
  1. 迁移到统一的 `UnifiedDragHandler`
  2. 移除重复的 `detectDragGestures` 调用
  3. 使用 `TemplateScreenDragHandler.createDragModifier`

### 状态管理问题
- 多处 `isDragInProgress` 状态管理
- 不同的拖拽状态跟踪方式
- 建议统一使用 `DragState<T>` 实现

### TODO/FIXME问题
- 无直接的TODO/FIXME标记
- 存在大量调试日志和注释性日志

### 硬编码问题
- `.dp` 使用：
  1. `cardHeight = 80.dp.toPx()`
  2. 建议替换为 `Tokens.Size.CardHeight`
- 颜色硬编码：使用 `MaterialTheme.workoutColors`，符合规范

## 文件2: TemplateEditComponents.kt
### 旧拖拽实现问题
- 位置：行1340-1366
- 问题：重复的拖拽逻辑实现
- 风险：中等，存在代码重复
- 替换建议：
  1. 提取通用的拖拽处理逻辑
  2. 使用统一的 `DragHandler`

### 状态管理问题
- 存在多个拖拽相关状态变量
- 缺乏统一的状态管理机制
- 建议：使用 `DragState<T>` 重构

### TODO/FIXME问题
- 无直接的TODO/FIXME
- 代码注释完善，描述清晰

### 硬编码问题
- 使用 `Tokens` 替代硬编码，符合规范
- 动画和间距使用 `Tokens.Spacing`
- 颜色使用 `MaterialTheme.workoutColors`

## 文件3: TemplateContract.kt
### 拖拽状态问题
- 同时存在新旧拖拽状态字段
  - 新：`templateDragState`/`draftDragState`
  - 旧：`isDragging`/`draggedItemIndex`/`dragTargetIndex`
- 建议：完全迁移到新的 `DragState<T>`，移除旧字段

### 状态管理冗余
- 8处 `isDragInProgress`
- 4处 `dragOffset`
- 建议：精简状态管理，使用统一的 `DragState`

### TODO/FIXME问题
- 无直接的TODO/FIXME
- 代码注释详细，说明充分

### 硬编码问题
- 使用枚举和常量替代硬编码值
- 状态转换规则清晰

## 文件4: TemplateEditContract.kt
### 拖拽状态问题
- 复杂的拖拽状态字段：
  ```kotlin
  val isDragging: Boolean = false
  val draggedItemId: String? = null
  val draggedItemIndex: Int = -1
  val dropTargetIndex: Int = -1
  val dragOffset: Float = 0f
  val draggedExerciseId: String? = null
  val dragTargetIndex: Int = -1
  val isDragInProgress: Boolean = false
  ```
- 建议：使用单一 `DragState<T>` 替代

### 状态转换复杂性
- 多个状态转换方法
- 建议：简化状态转换逻辑
- 关键方法：
  - `createDragState()`
  - `updateFromDragState()`

### TODO/FIXME问题
- 无直接的TODO/FIXME
- 代码注释详细，说明充分

### 硬编码问题
- 使用常量替代硬编码
- 例如：`Constants.MAX_TEMPLATE_NAME_LENGTH`

## 立即处理项目
1. 统一拖拽组件实现
2. 移除重复的拖拽状态字段
3. 迁移到 `DragState<T>`
4. 清理旧的拖拽实现代码
5. 移除向后兼容的冗余字段

## 重要TODO实施
1. 重构拖拽状态管理
2. 统一拖拽组件接口
3. 移除重复代码
4. 优化性能和代码可读性

## 硬编码修复清单
1. 替换 `.dp` 硬编码为 `Tokens`
2. 使用 `Tokens.Size` 定义尺寸
3. 保持使用 `MaterialTheme.workoutColors`

## 总结建议
- 拖拽实现高度重复且不一致
- 状态管理复杂，存在冗余
- 需要全面重构拖拽组件
- 保持现有的设计系统和主题令牌规范
