package com.example.gymbro.core.network.service

import com.example.gymbro.core.network.config.NetworkConfigManager
import com.example.gymbro.core.network.detector.ContentType
import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.core.network.processor.StreamingProcessor
import com.example.gymbro.core.network.rest.ApiResult
import com.example.gymbro.core.network.rest.RestClient
import com.example.gymbro.shared.models.ai.ChatRequest
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * 🔥 【架构验证】UnifiedAiResponseService测试
 * 
 * 验证统一AI响应服务的核心功能：
 * - SSE响应解析
 * - 文本完整性保持
 * - DirectOutputChannel集成
 * - 错误处理
 */
class UnifiedAiResponseServiceTest {

    private val mockRestClient = mockk<RestClient>()
    private val mockNetworkConfigManager = mockk<NetworkConfigManager>()
    private val mockDirectOutputChannel = mockk<DirectOutputChannel>(relaxed = true)
    private val mockStreamingProcessor = mockk<StreamingProcessor>()

    private val service = UnifiedAiResponseService(
        restClient = mockRestClient,
        networkConfigManager = mockNetworkConfigManager,
        directOutputChannel = mockDirectOutputChannel,
        streamingProcessor = mockStreamingProcessor
    )

    @Test
    fun `processAiStreamingResponse should handle complete text flow correctly`() = runTest {
        // Given: Mock SSE response with complete text
        val sseResponse = """
            data: {"choices":[{"delta":{"content":"Hello"}}]}
            data: {"choices":[{"delta":{"content":" World"}}]}
            data: {"choices":[{"delta":{"content":"!"}}]}
            data: [DONE]
        """.trimIndent()

        val request = ChatRequest(
            messages = emptyList(),
            model = "gpt-3.5-turbo",
            stream = true
        )

        // Mock network config
        every { mockNetworkConfigManager.getCurrentConfig() } returns mockk {
            every { restBase } returns "https://api.openai.com"
            every { apiKey } returns "test-key"
        }

        // Mock REST client response
        coEvery { 
            mockRestClient.post(any(), any(), any()) 
        } returns ApiResult.Success(sseResponse)

        // Mock streaming processor to return processed tokens
        every { mockStreamingProcessor.extractJsonSseContent(any()) } returnsMany listOf(
            "Hello",
            " World", 
            "!",
            ""
        )

        // When: Process the streaming response
        val result = service.processAiStreamingResponse(request, "test-message-id").toList()

        // Then: Verify complete text is preserved
        assertEquals(3, result.size)
        assertEquals("Hello", result[0])
        assertEquals(" World", result[1])
        assertEquals("!", result[2])

        // Verify DirectOutputChannel receives all tokens
        verify(exactly = 3) { 
            mockDirectOutputChannel.sendToken(
                token = any(),
                conversationId = "test-message-id",
                contentType = ContentType.JSON_SSE,
                metadata = any()
            )
        }
    }

    @Test
    fun `processAiStreamingResponse should handle empty tokens correctly`() = runTest {
        // Given: SSE response with empty content
        val sseResponse = """
            data: {"choices":[{"delta":{}}]}
            data: [DONE]
        """.trimIndent()

        val request = ChatRequest(
            messages = emptyList(),
            model = "gpt-3.5-turbo",
            stream = true
        )

        every { mockNetworkConfigManager.getCurrentConfig() } returns mockk {
            every { restBase } returns "https://api.openai.com"
            every { apiKey } returns "test-key"
        }

        coEvery { 
            mockRestClient.post(any(), any(), any()) 
        } returns ApiResult.Success(sseResponse)

        // Mock streaming processor to return empty token
        every { mockStreamingProcessor.extractJsonSseContent(any()) } returns ""

        // When: Process the streaming response
        val result = service.processAiStreamingResponse(request, "test-message-id").toList()

        // Then: Verify no tokens are emitted for empty content
        assertTrue(result.isEmpty())

        // Verify DirectOutputChannel doesn't receive empty tokens
        verify(exactly = 0) { 
            mockDirectOutputChannel.sendToken(any(), any(), any(), any())
        }
    }

    @Test
    fun `processAiStreamingResponse should handle API errors correctly`() = runTest {
        // Given: API error response
        val request = ChatRequest(
            messages = emptyList(),
            model = "gpt-3.5-turbo",
            stream = true
        )

        every { mockNetworkConfigManager.getCurrentConfig() } returns mockk {
            every { restBase } returns "https://api.openai.com"
            every { apiKey } returns "test-key"
        }

        coEvery { 
            mockRestClient.post(any(), any(), any()) 
        } returns ApiResult.Error("API Error: Rate limit exceeded")

        // When & Then: Verify exception is thrown
        try {
            service.processAiStreamingResponse(request, "test-message-id").toList()
            assert(false) { "Expected exception was not thrown" }
        } catch (e: Exception) {
            assertTrue(e.message?.contains("API Error") == true)
        }

        // Verify error token is sent to DirectOutputChannel
        verify { 
            mockDirectOutputChannel.sendToken(
                token = match { it.contains("AI响应处理失败") },
                conversationId = "test-message-id",
                contentType = ContentType.PLAIN_TEXT,
                metadata = match { it["error"] == true }
            )
        }
    }
}
